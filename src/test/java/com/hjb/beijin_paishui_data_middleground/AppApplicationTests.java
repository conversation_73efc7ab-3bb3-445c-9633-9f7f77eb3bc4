//package com.hjb.beijin_paishui_data_middleground;
//
//import com.google.gson.Gson;
//import com.google.gson.reflect.TypeToken;
//import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
//import com.hjb.beijin_paishui_data_middleground.common.utils.SpringContextUtils;
//import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.flood.FloodWaterPlantEditDto;
//import com.hjb.beijin_paishui_data_middleground.entity.szls.vo.WaterPlantResponseData;
//import org.junit.jupiter.api.Test;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.context.ApplicationContext;
//import org.springframework.context.expression.BeanFactoryResolver;
//import org.springframework.expression.ExpressionParser;
//import org.springframework.expression.spel.standard.SpelExpressionParser;
//import org.springframework.expression.spel.support.StandardEvaluationContext;
//
//import javax.annotation.Resource;
//import java.lang.reflect.Type;
//import java.time.LocalDate;
//import java.time.LocalDateTime;
//import java.time.LocalTime;
//import java.util.*;
//
//@SpringBootTest
//class AppApplicationTests {
//
////    @Test
////    void testSpel() throws ClassNotFoundException {
////        ExpressionParser parser = new SpelExpressionParser();
////        StandardEvaluationContext context = new StandardEvaluationContext();
////        context.setBeanResolver(new BeanFactoryResolver(applicationContext));
////
////        // 获取bean对象
//////        DemoServiceImpl demoServiceImpl = parser.parseExpression("@demoService").getValue(context, DemoServiceImpl.class);
//////        System.out.println("bean: " + demoServiceImpl);
////
////        // 访问bean方法
////        Boolean ans = parser.parseExpression("hasRole('admin')").getValue(context, Boolean.class);
////        System.out.println("bean method return: " + ans);
////    }
//
////    @Test
////    void testSpringBeanName() {
////        Gson gson = new Gson();
////        Type type = new TypeToken<WaterPlantResponseData>() {
////        }.getType();
////        String jsonResponse = "{\"code\":200,\"message\":\"success\",\"data\":{\"QH0829220220919\":\"对应值\"}}";
////        WaterPlantResponseData response = gson.fromJson(jsonResponse, type);
////        List<FloodWaterPlantEditDto.DetailData> list = new ArrayList<>();
////        // 定义水厂信息映射表，根据你的数据结构填充
////        Map<String, Map<String, String>> waterPlantMap = new HashMap<>();
////        Map<String, String> map1 = new HashMap<>();
////        map1.put("FrontLevel", "QH0827220220919");
////        map1.put("InstantaneousLift", "QH0829220220919");
////        map1.put("WhetherToCross", "QH0020120220919");
////        waterPlantMap.put("清河40", map1);
////
////        // 将后面的都改为相同的map类型
////        Map<String, String> map2 = new HashMap<>();
////        map2.put("FrontLevel", "QH0001020220919");
////        map2.put("InstantaneousLift", "QH0002420220919");
////        map2.put("WhetherToCross", "QH0020120220919");
////        waterPlantMap.put("清河15", map2);
////
////        Map<String, String> map3 = new HashMap<>();
////        map3.put("FrontLevel", "QHE0000520220823");
////        map3.put("InstantaneousLift", "QHE0008020220823");
////        map3.put("WhetherToCross", "QHE0000920230601");
////        waterPlantMap.put("清河二", map3);
////
////        Map<String, String> map4 = new HashMap<>();
////        map4.put("FrontLevel", "BXH0000120220606");
////        map4.put("InstantaneousLift", "BXH0002220220606");
////        map4.put("WhetherToCross", "无跨越");
////        waterPlantMap.put("北小河", map4);
////
////        Map<String, String> map5 = new HashMap<>();
////        map5.put("FrontLevel", "JXQ0007620220515");
////        map5.put("InstantaneousLift", "JXQ0007420220515");
////        map5.put("WhetherToCross", "TXQ0009120220515");
////        waterPlantMap.put("酒仙桥", map5);
////
////        Map<String, String> map6 = new HashMap<>();
////        map6.put("FrontLevel", "GAT0000320220517");
////        map6.put("InstantaneousLift", "GAT0006120220517");
////        map6.put("WhetherToCross", "GAT0000720230530");
////        waterPlantMap.put("高安屯", map6);
////
////        Map<String, String> map7 = new HashMap<>();
////        map7.put("FrontLevel", "GBD0116620220506");
////        map7.put("InstantaneousLift", "GBD1918220220608");
////        map7.put("WhetherToCross", "GBD0001120230531");
////        waterPlantMap.put("高碑店", map7);
////
////        Map<String, String> map8 = new HashMap<>();
////        map8.put("FrontLevel", "DFZ0046420220525");
////        map8.put("InstantaneousLift", "DFZ0000220220728");
////        map8.put("WhetherToCross", "无跨越");
////        waterPlantMap.put("定福庄", map8);
////
////        Map<String, String> map9 = new HashMap<>();
////        map9.put("FrontLevel", "XH1503620230313");
////        map9.put("InstantaneousLift", "XH1507520230313");
////        map9.put("WhetherToCross", "XHM0000520230601");
////        waterPlantMap.put("小红门", map9);
////
////        Map<String, String> map10 = new HashMap<>();
////        map10.put("FrontLevel", "HF0008820230313");
////        map10.put("InstantaneousLift", "H0013420230313");
////        map10.put("WhetherToCross", "无跨越");
////        waterPlantMap.put("槐房", map10);
////
////        Map<String, String> map11 = new HashMap<>();
////        map11.put("FrontLevel", "WJC00023202211013");
////        map11.put("InstantaneousLift", "WJC01585202211013");
////        map11.put("WhetherToCross", "无跨越");
////        waterPlantMap.put("吴家村", map11);
////
////        Map<String, String> map12 = new HashMap<>();
////        map12.put("FrontLevel", "LGQ00540202211020");
////        map12.put("InstantaneousLift", "LGQ00024202211020");
////        map12.put("WhetherToCross", "无监测");
////        waterPlantMap.put("卢沟桥", map12);
////        // 遍历响应数据，填充到对应的水厂信息中
////        for (Map.Entry<String, Map<String, String>> stringMapEntry : waterPlantMap.entrySet()) {
////            FloodWaterPlantEditDto.DetailData tableData = new FloodWaterPlantEditDto.DetailData();
////            tableData.setWaterPlantName(stringMapEntry.getKey());
////            Map<String, String> stringMapEntryValue = stringMapEntry.getValue();
////            for (Map.Entry<String, String> entry : response.getData().entrySet()) {
////                String key = entry.getKey();
////                String value = entry.getValue();
////                if (stringMapEntryValue.get("FrontLevel").equals(key))
////                    tableData.setFrontLevel(value);
////                if (stringMapEntryValue.get("InstantaneousLift").equals(key))
////                    tableData.setInstantaneousLift(value);
////                if (stringMapEntryValue.get("WhetherToCross").equals(key))
////                    tableData.setWhetherToCross(value);
////                else
////                    tableData.setWhetherToCross(stringMapEntryValue.get("WhetherToCross"));
////            }
////            list.add(tableData);
////        }
////        System.out.println(list);
////    }
//    @Test
//    public void  test5(){
//
////        // 获取当前时间
////        LocalDateTime now = LocalDateTime.now();
////        // 计算今年年初的时间
////        LocalDateTime startOfThisYear = LocalDateTime.of(LocalDate.of(now.getYear(), 1, 1), LocalTime.MIN);
////        // 计算去年年初的时间
////        LocalDateTime startOfLastYear = LocalDateTime.of(LocalDate.of(now.getYear() - 1, 1, 1), LocalTime.MIN);
////        // 计算去年年末的时间
////        LocalDateTime endOfLastYear = LocalDateTime.of(LocalDate.of(now.getYear() - 1, 12, 31), LocalTime.MAX);
////        System.out.println("今年年初的时间: " + startOfThisYear);
////        System.out.println("去年年初的时间: " + startOfLastYear);
////        System.out.println("去年年末的时间: " + endOfLastYear);
//    }
//
//}
