# 服务名称
spring:
  application:
    name: beijin-paishui-data-middle
  # 数据库配置
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          url: *****************************************
          driver-class-name: org.postgresql.Driver
          username: postgres
          password: 123456
#        master:
#          url: *****************************************
#          driver-class-name: org.postgresql.Driver
#          username: dtwin
#          password: SHUZI5luansheng1#
#        paishui:
#          url: ****************************************
#          driver-class-name: org.postgresql.Driver
#          username: drain_3d
#          password: drain_3d@12345#QWERT
#        fangxun:
#          url: *****************************************
#          driver-class-name: oracle.jdbc.driver.OracleDriver
#          username: fxyj_share
#          password: Qshare2024
#        guanwang:
#          url: ****************************************
#          driver-class-name: org.postgresql.Driver
#          username: yyguest
#          password: YyGuest#pipe#8889
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER
#   redis 配置
#  redis:
#    port: 6379
#    host: localhost
#    database: 0
#    password: root
server:
  port: 8808
  servlet:
    context-path: /api
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true     # 驼峰命名转换
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: isDelete # 全局逻辑删除的实体字段名(since 3.3.0,配置后可以忽略不配置步骤2)
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
knife4j:
  enable: true
