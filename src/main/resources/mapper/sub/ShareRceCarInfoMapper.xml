<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hjb.beijin_paishui_data_middleground.mapper.sub.ShareRceCarInfoMapper">
    <select id="getVehicleCallSignInformation"
            resultType="com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.CarAndUnitVo">
        select sci.dev_name as carType,srui.name, srui.call_no as callNo, sci.car_no as carNo, srui.comnm, sci.telephone
        from FXYJ.SHARE_RCE_CAR_INFO srci
        join FXYJ.SHARE_CAR_INFO sci on srci.car_no = sci.car_no
        join FXYJ.SHARE_RCE_UNIT_INFO srui on srci.ru_code = srui.ru_code
        <where>
            srci.is_first = 1
            <if test="carNo != null and carNo != ''">
                and sci.car_no = #{carNo}
            </if>
        </where>
    </select>
</mapper>
