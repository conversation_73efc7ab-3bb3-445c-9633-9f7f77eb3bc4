<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hjb.beijin_paishui_data_middleground.mapper.sub.V3dDrainOrderMapper">
    <select id="countEventTypesByYearRange"
            resultType="com.hjb.beijin_paishui_data_middleground.entity.sub.dto.EventTypeCountDTO">
        SELECT classify_one,
               classify_one_name,
               SUM(CASE
                       WHEN register_time &gt;= #{oneYearAgo} AND register_time &lt;= #{now} THEN 1
                       ELSE 0 END) AS current_year_count,
               SUM(CASE
                       WHEN register_time &gt;= #{twoYearsAgo} AND register_time &lt;= #{oneYearAgo} THEN 1
                       ELSE 0 END) AS last_year_count
        FROM v_3d_drain_order
        WHERE bol_ownership = 1
          AND classify_one IS NOT NULL
          AND register_time BETWEEN #{twoYearsAgo} AND #{now}
        GROUP BY classify_one, classify_one_name
    </select>
    <select id="getFacilityAppealEventYearOrders"
            resultType="com.hjb.beijin_paishui_data_middleground.entity.sub.po.ps.V3dDrainOrder">
        SELECT classify_one, classify_one_name, classify_two, classify_two_name
        FROM v_3d_drain_order
        WHERE register_time &gt;= #{oneYearAgo}
          AND register_time &lt;= #{now}
          AND classify_one = '001'
          AND classify_two IS NOT NULL
          AND classify_two != ''
      AND classify_two_name IS NOT NULL AND classify_two_name != ''
    </select>
</mapper>
