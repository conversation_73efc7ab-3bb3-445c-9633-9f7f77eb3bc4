<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hjb.beijin_paishui_data_middleground.mapper.sub.PipeNetworkTestingMapper">
    <select id="getPipeNetworkTesting" resultType="com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainPipeNetworkTesting">
        WITH calculations AS (SELECT SUM(cwr.work_length)                                          AS total_work_length,
                                     SUM(cwr.bh_length)                                            AS total_bh_length,
                                     SUM(CASE WHEN gp.syxz = 'ys' THEN cwr.work_length ELSE 0 END) AS rain_detection_length,
                                     SUM(CASE WHEN gp.syxz = 'ws' THEN cwr.work_length ELSE 0 END) AS sewage_detection_length
                              FROM yy.cctv c
                                       JOIN yy.cctv_work_record cwr ON c.cctv_work_id = cwr.uuid
                                       LEFT JOIN gw.gw_pipe gp ON c.cctv_gddm = gp.code)
        SELECT CASE
                   WHEN total_work_length > 0 THEN ROUND(
                           ((total_work_length - total_bh_length) * 100.0 / total_work_length), 2)::TEXT
                   ELSE '0'
                   END AS pipeNetworkGoodRate,
               ROUND((total_work_length / 1000.0), 2)::TEXT AS actualDetectionLength, ROUND((rain_detection_length / 1000.0), 2)::TEXT AS rainDetectionLength, ROUND((sewage_detection_length / 1000.0), 2) ::TEXT AS sewageDetectionLength
        FROM calculations;
    </select>

    <!-- 单独查询图表数据 -->
    <select id="getPipeNetworkTestingChart" resultType="java.util.Map">
        SELECT
            cjgq.name AS xValue,
            CAST(ROUND(SUM(cwr.work_length / 1000.0)) AS INTEGER) AS seriesValue
        FROM yy.cctv c
                 JOIN yy.cctv_work_record cwr ON c.cctv_work_id = cwr.uuid
                 JOIN yy.cctv_jgqx cjgq ON array_position(string_to_array(c.cctv_jgqx, ','), cjgq.code::text) IS NOT NULL
        GROUP BY cjgq.name;
    </select>

</mapper>
