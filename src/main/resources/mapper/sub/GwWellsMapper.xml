<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hjb.beijin_paishui_data_middleground.mapper.sub.GwWellsMapper">
    <select id="getListByGwGcTzCode"
            resultType="com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical.SyntheticalPipelineOperation">
        SELECT
            SUM(CASE WHEN gw.gzw = 'jcj' THEN 1 ELSE 0 END) AS manholeTotal,
            SUM(CASE WHEN gw.gzw = 'ysk' THEN 1 ELSE 0 END) AS rainwaterGrateTotal,
            SUM(CASE WHEN gw.gzw = 'phk' THEN 1 ELSE 0 END) AS riverOutlet
        FROM gw.gw_well gw
                 JOIN gw.gw_gc_tz gctz ON gw.gctz = gctz.code
        WHERE gctz.sfqs = 'y'
          AND gw.syzt = 'using'
    </select>
    <select id="getListByGwGcTzCodeSeachSyxzCount"
            resultType="com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainAncillaryFacilities">
        SELECT
            ROUND(COUNT(1) / 10000.0, 2) AS manholeCover,
            ROUND(SUM(CASE WHEN gw.syxz = 'ys' THEN 1 ELSE 0 END) / 10000.0, 2) AS rainwater,
            ROUND(SUM(CASE WHEN gw.syxz = 'ws' THEN 1 ELSE 0 END) / 10000.0, 2) AS sewage,
            ROUND(SUM(CASE WHEN gw.syxz = 'hl' THEN 1 ELSE 0 END) / 10000.0, 2) AS combinedFlow,
            ROUND(SUM(CASE WHEN gw.gzw = 'ysk' THEN 1 ELSE 0 END) / 10000.0, 2) AS rainwaterInlet,
            ROUND(SUM(gw.bzs) / 10000.0, 2) AS grate
        FROM
            gw.gw_well gw
                JOIN
            gw.gw_gc_tz gctz ON gw.gctz = gctz.code
        WHERE
            gctz.sfqs = 'y'
          AND gw.syzt = 'using';
    </select>
    <select id="getListByGwGcTzCodeS"
            resultType="com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw.GwWells">
        SELECT gw.*
        FROM gw.gw.well gw
                 JOIN gw.gw_gc_tz gctz ON gw.gctz = gctz.code
        WHERE gctz.sfqs = 'y'
          AND gw.syzt = 'using'
    </select>
    <select id="getByCode" resultType="com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.GwWellAndGzTzAndGwYyTzInfo">
        SELECT gwl.code,
               gwl.gctz,
               gwl.syxz,
               gwl.gzw,
               gwl.jmgc,
               gwl.jdgc,
               gwl.dem_dmg as demDmg,
               gwl.jgcz,
               gwl.old_code as oldCode,
               gwl.syxz,
               gwl.bz,
               gwl.editor,
               gwl.edit_time as editTime,
               road2.name as ssld,
               road.name as szdl,
               banzu.name as banzuName,
               dep.name as deptName,
               tz.sfqs,
               tz.xmbh,
               tz.yjly,
               tz.name as tzName,
               yz.name as gqName
        FROM gw.gw_well gwl
                 JOIN gw.a_banzu banzu on gwl.banzu = banzu.code
                 JOIN gw.a_dept dep on banzu.dept = dep.code
                 JOIN gw.gw_gc_tz tz on gwl.gctz = tz.code
                 LEFT JOIN gw.gw_yy_tz yz on gwl.yytz = yz.code
                 LEFT JOIN gw.gw_road road on gwl.road = road.code
                 LEFT JOIN gw.gw_road2 road2 on gwl.road2 = road2.code
        where gwl.code = #{code}
    </select>
    <select id="seachGwWellPoint"
            resultType="com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.GwWellAndGzTzAndGwYyTzInfo">
        SELECT gwl.code,
        gwl.gctz,
        gwl.syxz,
        gwl.gzw,
        gwl.jmgc,
        gwl.jdgc,
        gwl.dem_dmg as demDmg,
        gwl.jgcz,
        gwl.old_code as oldCode,
        gwl.syxz,
        gwl.bz,
        gwl.editor,
        gwl.edit_time as editTime,
        road2.name as ssld,
        road.name as szdl,
        banzu.name as banzuName,
        dep.name as deptName,
        tz.sfqs,
        tz.xmbh,
        tz.yjly,
        tz.name as tzName,
        yz.name as gqName
        FROM gw.gw_well gwl
        JOIN gw.a_banzu banzu on gwl.banzu = banzu.code
        JOIN gw.a_dept dep on banzu.dept = dep.code
        JOIN gw.gw_gc_tz tz on gwl.gctz = tz.code
        LEFT JOIN gw.gw_yy_tz yz on gwl.yytz = yz.code
        LEFT JOIN gw.gw_road road on gwl.road = road.code
        LEFT JOIN gw.gw_road2 road2 on gwl.road2 = road2.code
        <where>
            <if test="dept != null and dept != ''">
                and gwl.dept = #{dept}
            </if>
            <if test="gctzCode != null and gctzCode != ''">
                and tz.code = #{gctzCode,jdbcType=BIGINT}::bigint
            </if>
            <if test="gctzName != null and gctzName != ''">
                and tz.name = #{gctzName}
            </if>
            <if test="yyTzCode != null and yyTzCode != ''">
                and yz.code = #{yyTzCode}
            </if>
            <if test="yyTzName != null and yyTzName != ''">
                and yz.name = #{yyTzName}
            </if>
            <if test="code != null and code != ''">
                and gwl.code = #{code}
            </if>
            <if test="sfqs != null and sfqs != ''">
                and tz.sfqs = #{sfqs}
            </if>
            <if test="syxz != null and syxz != ''">
                and gwl.syxz = #{syxz}
            </if>
            <if test="syzt != null and syzt != ''">
                and gwl.syzt = #{syzt}
            </if>
            <if test="gzw != null and gzw != ''">
                and gwl.gzw = #{gzw}
            </if>
            <if test="gxlx != null and gxlx != ''">
                and gwl.gxlx = #{gxlx}
            </if>
        </where>
    </select>
</mapper>
