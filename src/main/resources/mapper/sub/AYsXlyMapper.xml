<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hjb.beijin_paishui_data_middleground.mapper.sub.interactive.AYsXlyMapper">
    <select id="getByCode" resultType="com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.AYsXlyVo">
        SELECT
            aysx.code,
            aysx.name,
            ab.name AS banzuName,
            ad.name AS deptName,
            aysx.dept,
            aysx.banzu,
            aysx.river
        FROM
            gw.a_ys_xly aysx
                LEFT JOIN
            gw.a_banzu ab ON aysx.banzu = ab.code
                LEFT JOIN
            gw.a_dept ad ON aysx.dept = ad.code
        <where>
            <if test="code != null">
                awsx.code = #{code}
            </if>
        </where>
    </select>
</mapper>
