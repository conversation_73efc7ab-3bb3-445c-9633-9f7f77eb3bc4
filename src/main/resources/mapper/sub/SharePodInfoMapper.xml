<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hjb.beijin_paishui_data_middleground.mapper.sub.SharePodInfoMapper">

    <select id="getListByOrderByMonthCount" resultType="java.util.Map">
        SELECT
        EXTRACT(MONTH FROM fdtime) AS month,
        COUNT(*) AS count
        FROM
        FXYJ.SHARE_POD_INFO
        WHERE
        status = 2
        AND fdtime &gt;= #{oneYearAgo}
        AND fdtime &lt;= #{now}
        GROUP BY
        EXTRACT(MONTH FROM fdtime)
        ORDER BY
        EXTRACT(MONTH FROM fdtime)
    </select>
</mapper>
