<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hjb.beijin_paishui_data_middleground.mapper.sub.interactive.ALjqMapper">
    <select id="getByCode" resultType="com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.ALjqVo">
        SELECT
            code,
            name,
            huanlu,
            fangxiang,
            leixing,
            chdw,
            weizhi,
            st_x(sgeom) as xCoordinate,
            st_y(sgeom) as yCoordinate
        FROM
            gw.a_ljq
        where code = #{code}
    </select>
</mapper>
