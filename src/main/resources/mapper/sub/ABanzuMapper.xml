<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hjb.beijin_paishui_data_middleground.mapper.sub.interactive.ABanzuMapper">
    <select id="getByCode" resultType="com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.ABanzuVo">
        select ba.code,
               ba.name,
               ba.ST_AsText(ba.sgeom) as sgeom,
               ad.name
                   as deptName
        from gw.a_banzu
            ba left JOIN  gw.a_dept ad on ba.dept = ad.code
        where ba.code = #{code}
    </select>
</mapper>
