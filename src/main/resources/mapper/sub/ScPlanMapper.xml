<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hjb.beijin_paishui_data_middleground.mapper.sub.ScPlanMapper">

    <select id="getQueryQuantityByMonth" resultType="java.util.Map">
        select
        to_char(ap.original_plan_date, 'MM') as month,
        count(1)::int as count
        from
        yy.sc_plan ap
        join yy.sc_plan_kemu ke on ap.uuid = ke.plan_uuid
        <where>
            <if test="syxz != null and syxz != ''">
                ke.kemu_syxz = #{syxz}
            </if>
            <if test="status != null">
                <if test="syxz != null and syxz != ''">
                    and
                </if>
                ap.status = #{status}
            </if>
            <if test="oneYearAgo != null">
                <if test="syxz != null and syxz != '' or status != null">
                    and
                </if>
                ap.original_plan_date &gt;= #{oneYearAgo}
            </if>
            <if test="now != null">
                <if test="syxz != null and syxz != '' or status != null or oneYearAgo != null">
                    and
                </if>
                ap.original_plan_date &lt;= #{now}
            </if>
        </where>
        group by
        to_char(ap.original_plan_date, 'MM')
    </select>
    <select id="getScPlanLength" resultType="java.lang.Double">
        select sum(gp.pipelength)
        from
            yy.sc_plan ap
            join yy.sc_plan_kemu ke  on ap.uuid = ke.plan_uuid
            join yy.sc_plan_pipe pip on ke.uuid = pip.kemu_uuid
            join gw.gw_pipe gp on gp.code = pip.gw_id
        where
              original_plan_date &gt;= #{oneYearAgo}
          and original_plan_date &lt;= #{now}
          and ap.status = 'replied'
    </select>
    <select id="getMudOutput" resultType="java.lang.Double">
        select sum(ke.plan_value)
        from yy.sc_plan ap
                 join yy.sc_plan_kemu ke on ap.uuid = ke.plan_uuid
                 join yy.a_kemu akm on ke.kemu_code = akm.code
        <where>
            ap.status = 'replied'
            and akm.name like '%泥量%'
            <if test="syxz != null and syxz != ''">
             and  ke.kemu_syxz = #{syxz}
            </if>
            <if test="oneYearAgo != null">
                <if test="syxz != null and syxz != '' or status != null">
                    and
                </if>
                ap.original_plan_date &gt;= #{oneYearAgo}
            </if>
            <if test="now != null">
                <if test="syxz != null and syxz != '' or status != null or oneYearAgo != null">
                    and
                </if>
                ap.original_plan_date &lt;= #{now}
            </if>
        </where>
    </select>
</mapper>
