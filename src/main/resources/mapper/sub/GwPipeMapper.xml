<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hjb.beijin_paishui_data_middleground.mapper.sub.GwPipeMapper">
    <select id="getByCode" resultType="com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.GwPipeAndGzTzAndGwYyTzInfo">
        SELECT pipe.code,
               pipe.gctz,
               pipe.pipelength,
               pipe.gj,
               pipe.gj_type   as gjType,
               pipe.syxz,
               pipe.jgxs,
               pipe.syjdm,
               pipe.xyjdm,
               pipe.xygdg,
               pipe.sygdg,
               pipe.pd,
               pipe.ts,
               pipe.tsgd,
               pipe.old_code  as oldCode,
               pipe.gxlx,
               pipe.bfsj,
               pipe.bz,
               pipe.editor,
               pipe.edit_time as editTime,
               pipe.gdcz,
               banzu.name     as banzuName,
               dep.name       as deptName,
               tz.sfqs,
               tz.xmbh,
               tz.yjly,
               tz.name        as tzName,
               yz.name        as gqName,
               road2.name     as ssld,
               road.name      as szdl
        FROM gw.gw_pipe pipe
                 JOIN gw.gw_gc_tz tz on pipe.gctz = tz.code
                 JOIN gw.a_banzu banzu on pipe.banzu = banzu.code
                 JOIN gw.a_dept dep on banzu.dept = dep.code
                 LEFT JOIN gw.gw_yy_tz yz on pipe.yytz = yz.code
                 LEFT JOIN gw.gw_road road on pipe.road = road.code
                 LEFT JOIN gw.gw_road2 road2 on pipe.road2 = road2.code
        where pipe.code = #{code}
    </select>
    <select id="seachGwPipePoint"
            resultType="com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.GwPipeAndGzTzAndGwYyTzInfo">
        SELECT
        pipe.code,
        pipe.gctz,
        pipe.pipelength,
        pipe.gj,
        pipe.gj_type as gjType,
        pipe.syxz,
        pipe.jgxs,
        pipe.syjdm,
        pipe.xyjdm,
        pipe.xygdg,
        pipe.sygdg,
        pipe.pd,
        pipe.ts,
        pipe.tsgd,
        pipe.old_code as oldCode,
        pipe.gxlx,
        pipe.bfsj,
        pipe.bz,
        pipe.editor,
        pipe.edit_time as editTime,
        pipe.gdcz,
        banzu.name as banzuName,
        dep.name as deptName,
        tz.sfqs,
        tz.xmbh,
        tz.yjly,
        tz.name as tzName,
        yz.name as gqName,
        road2.name as ssld,
        road.name as szdl
        FROM gw.gw_pipe pipe JOIN gw.gw_gc_tz tz on pipe.gctz = tz.code
        JOIN gw.a_banzu banzu on pipe.banzu = banzu.code
        JOIN gw.a_dept dep on banzu.dept = dep.code
        LEFT JOIN gw.gw_yy_tz yz on pipe.yytz = yz.code
        LEFT JOIN gw.gw_road road on pipe.road = road.code
        LEFT JOIN gw.gw_road2 road2 on pipe.road2 = road2.code
        <where>
            <if test="dept != null and dept != ''">
                and pipe.dept = #{dept}
            </if>
            <if test="gctzCode != null and gctzCode != ''">
                and tz.code = #{gctzCode,jdbcType=BIGINT}::bigint
            </if>
            <if test="gctzName != null and gctzName != ''">
                and tz.name = #{gctzName}
            </if>
            <if test="yyTzCode != null and yyTzCode != ''">
                and yz.code = #{yyTzCode}
            </if>
            <if test="yyTzName != null and yyTzName != ''">
                and yz.name = #{yyTzName}
            </if>
            <if test="code != null and code != ''">
                and pipe.code = #{code}
            </if>
            <if test="sfqs != null and sfqs != ''">
                and tz.sfqs = #{sfqs}
            </if>
            <if test="syxz != null and syxz != ''">
                and pipe.syxz = #{syxz}
            </if>
            <if test="syzt != null and syzt != ''">
                and pipe.syzt = #{syzt}
            </if>
            <if test="tsgd != null and tsgd != ''">
                and pipe.tsgd = #{tsgd}
            </if>
            <if test="gxlx != null and gxlx != ''">
                and pipe.gxlx = #{gxlx}
            </if>
        </where>
    </select>
    <select id="getnormalOperationRate" resultType="java.lang.Double">
        select sum(gwp.pipelength)
        from gw.gw_pipe gwp
                 join yy.gw_pipe_ext ygp on gwp.code = ygp.code_ext
                 join gw.gw_gc_tz tz on gwp.gctz = tz.code
        where gwp.syzt = 'using'
          and tz.sfqs = 'y'
          and ygp.new_cal_gndj = 1 or ygp.new_cal_gndj = 2
    </select>
    <select id="getnormalOperationRateAll" resultType="java.lang.Double">
        select sum(gwp.pipelength)
        from gw.gw_pipe gwp
                 join yy.gw_pipe_ext ygp on gwp.code = ygp.code_ext
                 join gw.gw_gc_tz tz on gwp.gctz = tz.code
        where gwp.syzt = 'using'
            and tz.sfqs = 'y'
    </select>
</mapper>
