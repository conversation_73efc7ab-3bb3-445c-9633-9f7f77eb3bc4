<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hjb.beijin_paishui_data_middleground.mapper.sub.interactive.AXunchaMapper">
    <select id="getByCode" resultType="com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.AXunchaVo">
        SELECT
            xuncha.code,
            xuncha.name,
            ab.name AS banzuName,
            ad.name AS deptName,
            xuncha.dept,
            xuncha.banzu,
            ST_AsText(xuncha.sgeom) as sgeom
        FROM
            gw.a_xuncha xuncha
                LEFT JOIN
            gw.a_banzu ab ON xuncha.banzu = ab.code
                LEFT JOIN
            gw.a_dept ad ON xuncha.dept = ad.code
        where xuncha.code = #{code}
    </select>
</mapper>
