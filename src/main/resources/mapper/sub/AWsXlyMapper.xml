<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hjb.beijin_paishui_data_middleground.mapper.sub.interactive.AWsXlyMapper">
    <select id="getByCode" resultType="com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.AWsXlyVo">
        SELECT
            awsx.code,
            awsx.name,
            ab.name AS banzuName,
            ad.name AS deptName,
            awsx.dept,
            awsx.banzu,
            awsx.ly
        FROM
            gw.a_ws_xly awsx
                LEFT JOIN
            gw.a_banzu ab ON awsx.banzu = ab.code
                LEFT JOIN
            gw.a_dept ad ON awsx.dept = ad.code
        <where>
            <if test="code != null">
                awsx.code = #{code}
            </if>
        </where>
    </select>
</mapper>
