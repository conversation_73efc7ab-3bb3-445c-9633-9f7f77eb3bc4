<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hjb.beijin_paishui_data_middleground.mapper.szls.SyntheticalPumpStationOperationMapper">

    <resultMap id="BaseResultMap" type="com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical.SyntheticalPumpStationOperation">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="pumpStationTotal" column="pump_station_total" jdbcType="VARCHAR"/>
            <result property="rainPumpStation" column="rain_pump_station" jdbcType="VARCHAR"/>
            <result property="sewagePumpStation" column="sewage_pump_station" jdbcType="VARCHAR"/>
            <result property="totalLiftingCapacity" column="total_lifting_capacity" jdbcType="VARCHAR"/>
            <result property="totalStorageCapacity" column="total_storage_capacity" jdbcType="VARCHAR"/>
            <result property="initialPool" column="initial_pool" jdbcType="VARCHAR"/>
            <result property="storagePool" column="storage_pool" jdbcType="VARCHAR"/>
            <result property="isReported" column="is_reported" jdbcType="SMALLINT"/>
            <result property="createTime" column="create_time" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="VARCHAR"/>
            <result property="isDelete" column="is_delete" jdbcType="SMALLINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,pump_station_total,rain_pump_station,
        sewage_pump_station,total_lifting_capacity,total_storage_capacity,
        initial_pool,storage_pool,is_reported,
        create_time,update_time,is_delete
    </sql>
</mapper>
