<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hjb.beijin_paishui_data_middleground.mapper.szls.SzlsPlanMapper">

    <resultMap id="BaseResultMap" type="com.hjb.beijin_paishui_data_middleground.entity.szls.po.SzlsPlan">
            <result property="id" column="id" />
            <result property="planId" column="plan_id" />
            <result property="modelId" column="model_id" />
            <result property="planName" column="plan_name" />
            <result property="startTime" column="start_time" />
            <result property="endTime" column="end_time" />
            <result property="outputStep" column="output_step" />
            <result property="gmtCreate" column="gmt_create" />
    </resultMap>

    <sql id="Base_Column_List">
        id,plan_id,model_id,plan_name,start_time,end_time,
        output_step,gmt_create
    </sql>
</mapper>
