<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hjb.beijin_paishui_data_middleground.mapper.szls.DrainAncillaryFacilitiesMapper">

    <resultMap id="BaseResultMap" type="com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainAncillaryFacilities">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="manholeCover" column="manhole_cover" jdbcType="VARCHAR"/>
            <result property="rainwater" column="rainwater" jdbcType="VARCHAR"/>
            <result property="sewage" column="sewage" jdbcType="VARCHAR"/>
            <result property="combinedFlow" column="combined_flow" jdbcType="VARCHAR"/>
            <result property="grate" column="grate" jdbcType="VARCHAR"/>
            <result property="rainwaterPumpingStation" column="rainwater_pumping_station" jdbcType="VARCHAR"/>
            <result property="rainwaterInlet" column="rainwater_inlet" jdbcType="VARCHAR"/>
            <result property="drainageOutletAuth" column="drainage_outlet_auth" jdbcType="VARCHAR"/>
            <result property="drainageOutletUnauth" column="drainage_outlet_unauth" jdbcType="VARCHAR"/>
            <result property="initialPond" column="Initial_pond" jdbcType="VARCHAR"/>
            <result property="storagePond" column="storage_pond" jdbcType="VARCHAR"/>
            <result property="sewagePumpStation" column="sewage_pump_station" jdbcType="VARCHAR"/>
            <result property="isReported" column="is_reported" jdbcType="SMALLINT"/>
            <result property="createTime" column="create_time" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="VARCHAR"/>
            <result property="isDelete" column="is_delete" jdbcType="SMALLINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,manhole_cover,rainwater,
        sewage,combined_flow,grate,
        rainwater_pumping_station,rainwater_inlet,drainage_outlet_auth,
        drainage_outlet_unauth,Initial_pond,storage_pond,
        sewage_pump_station,is_reported,create_time,
        update_time,is_delete
    </sql>
</mapper>
