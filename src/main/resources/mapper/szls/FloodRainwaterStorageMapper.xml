<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hjb.beijin_paishui_data_middleground.mapper.szls.FloodRainwaterStorageMapper">

    <resultMap id="BaseResultMap" type="com.hjb.beijin_paishui_data_middleground.entity.szls.po.flood.FloodRainwaterStorage">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="pumpStationTotal" column="pump_station_total" jdbcType="VARCHAR"/>
            <result property="realTimeOperation" column="real_time_operation" jdbcType="VARCHAR"/>
            <result property="cumulativeOperation" column="cumulative_operation" jdbcType="VARCHAR"/>
            <result property="cumulativeLift" column="cumulative_lift" jdbcType="VARCHAR"/>
            <result property="initialStorageCapacity" column="initial_storage_capacity" jdbcType="VARCHAR"/>
            <result property="tankStorageCapacity" column="tank_storage_capacity" jdbcType="VARCHAR"/>
            <result property="rainwaterDrainageCapacity" column="rainwater_drainage_capacity" jdbcType="VARCHAR"/>
            <result property="searchCompany" column="search_company" jdbcType="VARCHAR"/>
            <result property="isReported" column="is_reported" jdbcType="SMALLINT"/>
            <result property="createTime" column="create_time" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="VARCHAR"/>
            <result property="isDelete" column="is_delete" jdbcType="SMALLINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,pump_station_total,real_time_operation,
        cumulative_operation,cumulative_lift,initial_storage_capacity,
        tank_storage_capacity,rainwater_drainage_capacity,search_company,
        is_reported,create_time,update_time,
        is_delete
    </sql>
</mapper>
