<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hjb.beijin_paishui_data_middleground.mapper.szls.FloodPipeNetworkMonitorMapper">

    <resultMap id="BaseResultMap" type="com.hjb.beijin_paishui_data_middleground.entity.szls.po.flood.FloodPipeNetworkMonitor">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="rainwaterPipeLevelLow50" column="rainwater_pipe_level_low_50" jdbcType="VARCHAR"/>
            <result property="rainwaterPipeLevel50To80" column="rainwater_pipe_level_50_to_80" jdbcType="VARCHAR"/>
            <result property="rainwaterPipeLevel80To100" column="rainwater_pipe_level_80_to_100" jdbcType="VARCHAR"/>
            <result property="stationDetails" column="station_details" jdbcType="VARCHAR"/>
            <result property="searchType" column="search_type" jdbcType="VARCHAR"/>
            <result property="isReported" column="is_reported" jdbcType="SMALLINT"/>
            <result property="createTime" column="create_time" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="VARCHAR"/>
            <result property="isDelete" column="is_delete" jdbcType="SMALLINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,rainwater_pipe_level_low_50,rainwater_pipe_level_50_to_80,
        rainwater_pipe_level_80_to_100,station_details,search_type,
        is_reported,create_time,update_time,
        is_delete
    </sql>
</mapper>
