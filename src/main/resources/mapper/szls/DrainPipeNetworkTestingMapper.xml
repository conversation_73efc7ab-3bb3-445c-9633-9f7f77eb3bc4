<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hjb.beijin_paishui_data_middleground.mapper.szls.DrainPipeNetworkTestingMapper">

    <resultMap id="BaseResultMap" type="com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainPipeNetworkTesting">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="pipeNetworkGoodRate" column="pipe_network_good_rate" jdbcType="VARCHAR"/>
            <result property="actualDetectionLength" column="actual_detection_length" jdbcType="VARCHAR"/>
            <result property="rainDetectionLength" column="rain_detection_length" jdbcType="VARCHAR"/>
            <result property="sewageDetectionLength" column="sewage_detection_length" jdbcType="VARCHAR"/>
            <result property="pipeNetworkInspection" column="pipe_network_inspection" jdbcType="VARCHAR"/>
            <result property="isReported" column="is_reported" jdbcType="SMALLINT"/>
            <result property="createTime" column="create_time" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="VARCHAR"/>
            <result property="isDelete" column="is_delete" jdbcType="SMALLINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,pipe_network_good_rate,actual_detection_length,
        rain_detection_length,sewage_detection_length,pipe_network_inspection,
        is_reported,create_time,update_time,
        is_delete
    </sql>
</mapper>
