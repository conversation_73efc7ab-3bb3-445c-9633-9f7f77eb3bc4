<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hjb.beijin_paishui_data_middleground.mapper.szls.DrainOperationalRiskMapper">

    <resultMap id="BaseResultMap" type="com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainOperationalRisk">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="gisLength" column="gis_length" jdbcType="DOUBLE"/>
            <result property="chainRatio" column="chain_ratio" jdbcType="VARCHAR"/>
            <result property="resolved" column="resolved" jdbcType="VARCHAR"/>
            <result property="resolvedProportion" column="resolved_proportion" jdbcType="VARCHAR"/>
            <result property="unsolved" column="unsolved" jdbcType="VARCHAR"/>
            <result property="unsolvedProportion" column="unsolved_proportion" jdbcType="VARCHAR"/>
            <result property="sortCondition" column="sort_condition" jdbcType="VARCHAR"/>
            <result property="searchType" column="search_type" jdbcType="VARCHAR"/>
            <result property="isReported" column="is_reported" jdbcType="SMALLINT"/>
            <result property="createTime" column="create_time" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="VARCHAR"/>
            <result property="isDelete" column="is_delete" jdbcType="SMALLINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,gis_length,chain_ratio,
        resolved,resolved_proportion,unsolved,
        unsolved_proportion,sort_condition,search_type,
        is_reported,create_time,update_time,
        is_delete
    </sql>
</mapper>
