<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hjb.beijin_paishui_data_middleground.mapper.szls.EventEventMapper">

    <resultMap id="BaseResultMap" type="com.hjb.beijin_paishui_data_middleground.entity.szls.po.event.EventEvent">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="totalEvents" column="total_events" jdbcType="VARCHAR"/>
            <result property="operationalScope" column="operational_scope" jdbcType="VARCHAR"/>
            <result property="nonOperationalScope" column="non_operational_scope" jdbcType="VARCHAR"/>
            <result property="rightsConfirmation" column="rights_confirmation" jdbcType="VARCHAR"/>
            <result property="totalEventsRatio" column="total_events_ratio" jdbcType="VARCHAR"/>
            <result property="operationalScopeRatio" column="operational_scope_ratio" jdbcType="VARCHAR"/>
            <result property="nonOperationalScopeRatio" column="non_operational_scope_ratio" jdbcType="VARCHAR"/>
            <result property="rightsConfirmationRatio" column="rights_confirmation_ratio" jdbcType="VARCHAR"/>
            <result property="isReported" column="is_reported" jdbcType="SMALLINT"/>
            <result property="createTime" column="create_time" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="VARCHAR"/>
            <result property="isDelete" column="is_delete" jdbcType="SMALLINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,total_events,operational_scope,
        non_operational_scope,rights_confirmation,total_events_ratio,
        operational_scope_ratio,non_operational_scope_ratio,rights_confirmation_ratio,
        is_reported,create_time,update_time,
        is_delete
    </sql>
</mapper>
