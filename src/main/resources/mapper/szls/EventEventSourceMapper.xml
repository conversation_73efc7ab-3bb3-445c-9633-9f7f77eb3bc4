<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hjb.beijin_paishui_data_middleground.mapper.szls.EventEventSourceMapper">

    <resultMap id="BaseResultMap" type="com.hjb.beijin_paishui_data_middleground.entity.szls.po.event.EventEventSource">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="quantity12345" column="quantity_12345" jdbcType="VARCHAR"/>
            <result property="quantity12345Ratio" column="quantity_12345_ratio" jdbcType="VARCHAR"/>
            <result property="quantity96159" column="quantity_96159" jdbcType="VARCHAR"/>
            <result property="quantity96159Ratio" column="quantity_96159_ratio" jdbcType="VARCHAR"/>
            <result property="isReported" column="is_reported" jdbcType="SMALLINT"/>
            <result property="createTime" column="create_time" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="VARCHAR"/>
            <result property="isDelete" column="is_delete" jdbcType="SMALLINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,quantity_12345,quantity_12345_ratio,
        quantity_96159,quantity_96159_ratio,is_reported,
        create_time,update_time,is_delete
    </sql>
</mapper>
