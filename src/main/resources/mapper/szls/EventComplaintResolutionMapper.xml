<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hjb.beijin_paishui_data_middleground.mapper.szls.EventComplaintResolutionMapper">

    <resultMap id="BaseResultMap" type="com.hjb.beijin_paishui_data_middleground.entity.szls.po.event.EventComplaintResolution">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="respondRate" column="respond_rate" jdbcType="VARCHAR"/>
            <result property="workOutRate" column="work_out_rate" jdbcType="VARCHAR"/>
            <result property="satisfactionRate" column="satisfaction_rate" jdbcType="VARCHAR"/>
            <result property="oneTimeDisposalQualifiedRate" column="one_time_disposal_qualified_rate" jdbcType="VARCHAR"/>
            <result property="oneTimeDisposalSatisfactionRate" column="one_time_disposal_satisfaction_rate" jdbcType="VARCHAR"/>
            <result property="isReported" column="is_reported" jdbcType="SMALLINT"/>
            <result property="createTime" column="create_time" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="VARCHAR"/>
            <result property="isDelete" column="is_delete" jdbcType="SMALLINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,respond_rate,work_out_rate,
        satisfaction_rate,one_time_disposal_qualified_rate,one_time_disposal_satisfaction_rate,
        is_reported,create_time,update_time,
        is_delete
    </sql>
</mapper>
