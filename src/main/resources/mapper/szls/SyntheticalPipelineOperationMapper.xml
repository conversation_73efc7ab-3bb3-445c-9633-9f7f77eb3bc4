<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hjb.beijin_paishui_data_middleground.mapper.szls.SyntheticalPipelineOperationMapper">

    <resultMap id="BaseResultMap" type="com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical.SyntheticalPipelineOperation">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="totalPipelineLength" column="total_pipeline_length" jdbcType="VARCHAR"/>
            <result property="rainwaterPipeline" column="rainwater_pipeline" jdbcType="VARCHAR"/>
            <result property="sewagePipeline" column="sewage_pipeline" jdbcType="VARCHAR"/>
            <result property="manholeTotal" column="manhole_total" jdbcType="VARCHAR"/>
            <result property="rainwaterGrateTotal" column="rainwater_grate_total" jdbcType="VARCHAR"/>
            <result property="riverOutlet" column="river_outlet" jdbcType="VARCHAR"/>
            <result property="pipelineDetails" column="pipeline_details" jdbcType="VARCHAR"/>
            <result property="isReported" column="is_reported" jdbcType="SMALLINT"/>
            <result property="createTime" column="create_time" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="VARCHAR"/>
            <result property="isDelete" column="is_delete" jdbcType="SMALLINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,total_pipeline_length,rainwater_pipeline,
        sewage_pipeline,manhole_total,rainwater_grate_total,
        river_outlet,pipeline_details,is_reported,
        create_time,update_time,is_delete
    </sql>
</mapper>
