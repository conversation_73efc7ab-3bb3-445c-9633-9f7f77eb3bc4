<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hjb.beijin_paishui_data_middleground.mapper.szls.DrainMaintenanceProductionMapper">

    <resultMap id="BaseResultMap" type="com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainMaintenanceProduction">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="pipeNetworkSmooth" column="pipe_network_smooth" jdbcType="VARCHAR"/>
            <result property="actualCuringLength" column="actual_curing_length" jdbcType="DOUBLE"/>
            <result property="rainSludge" column="rain_sludge" jdbcType="VARCHAR"/>
            <result property="sewageSludge" column="sewage_sludge" jdbcType="VARCHAR"/>
            <result property="annualTrend" column="annual_trend" jdbcType="VARCHAR"/>
            <result property="isReported" column="is_reported" jdbcType="SMALLINT"/>
            <result property="createTime" column="create_time" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="VARCHAR"/>
            <result property="isDelete" column="is_delete" jdbcType="SMALLINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,pipe_network_smooth,actual_curing_length,
        rain_sludge,sewage_sludge,annual_trend,
        is_reported,create_time,update_time,
        is_delete
    </sql>
</mapper>
