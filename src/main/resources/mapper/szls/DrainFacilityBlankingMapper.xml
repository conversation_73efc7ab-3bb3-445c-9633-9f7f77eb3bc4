<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hjb.beijin_paishui_data_middleground.mapper.szls.DrainFacilityBlankingMapper">

    <resultMap id="BaseResultMap" type="com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainFacilityBlanking">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="renewalLength" column="renewal_length" jdbcType="DOUBLE"/>
            <result property="localMaintenance" column="local_maintenance" jdbcType="VARCHAR"/>
            <result property="manholeCoverTreatment" column="manhole_cover_treatment" jdbcType="VARCHAR"/>
            <result property="annualTrend" column="annual_trend" jdbcType="VARCHAR"/>
            <result property="solutionInformation" column="solution_information" jdbcType="VARCHAR"/>
            <result property="isReported" column="is_reported" jdbcType="SMALLINT"/>
            <result property="createTime" column="create_time" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="VARCHAR"/>
            <result property="isDelete" column="is_delete" jdbcType="SMALLINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,renewal_length,local_maintenance,
        manhole_cover_treatment,annual_trend,solution_information,
        is_reported,create_time,update_time,
        is_delete
    </sql>
</mapper>
