<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hjb.beijin_paishui_data_middleground.mapper.szls.SyntheticalRainfallConditionsMapper">

    <resultMap id="BaseResultMap" type="com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical.SyntheticalRainfallConditions">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="rainfallAmount" column="rainfall_amount" jdbcType="VARCHAR"/>
            <result property="rainfallAmountYoy" column="rainfall_amount_yoy" jdbcType="VARCHAR"/>
            <result property="cumulativeAverageRainfall" column="cumulative_average_rainfall" jdbcType="VARCHAR"/>
            <result property="cumulativeAverageRainfallYoy" column="cumulative_average_rainfall_yoy" jdbcType="VARCHAR"/>
            <result property="top10RainfallStations" column="top10_rainfall_stations" jdbcType="VARCHAR"/>
            <result property="administrativeCumulativeRainfall" column="administrative_cumulative_rainfall" jdbcType="VARCHAR"/>
            <result property="officeRainfallAdministrative" column="office_rainfall_administrative" jdbcType="VARCHAR"/>
            <result property="riverRainfallAdministrative" column="river_rainfall_administrative" jdbcType="VARCHAR"/>
            <result property="searchYear" column="search_year" jdbcType="VARCHAR"/>
            <result property="isReported" column="is_reported" jdbcType="SMALLINT"/>
            <result property="createTime" column="create_time" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="VARCHAR"/>
            <result property="isDelete" column="is_delete" jdbcType="SMALLINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,rainfall_amount,rainfall_amount_yoy,
        cumulative_average_rainfall,cumulative_average_rainfall_yoy,top10_rainfall_stations,
        administrative_cumulative_rainfall,office_rainfall_administrative,river_rainfall_administrative,
        search_year,is_reported,create_time,
        update_time,is_delete
    </sql>
</mapper>
