<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hjb.beijin_paishui_data_middleground.mapper.szls.SyntheticalReclaimedWaterSupplyMapper">

    <resultMap id="BaseResultMap" type="com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical.SyntheticalReclaimedWaterSupply">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="pumpHouse" column="pump_house" jdbcType="VARCHAR"/>
            <result property="distributionNetwork" column="distribution_network" jdbcType="VARCHAR"/>
            <result property="distributionCapacity" column="distribution_capacity" jdbcType="VARCHAR"/>
            <result property="isReported" column="is_reported" jdbcType="SMALLINT"/>
            <result property="createTime" column="create_time" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="VARCHAR"/>
            <result property="isDelete" column="is_delete" jdbcType="SMALLINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,pump_house,distribution_network,
        distribution_capacity,is_reported,create_time,
        update_time,is_delete
    </sql>
</mapper>
