package com.hjb.beijin_paishui_data_middleground.mapper.szls;

import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainHybridConnection;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
* <AUTHOR>
* @description 针对表【drain_hybrid_connection(混接)】的数据库操作Mapper
* @createDate 2025-01-10 13:44:25
* @Entity com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainHybridConnection
*/
public interface DrainHybridConnectionMapper extends BaseMapper<DrainHybridConnection> {

}




