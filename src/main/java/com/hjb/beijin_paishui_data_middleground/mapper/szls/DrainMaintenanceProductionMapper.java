package com.hjb.beijin_paishui_data_middleground.mapper.szls;

import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainMaintenanceProduction;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
* <AUTHOR>
* @description 针对表【drain_maintenance_production(养护生产)】的数据库操作Mapper
* @createDate 2025-01-10 13:33:26
* @Entity com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainMaintenanceProduction
*/
public interface DrainMaintenanceProductionMapper extends BaseMapper<DrainMaintenanceProduction> {

}




