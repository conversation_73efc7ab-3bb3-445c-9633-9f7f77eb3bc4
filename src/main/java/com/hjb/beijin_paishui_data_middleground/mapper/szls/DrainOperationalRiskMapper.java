package com.hjb.beijin_paishui_data_middleground.mapper.szls;

import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainOperationalRisk;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
* <AUTHOR>
* @description 针对表【drain_operational_risk(运行风险)】的数据库操作Mapper
* @createDate 2025-01-10 14:12:49
* @Entity com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainOperationalRisk
*/
public interface DrainOperationalRiskMapper extends BaseMapper<DrainOperationalRisk> {

}




