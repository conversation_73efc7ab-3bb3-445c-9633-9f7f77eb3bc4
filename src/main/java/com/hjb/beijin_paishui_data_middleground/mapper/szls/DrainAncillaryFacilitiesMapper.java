package com.hjb.beijin_paishui_data_middleground.mapper.szls;

import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainAncillaryFacilities;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
* <AUTHOR>
* @description 针对表【drain_ancillary_facilities(附属设施)】的数据库操作Mapper
* @createDate 2025-01-10 13:27:08
* @Entity com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainAncillaryFacilities
*/
public interface DrainAncillaryFacilitiesMapper extends BaseMapper<DrainAncillaryFacilities> {

}




