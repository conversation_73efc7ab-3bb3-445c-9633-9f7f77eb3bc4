package com.hjb.beijin_paishui_data_middleground.mapper.szls;

import com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical.SyntheticalWaterTreatmentPlate;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
* <AUTHOR>
* @description 针对表【synthetical_water_treatment_plate(水处理板块)】的数据库操作Mapper
* @createDate 2025-01-10 11:18:58
* @Entity com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical.SyntheticalWaterTreatmentPlate
*/
public interface SyntheticalWaterTreatmentPlateMapper extends BaseMapper<SyntheticalWaterTreatmentPlate> {

}




