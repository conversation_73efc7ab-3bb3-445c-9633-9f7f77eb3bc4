package com.hjb.beijin_paishui_data_middleground.mapper.szls;

import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainPipeNetworkAndFacilities;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
* <AUTHOR>
* @description 针对表【drain_pipe_network_and_facilities(管网及设施)】的数据库操作Mapper
* @createDate 2025-01-10 13:19:38
* @Entity com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainPipeNetworkAndFacilities
*/
public interface DrainPipeNetworkAndFacilitiesMapper extends BaseMapper<DrainPipeNetworkAndFacilities> {

}




