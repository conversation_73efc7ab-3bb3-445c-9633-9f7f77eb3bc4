package com.hjb.beijin_paishui_data_middleground.mapper.szls;

import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainPeripheralConstruction;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
* <AUTHOR>
* @description 针对表【drain_peripheral_construction(周边施工)】的数据库操作Mapper
* @createDate 2025-01-10 13:56:36
* @Entity com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainPeripheralConstruction
*/
public interface DrainPeripheralConstructionMapper extends BaseMapper<DrainPeripheralConstruction> {

}




