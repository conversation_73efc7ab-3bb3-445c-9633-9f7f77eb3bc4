package com.hjb.beijin_paishui_data_middleground.mapper.szls;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.flood.StationGj;
import org.apache.ibatis.annotations.Mapper;

/**
 * @Description: 无法在pipe表中找到对应管径的测站及其管径Mapper
 * @Author: lvhongen
 * @Date: 2025-05-07 14:17
 * @Version： 1.0
 **/
@Mapper
public interface StationGjMapper extends BaseMapper<StationGj> {
}
