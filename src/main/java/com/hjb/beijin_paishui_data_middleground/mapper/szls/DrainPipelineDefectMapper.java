package com.hjb.beijin_paishui_data_middleground.mapper.szls;

import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainPipelineDefect;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
* <AUTHOR>
* @description 针对表【drain_pipeline_defect(管道缺陷)】的数据库操作Mapper
* @createDate 2025-01-13 11:12:17
* @Entity com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainPipelineDefect
*/
public interface DrainPipelineDefectMapper extends BaseMapper<DrainPipelineDefect> {

}




