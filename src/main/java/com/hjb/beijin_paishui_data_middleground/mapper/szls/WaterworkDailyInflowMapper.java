package com.hjb.beijin_paishui_data_middleground.mapper.szls;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.waterwork.WaterworkDailyInflow;

/**
 * <AUTHOR>
 * @description 针对表【waterwork_daily_inflow(当日进水量)】的数据库操作Mapper
 * @createDate 2025/7/30 14:51
 * @Entity om.hjb.beijin_paishui_data_middleground.entity.szls.po.waterwork.WaterworkDailyInflow
 */
public interface WaterworkDailyInflowMapper extends BaseMapper<WaterworkDailyInflow> {

}
