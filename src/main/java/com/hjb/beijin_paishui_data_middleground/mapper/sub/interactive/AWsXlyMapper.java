package com.hjb.beijin_paishui_data_middleground.mapper.sub.interactive;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.AWsXlyVo;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.PointVo;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw.AWsMain;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw.AWsXly;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Description: 污水小流域Mapper
 * @Author: lvhongen
 * @Date: 2025-04-23 08:55
 * @Version： 1.0
 **/
@Mapper
@DS("guanwang")
public interface AWsXlyMapper extends BaseMapper<AWsXly> {
    @Select("select code,ST_AsText(sgeom) as sgeom from gw.a_ws_xly")
    List<PointVo> getAllList();
    List<AWsXlyVo> getByCode(String code);
}
