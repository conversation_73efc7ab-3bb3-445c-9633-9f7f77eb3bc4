package com.hjb.beijin_paishui_data_middleground.mapper.sub.interactive;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.PointVo;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.SyntheticaSelectData;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw.AXzj;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Description: 行政区字典表Mapper
 * @Author: lvhongen
 * @Date: 2025-03-31 14:42
 * @Version： 1.0
 **/
@DS("guanwang")
@Mapper
public interface AXzjMapper extends BaseMapper<AXzj> {
    @Select("select name,code as value from gw.a_xzj")
    List<SyntheticaSelectData.SelectData> getSyntheticaInteractiveSelect ();
    @Select("select code,name,ST_AsText(sgeom) as sgeom from gw.a_xzj")
    List<PointVo> getAllList();

    @Select("select code,name,ST_AsText(sgeom) as sgeom from gw.a_xzj where code = #{code}")
    AXzj getByCode(String code);
}
