package com.hjb.beijin_paishui_data_middleground.mapper.sub;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hjb.beijin_paishui_data_middleground.entity.sub.dto.EventTypeCountDTO;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.ps.V3dDrainOrder;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.event.EventEventType;
import lombok.NonNull;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * @Description: 【排水】排水事件视图Mapper
 * @Author: lvhongen
 * @Date: 2025-03-22 17:29
 * @Version： 1.0
 **/
@DS("paishui")
@Mapper
public interface V3dDrainOrderMapper extends BaseMapper<V3dDrainOrder> {
    /**
     * 查询当前事件所在年份中权属为1的事件总量
     *
     * @param oneYearAgo 近一年的起始时间
     * @param now        当前时间
     * @return 事件总量
     */
    @Select("SELECT COUNT(*) " +
            "FROM v_3d_drain_order " +
            "WHERE register_time >= #{oneYearAgo} AND register_time <= #{now}")
    long countTotalEventsCurrent(@NonNull LocalDateTime oneYearAgo, @NonNull LocalDateTime now);
    /**
     * 查询当前事件所在年份中权属为1的运营范围数
     *
     * @param oneYearAgo 近一年的起始时间
     * @param now        当前时间
     * @return 运营范围数
     */
    @Select("SELECT COUNT(*) " +
            "FROM v_3d_drain_order " +
            "WHERE register_time >= #{oneYearAgo} AND register_time <= #{now} AND bol_ownership = 1")
    long countOperationalScopeCurrent(@NonNull LocalDateTime oneYearAgo, @NonNull LocalDateTime now);
    /**
     * 查询当前事件所在年份中权属为0的非运营范围数
     *
     * @param oneYearAgo 近一年的起始时间
     * @param now        当前时间
     * @return 非运营范围数
     */
    @Select("SELECT COUNT(*) " +
            "FROM v_3d_drain_order " +
            "WHERE register_time >= #{oneYearAgo} AND register_time <= #{now} AND bol_ownership = 0")
    long countNonOperationalScopeCurrent(@NonNull LocalDateTime oneYearAgo, @NonNull LocalDateTime now);
    /**
     * 查询当前事件所在年份中权属为空或空字符串的确权中数
     *
     * @param oneYearAgo 近一年的起始时间
     * @param now        当前时间
     * @return 确权中数
     */
    @Select("SELECT COUNT(*) " +
            "FROM v_3d_drain_order " +
            "WHERE register_time >= #{oneYearAgo} AND register_time <= #{now} AND (bol_ownership IS NULL)")
    long countRightsConfirmationCurrent(@NonNull LocalDateTime oneYearAgo, @NonNull LocalDateTime now);

    /**
     * 查询近一年的去年中权属为空或空字符串的确权中数
     *
     * @param twoYearsAgo       近一年的去年的起始时间
     * @param oneYearAgoMinusOneDay 近一年前一天的时间
     * @return 确权中数
     */
    @Select("SELECT COUNT(*) " +
            "FROM v_3d_drain_order " +
            "WHERE register_time >= #{twoYearsAgo} AND register_time <= #{oneYearAgoMinusOneDay} AND (bol_ownership IS NULL)")
    long countRightsConfirmationLastYear(@NonNull LocalDateTime twoYearsAgo, @NonNull LocalDateTime oneYearAgoMinusOneDay);

    /**
     * 通过事件类型分组查询事件
     * @param oneYearAgo
     * @param now
     * @param twoYearsAgo
     * @return
     */
    List<EventTypeCountDTO> countEventTypesByYearRange(@Param("now") LocalDateTime now,
                                                       @Param("oneYearAgo") LocalDateTime oneYearAgo,
                                                       @Param("twoYearsAgo") LocalDateTime twoYearsAgo);
    /**
     * 通过时间查询classify_one为"001"的事件
     * @param oneYearAgo
     * @param now
     * @return
     */
    List<V3dDrainOrder> getFacilityAppealEventYearOrders(@Param("oneYearAgo") LocalDateTime oneYearAgo,
                                                         @Param("now") LocalDateTime now);

    /**
     * 获取分公司区域历史告警详情
     */


}

