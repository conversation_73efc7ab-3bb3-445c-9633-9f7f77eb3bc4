package com.hjb.beijin_paishui_data_middleground.mapper.sub;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.fx.ShareCarInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Description: 车辆视图实体类
 * @Author: lvhongen
 * @Date: 2025-04-01 17:44
 * @Version： 1.0
 **/
@DS("fangxun")
@Mapper
public interface ShareCarInfoMapper extends BaseMapper<ShareCarInfo> {
    //获取车辆类型信息
    @Select("SELECT dev_name from FXYJ.share_car_info WHERE dev_name IS NOT NULL AND " +
            "trim(dev_name) != ' ' group by dev_name")
    List<String> getCarTypeQueryList();
    //获取车辆分公司类型列表
    @Select("SELECT comnm from FXYJ.share_car_info group by comnm")
    List<String> getCarComnmQueryList();
}
