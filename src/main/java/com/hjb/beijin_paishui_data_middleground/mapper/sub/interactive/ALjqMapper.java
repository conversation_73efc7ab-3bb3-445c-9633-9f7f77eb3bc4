package com.hjb.beijin_paishui_data_middleground.mapper.sub.interactive;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.ALjqVo;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.AWsXlyVo;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.PointVo;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw.ALjq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Description: 立交桥Mapper
 * @Author: lvhongen
 * @Date: 2025-04-23 09:09
 * @Version： 1.0
 **/
@Mapper
@DS("guanwang")
public interface ALjqMapper extends BaseMapper<ALjq> {
    @Select("select code,ST_AsText(sgeom) as sgeom from gw.a_ljq")
    List<PointVo> getAllList();
    ALjqVo getByCode(String code);
}
