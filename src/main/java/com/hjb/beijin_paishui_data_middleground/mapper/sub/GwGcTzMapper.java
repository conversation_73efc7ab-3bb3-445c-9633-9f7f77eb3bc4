package com.hjb.beijin_paishui_data_middleground.mapper.sub;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw.GwGcTz;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw.GwWells;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainAncillaryFacilities;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical.SyntheticalPipelineOperation;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @Description: 设施台账表
 * @Author: lvhongen
 * @Date: 2025-03-19 22:14
 * @Version： 1.0
 **/
@DS("guanwang")
@Mapper
public interface GwGcTzMapper extends BaseMapper<GwGcTz> {
}
