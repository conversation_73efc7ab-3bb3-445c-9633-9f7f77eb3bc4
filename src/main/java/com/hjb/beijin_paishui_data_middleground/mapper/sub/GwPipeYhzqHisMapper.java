package com.hjb.beijin_paishui_data_middleground.mapper.sub;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw.GwPipeYhzqHis;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * @Description: 养护周期历史记录Mapper
 * @Author: lvhongen
 * @Date: 2025-03-19 23:16
 * @Version： 1.0
 **/
@DS("guanwang")
@Mapper
public interface GwPipeYhzqHisMapper extends BaseMapper<GwPipeYhzqHis> {
    @Select("SELECT  ROUND(SUM(gw_pipe.pipelength)/1000,2) AS amount_of_curing " +
            "FROM yy.gw_pipe_yhzq_his " +
            "JOIN gw.gw_pipe ON yy.gw_pipe_yhzq_his.yhzq_gddm = gw.gw_pipe.code")
    Double getAmountOfCuring();

    @Select("SELECT  ROUND(SUM(gw_pipe.pipelength)/1000,2) AS detecting_mileage " +
            "FROM yy.cctv " +
            "JOIN gw.gw_pipe ON yy.cctv.cctv_gddm = gw.gw_pipe.code")
    Double getDetectingMileage();

    @Select("SELECT  ROUND(SUM(gw_pipe.pipelength)/1000,2) AS detecting_mileage " +
            "FROM yy.cctv " +
            "JOIN gw.gw_pipe ON yy.cctv.cctv_gddm = gw.gw_pipe.code")
    Double getInspectionMileage();
}
