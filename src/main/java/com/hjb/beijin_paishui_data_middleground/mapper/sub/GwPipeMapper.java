package com.hjb.beijin_paishui_data_middleground.mapper.sub;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.dto.GwPipeSeachDto;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.GwPipeAndGzTzAndGwYyTzInfo;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.GwPipeVo;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.PointVo;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw.GwPipe;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw.GwWells;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Description: 管线数据表
 * @Author: lvhongen
 * @Date: 2025-03-19 22:14
 * @Version： 1.0
 **/
@DS("guanwang")
@Mapper
public interface GwPipeMapper extends BaseMapper<GwPipe> {
    /**
     * 计算管网总里程
     * @return
     */
    @Select("SELECT  ROUND(SUM(gw_pipe.pipelength)/1000,2) AS total_pipeline_length " +
            "FROM gw.gw_pipe " +
            "JOIN gw.gw_gc_tz ON gw.gw_pipe.gctz = gw.gw_gc_tz.code " +
            "WHERE gw.gw_pipe.syzt = 'using' AND gw.gw_gc_tz.sfqs = 'y'")
    Double getTotalPipelineLength();

    /**
     * 计算雨水管网总里程
     * @return
     */
    @Select("SELECT ROUND(SUM(gw_pipe.pipelength)/1000,2) AS rainwater_pipeline_length " +
            "FROM gw.gw_pipe " +
            "JOIN gw.gw_gc_tz ON gw.gw_pipe.gctz = gw.gw_gc_tz.code " +
            "WHERE gw.gw_pipe.syzt = 'using' AND gw.gw_gc_tz.sfqs = 'y' AND gw.gw_pipe.syxz = 'ys'")
    Double getRainwaterPipelineLength();
    /**
     * 计算污水
     * @return
     */
    @Select("SELECT ROUND(SUM(gw_pipe.pipelength)/1000,2) AS sewage_pipeline_length " +
            "FROM gw.gw_pipe " +
            "JOIN gw.gw_gc_tz ON gw.gw_pipe.gctz = gw.gw_gc_tz.code " +
            "WHERE gw.gw_pipe.syzt = 'using' AND gw.gw_gc_tz.sfqs = 'y' AND gw.gw_pipe.syxz = 'ws'")
    Double getSewagePipelineLength();

    /**
     * 计算合流长度
     * @return
     */
    @Select("SELECT ROUND(SUM(gw_pipe.pipelength)/1000,2) AS sewage_pipeline_length " +
            "FROM gw.gw_pipe " +
            "JOIN gw.gw_gc_tz ON gw.gw_pipe.gctz = gw.gw_gc_tz.code " +
            "WHERE gw.gw_pipe.syzt = 'using' AND gw.gw_gc_tz.sfqs = 'y' AND gw.gw_pipe.syxz = 'hl'")
    Double getCombinedSewerPipelineLength ();

    @Select("SELECT code, gctz, gzw, syzt, syxz, bzs FROM gw.gw_well WHERE syzt = 'using'")
    List<GwWells> getList();
    @Select("select count(1) from gw.gw_pipe where xyjdm = #{code}")
    int getTest (String code);

    @Select("select gp.code,ST_AsText(gp.sgeom) as sgeom  " +
            "FROM gw.gw_pipe gp JOIN gw.gw_gc_tz tz on gp.gctz = tz.code where gp.syxz = #{syxz} " +
            "and gp.syzt = 'using' and tz.sfqs = 'y'")
    List<PointVo> getAllList(String syxz);

    /**
     * 通过管网id查询管网详细信息
     * @param code
     * @return
     */
    GwPipeAndGzTzAndGwYyTzInfo getByCode(String code);

    /**
     * 通过条件查询所有管网详细信息
     * @param gwPipeSeachDto
     * @return
     */

    IPage<GwPipeAndGzTzAndGwYyTzInfo> seachGwPipePoint (Page<GwPipeAndGzTzAndGwYyTzInfo> page, GwPipeSeachDto gwPipeSeachDto);

    /**
     * 获取畅通管道中newCalGndj为1或者2的管道管长
     * @return
     */
    Double getnormalOperationRate();
    /**
     * 获取畅通管道中所有管道管长
     * @return
     */
    Double getnormalOperationRateAll ();



}
