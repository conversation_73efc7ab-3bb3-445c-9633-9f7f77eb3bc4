package com.hjb.beijin_paishui_data_middleground.mapper.sub;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.ps.V3dDrainOrderReply;
import org.apache.ibatis.annotations.Mapper;

/**
 * @Description: 处置记录Mapper
 * @Author: lvhongen
 * @Date: 2025-03-23 18:55
 * @Version： 1.0
 **/
@DS("paishui")
@Mapper
public interface V3dDrainOrderReplyMapper extends BaseMapper<V3dDrainOrderReply> {
}
