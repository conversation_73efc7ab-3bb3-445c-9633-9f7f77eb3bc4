package com.hjb.beijin_paishui_data_middleground.mapper.sub;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.PointVo;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.SyntheticaSelectData;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw.ADept;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw.AYsMain;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Description: 管网公司字典表
 * @Author: lvhongen
 * @Date: 2025-04-21 14:47
 * @Version： 1.0
 **/
@Mapper
@DS("guanwang")
public interface ADeptMapper extends BaseMapper<ADept> {
    /**
     * 查询行分公司列表信息
     * @return
     */
    @Select("select name,code as value from gw.a_dept")
    List<SyntheticaSelectData.SelectData> getSyntheticaInteractiveSelect ();
    @Select("select code,name,ST_AsText(sgeom) as sgeom from gw.a_dept")
    List<PointVo> getAllList();
    @Select("select code,name,ST_AsText(sgeom) as sgeom from gw.a_dept where code = #{code}")
    ADept getByCode(String code);
}
