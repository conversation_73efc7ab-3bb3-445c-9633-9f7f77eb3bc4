package com.hjb.beijin_paishui_data_middleground.mapper.sub;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.drain.DrainPipeNetworkTestingEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainPipeNetworkTesting;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * @Description: 管网检测Mapper
 * @Author: lvhongen
 * @Date: 2025-04-08 11:41
 * @Version： 1.0
 **/
@DS("guanwang")
@Mapper
public interface PipeNetworkTestingMapper {
    DrainPipeNetworkTesting getPipeNetworkTesting();
    List<Map<String, Object>> getPipeNetworkTestingChart();
}
