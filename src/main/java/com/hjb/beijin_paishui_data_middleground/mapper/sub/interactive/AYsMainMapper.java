package com.hjb.beijin_paishui_data_middleground.mapper.sub.interactive;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.PointVo;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw.AWsMain;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw.AYsMain;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Description: 雨水主干Mapper
 * @Author: lvhongen
 * @Date: 2025-04-23 08:45
 * @Version： 1.0
 **/
@DS("guanwang")
@Mapper
public interface AYsMainMapper extends BaseMapper<AWsMain> {
    @Select("select code,ST_AsText(sgeom) as sgeom from gw.a_ys_main")
    List<PointVo> getAllList();
    @Select("select code,name,ST_AsText(sgeom) as sgeom from gw.a_ys_main where code = #{code}")
    AYsMain getByCode(String code);
}
