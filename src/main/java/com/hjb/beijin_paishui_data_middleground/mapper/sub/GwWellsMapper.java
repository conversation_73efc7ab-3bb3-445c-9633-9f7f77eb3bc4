package com.hjb.beijin_paishui_data_middleground.mapper.sub;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.dto.GwWellSeachDto;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.GwPipeVo;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.GwWellAndGzTzAndGwYyTzInfo;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.GwWellVo;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.PointVo;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw.GwWells;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainAncillaryFacilities;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical.SyntheticalPipelineOperation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Description: 管井数据表
 * @Author: lvhongen
 * @Date: 2025-03-19 22:14
 * @Version： 1.0
 **/

@DS("guanwang")
@Mapper
public interface GwWellsMapper extends BaseMapper<GwWells> {
    /**
     *通过构筑物类型查询管井数量
     * @return
     */
    SyntheticalPipelineOperation getListByGwGcTzCode();

    /**
     * 通过使用性质查询管井数量
     * @return
     */
    DrainAncillaryFacilities getListByGwGcTzCodeSeachSyxzCount();
    List<GwWells> getListByGwGcTzCodeS();

    /**
     * 通过构筑物类型查询管井
     * @param gzw
     * @return
     */
    @Select("select gwl.code,ST_AsText(gwl.sgeom) as sgeom  " +
            "FROM gw.gw_well gwl JOIN gw.gw_gc_tz tz on gwl.gctz = tz.code where gwl.gzw = #{gzw} " +
            "and gwl.syzt = 'using' and tz.sfqs = 'y'")
    List<PointVo> getAllList(String gzw);

    GwWellAndGzTzAndGwYyTzInfo getByCode(String code);

    IPage<GwWellAndGzTzAndGwYyTzInfo> seachGwWellPoint (Page<GwWellAndGzTzAndGwYyTzInfo> page, GwWellSeachDto gwWellSeachDto);

    /**
     * 通过构筑物类型查询数量
     * @param gzw
     * @return
     */
    @Select("select count(1)  " +
            "FROM gw.gw_well gwl JOIN gw.gw_gc_tz tz on gwl.gctz = tz.code where gwl.gzw = #{gzw} " +
            "and gwl.syzt = 'using' and tz.sfqs = #{sfqs}")
    long getCountByGwz (String gzw,String sfqs);
}
