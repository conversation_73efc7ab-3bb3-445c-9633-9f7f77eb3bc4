package com.hjb.beijin_paishui_data_middleground.mapper.sub.interactive;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.PointVo;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw.APlantall;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw.AScope;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Description: 再生水厂Mapper
 * @Author: lvhongen
 * @Date: 2025-04-23 09:13
 * @Version： 1.0
 **/
@Mapper
@DS("guanwang")
public interface APlantallMapper extends BaseMapper<APlantall> {
    @Select("select code,name,ST_AsText(sgeom) as sgeom from gw.a_plantall")
    List<PointVo> getAllList ();
    @Select("select code,name,ST_AsText(sgeom) as sgeom from gw.a_plantall where code = #{code}")
    APlantall getByCode(String code);
}
