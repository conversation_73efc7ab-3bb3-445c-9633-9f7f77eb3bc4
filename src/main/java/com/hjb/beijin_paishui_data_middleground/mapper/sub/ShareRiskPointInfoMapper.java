package com.hjb.beijin_paishui_data_middleground.mapper.sub;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.fx.ShareRiskPointInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * @Description: 【防汛】风险点Mapper
 * @Author: lvhongen
 * @Date: 2025-03-22 16:42
 * @Version： 1.0
 **/
@DS("fangxun")
@Mapper
public interface ShareRiskPointInfoMapper extends BaseMapper<ShareRiskPointInfo> {
    @Select("SELECT comnm FROM FXYJ.SHARE_RISK_POINT_INFO GROUP BY comnm")
    public List<String> selectDistinctByComnm ();
    @Select("SELECT adnm FROM FXYJ.SHARE_RISK_POINT_INFO GROUP BY adnm")
    public List<String> selectDistinctByAdnm ();
}
