package com.hjb.beijin_paishui_data_middleground.mapper.sub.interactive;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.PointVo;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw.AWsly;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw.AYsly;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Description: 雨水流域Mapper
 * @Author: lvhongen
 * @Date: 2025-04-25 11:16
 * @Version： 1.0
 **/
@Mapper
@DS("guanwang")
public interface AYslyMapper extends BaseMapper<AYsly> {
    @Select("select code,ST_AsText(sgeom) as sgeom from gw.a_a_ysly")
    List<PointVo> getAllList();

    @Select("select code,name,ST_AsText(sgeom) as sgeom from gw.a_a_ysly where code = #{code}")
    AYsly getByCode(String code);
}
