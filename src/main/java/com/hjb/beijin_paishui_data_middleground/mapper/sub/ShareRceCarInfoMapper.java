package com.hjb.beijin_paishui_data_middleground.mapper.sub;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.CarAndUnitVo;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.fx.ShareRceCarInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Description: 车辆信息与呼号信息关联视图Mapper
 * @Author: lvhongen
 * @Date: 2025-04-01 17:48
 * @Version： 1.0
 **/
@DS("fangxun")
@Mapper
public interface ShareRceCarInfoMapper extends BaseMapper<ShareRceCarInfo> {
    List<CarAndUnitVo> getVehicleCallSignInformation(String carNo);
}
