package com.hjb.beijin_paishui_data_middleground.mapper.sub;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw.ScPlan;
import io.swagger.models.auth.In;
import lombok.NonNull;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * @Description: 生产计划Mapper
 * @Author: lvhongen
 * @Date: 2025-04-28 10:11
 * @Version： 1.0
 **/
@DS("guanwang")
@Mapper
public interface ScPlanMapper extends BaseMapper<ScPlan> {
    List<Map> getQueryQuantityByMonth(@NonNull LocalDate oneYearAgo, @NonNull LocalDate now, String status, String syxz);
    Double getScPlanLength(@NonNull LocalDate oneYearAgo, @NonNull LocalDate now);
    //计算出泥量
    Double getMudOutput(@NonNull LocalDate oneYearAgo, @NonNull LocalDate now,@NonNull String syxz);

}
