package com.hjb.beijin_paishui_data_middleground.mapper.sub.interactive;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.AWsXlyVo;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.AXunchaVo;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.PointVo;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw.AXuncha;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Description: 巡查范围Mapper
 * @Author: lvhongen
 * @Date: 2025-04-23 09:05
 * @Version： 1.0
 **/
@Mapper
@DS("guanwang")
public interface AXunchaMapper extends BaseMapper<AXuncha> {
    @Select("select code,ST_AsText(sgeom) as sgeom from gw.a_xuncha")
    List<PointVo> getAllList();
    AXunchaVo getByCode(String code);
}
