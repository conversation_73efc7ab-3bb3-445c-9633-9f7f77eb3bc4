package com.hjb.beijin_paishui_data_middleground.mapper.sub;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.fx.SharePodInfo;
import io.swagger.models.auth.In;
import lombok.NonNull;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * @Description: 积水点Mapper
 * @Author: lvhongen
 * @Date: 2025-04-10 17:05
 * @Version： 1.0
 **/
@Mapper
@DS("fangxun")
public interface SharePodInfoMapper extends BaseMapper<SharePodInfo> {
    /**
     * 通过时间去分组查询近一年处理状态为2已经处理的个数并且按照1~12月统计
     * @return
     */
    List<Map<String, Object>> getListByOrderByMonthCount(@NonNull LocalDateTime oneYearAgo, @NonNull LocalDateTime now);
}
