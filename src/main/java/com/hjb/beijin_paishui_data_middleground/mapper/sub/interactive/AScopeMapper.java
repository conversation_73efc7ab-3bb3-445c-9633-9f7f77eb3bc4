package com.hjb.beijin_paishui_data_middleground.mapper.sub.interactive;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.PointVo;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw.AScope;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw.AXzj;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Description: 桥区建模范围Mapper
 * @Author: lvhongen
 * @Date: 2025-04-22 16:41
 * @Version： 1.0
 **/
@Mapper
@DS("guanwang")
public interface AScopeMapper extends BaseMapper<AScope> {
    @Select("select code,ST_AsText(sgeom) as sgeom from gw.a_scope")
    List<PointVo> getAllList ();
    @Select("select code,name,ST_AsText(sgeom) as sgeom from gw.a_scope where code = #{code}")
    AScope getByCode(String code);
}
