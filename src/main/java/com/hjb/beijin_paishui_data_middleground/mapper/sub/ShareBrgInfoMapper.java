package com.hjb.beijin_paishui_data_middleground.mapper.sub;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.fx.ShareBrgInfo;
import com.hjb.beijin_paishui_data_middleground.entity.sub.vo.ShareBrgInfoVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * @Description: 【防汛】下凹桥区Mapper
 * @Author: lvhongen
 * @Date: 2025-03-22 16:19
 * @Version： 1.0
 **/
@DS("fangxun")
@Mapper
public interface ShareBrgInfoMapper extends BaseMapper<ShareBrgInfo> {
    /**
     * 按 comnm 和 adnm 字段分组去重查询
     * @return 包含分组去重结果的 List<Map<String, Object>>
     */
    @Select("SELECT comnm FROM FXYJ.SHARE_BRG_INFO GROUP BY comnm")
    List<String> selectDistinctByComnm();
    @Select("SELECT adnm FROM FXYJ.SHARE_BRG_INFO GROUP BY adnm")
    List<String> selectDistinctByAdnm();
    @Select("SELECT br_code,lttd,lgtd FROM FXYJ.SHARE_BRG_INFO")
    List<ShareBrgInfoVo> getAllList ();
}
