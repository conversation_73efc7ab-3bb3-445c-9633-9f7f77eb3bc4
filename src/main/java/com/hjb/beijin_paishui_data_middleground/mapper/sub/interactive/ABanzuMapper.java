package com.hjb.beijin_paishui_data_middleground.mapper.sub.interactive;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.ABanzuVo;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.PointVo;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw.ABanzu;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw.AWsMain;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Description: 运行班Mapper
 * @Author: lvhongen
 * @Date: 2025-04-23 08:50
 * @Version： 1.0
 **/
@Mapper
@DS("guanwang")
public interface ABanzuMapper extends BaseMapper<ABanzu> {
    @Select("select code,name,ST_AsText(sgeom) as sgeom from gw.a_banzu")
    List<PointVo> getAllList();
    ABanzuVo getByCode(String code);
}
