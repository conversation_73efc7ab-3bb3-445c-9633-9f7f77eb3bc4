package com.hjb.beijin_paishui_data_middleground.config;

import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.plugin.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.Statement;
import java.util.Properties;

// 自定义拦截器，实现 MyBatis 的 Interceptor 接口
@Intercepts({
        @Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class})
})
public class RealDatabaseNameInterceptor implements Interceptor, InnerInterceptor {
    private static final Logger logger = LoggerFactory.getLogger(RealDatabaseNameInterceptor.class);

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        // 获取 StatementHandler 对象
        StatementHandler statementHandler = (StatementHandler) invocation.getTarget();
        // 获取 BoundSql 对象，包含执行的 SQL 语句
        BoundSql boundSql = statementHandler.getBoundSql();
        String sql = boundSql.getSql();
        // 从拦截方法的参数中获取 Connection 对象
        Connection connection = (Connection) invocation.getArgs()[0];
        // 获取当前连接的数据库名
        String databaseName = connection.getCatalog();
        // 输出操作的数据库名和 SQL 语句到日志
        logger.info("Operating real database: {}, SQL: {}", databaseName, sql);
        return invocation.proceed();
    }

    @Override
    public Object plugin(Object target) {
        if (target instanceof StatementHandler) {
            return Plugin.wrap(target, this);
        }
        return target;
    }

    @Override
    public void setProperties(Properties properties) {
        // 可在此设置拦截器的属性
    }
}
