package com.hjb.beijin_paishui_data_middleground.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * Knife4j 接口文档配置
 *
 * <AUTHOR>
 * @create 2022-07-04-17:26
 */
@Configuration
@EnableSwagger2
public class Knife4jConfiguration{

    @Bean
    public Docket createRestApi1() {
        // 指定api类型为swagger2
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .groupName("数据填报平台 API")
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.hjb.beijin_paishui_data_middleground.controller.manage"))
                .paths(PathSelectors.any())
                .build();
    }

    @Bean
    public Docket createRestApi2() {
        // 指定api类型为swagger2
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .groupName("数字孪生数据中台 API")
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.hjb.beijin_paishui_data_middleground.controller.szls"))
                .paths(PathSelectors.any())
                .build();
    }
    @Bean
    public Docket createRestApi3() {
        // 指定api类型为swagger2
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .groupName("数字孪生数据中台 真实数据API")
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.hjb.beijin_paishui_data_middleground.controller.authenticSzls"))
                .paths(PathSelectors.any())
                .build();
    }
    @Bean
    public Docket createRestApi4() {
        // 指定api类型为swagger2
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .groupName("数字孪生数据中台 交互API")
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.hjb.beijin_paishui_data_middleground.controller.interactive"))
                .paths(PathSelectors.any())
                .build();
    }
    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("北京排水集团数字孪生 API")
                .contact(new Contact("温旺鑫", "<EMAIL>", "<EMAIL>"))
                .version("1.0.0")
                .description("北京排水集团数字孪生数据中台 API")
                .build();
    }
}
