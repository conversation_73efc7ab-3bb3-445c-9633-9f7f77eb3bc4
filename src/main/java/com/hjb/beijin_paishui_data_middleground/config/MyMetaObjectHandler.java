package com.hjb.beijin_paishui_data_middleground.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * MP字段自动填充
 *
 * <AUTHOR>
 */
@Component
public class MyMetaObjectHandler implements MetaObjectHandler {


    @Override
    public void insertFill(MetaObject metaObject) {
        //fieldName是属性名称，不是数据库的字段名称
        this.setFieldValByName("createTime", String.valueOf(new Date().getTime()), metaObject);
        this.setFieldValByName("updateTime", String.valueOf(new Date().getTime()), metaObject);
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        this.setFieldValByName("updateTime", String.valueOf(new Date().getTime()), metaObject);
    }
}
