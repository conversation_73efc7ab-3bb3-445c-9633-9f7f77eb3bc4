package com.hjb.beijin_paishui_data_middleground.common.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 通用分页对象
 *
 * <AUTHOR>
 * @create 2022-07-04-16:30
 */
@Data
@ApiModel("通用分页对象")
public class CommonPage {

    @ApiModelProperty(name = "currentPage", value = "当前页，默认1", example = "1")
    private Long currentPage = 1L;

    @ApiModelProperty(name = "pageSize", value = "每页数量，默认10", example = "10")
    private Long pageSize = 10L;

}
