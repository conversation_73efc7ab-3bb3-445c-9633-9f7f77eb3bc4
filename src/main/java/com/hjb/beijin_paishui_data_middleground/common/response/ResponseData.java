package com.hjb.beijin_paishui_data_middleground.common.response;

import lombok.Data;

import java.io.Serializable;

/**
 * 响应数据
 *
 * <AUTHOR>
 * @create 2022-07-04-16:44
 */
@Data
public class ResponseData<T> implements Serializable {
    private Integer code;
    private String message;
    private T data;

    public ResponseData() {

    }

    private ResponseData(Builder<T> builder) {
        this.code = builder.code;
        this.message = builder.message;
        this.data = builder.data;
    }

    public ResponseData(ResponseCode responseCode) {
        this.data = null;
        this.code = responseCode.getCode();
        this.message = responseCode.getMsg();
    }

    public static <T> ResponseData<T> ok(T data) {
        ResponseData<T> res = new ResponseData<>();
        res.setData(data);
        res.setCode(ResponseCode.SUCCESS.getCode());
        res.setMessage(ResponseCode.SUCCESS.getMsg());
        return res;
    }

    public static <T> ResponseData<T> warn(String message) {
        ResponseData<T> res = new ResponseData<>();
        res.setData(null);
        res.setCode(ResponseCode.WARN.getCode());
        res.setMessage(message);
        return res;
    }

    public static <T> ResponseData<T> error(String message) {
        ResponseData<T> res = new ResponseData<>();
        res.setData(null);
        res.setCode(ResponseCode.FAILURE.getCode());
        res.setMessage(message);
        return res;
    }

    public static <T> ResponseData<T> error(ResponseCode responseCode, String message) {
        ResponseData<T> res = new ResponseData<>();
        res.setData(null);
        res.setCode(responseCode.getCode());
        res.setMessage(message);
        return res;
    }

    public static <T> ResponseData<T> error(int code, String message) {
        ResponseData<T> res = new ResponseData<>();
        res.setData(null);
        res.setCode(code);
        res.setMessage(message);
        return res;
    }

    public static <T> ResponseData<T> serverFail(String message) {
        ResponseData<T> res = new ResponseData<>();
        res.setData(null);
        res.setCode(ResponseCode.SERVER_ERR.getCode());
        res.setMessage(message);
        return res;
    }

    public static class Builder<T> {
        private int code;
        private String message;
        private T data;

        public Builder<T> code(int code) {
            this.code = code;
            return this;
        }

        public Builder<T> message(String message) {
            this.message = message;
            return this;
        }

        public Builder<T> data(T data) {
            this.data = data;
            return this;
        }

        public ResponseData<T> build() {
            return new ResponseData<>(this);
        }
    }

}
