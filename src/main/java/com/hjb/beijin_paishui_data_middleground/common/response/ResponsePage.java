package com.hjb.beijin_paishui_data_middleground.common.response;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.List;

/**
 * 分页响应对象
 *
 * <AUTHOR>
 * @create 2022-07-04-17:02
 */
@Data
@ApiModel("分页响应对象")
public class ResponsePage<T> {

    @ApiModelProperty(name = "currentPage", value = "当前页", example = "1")
    private Long currentPage;

    @ApiModelProperty(name = "pageSize", value = "一页显示多少数据", example = "10")
    private Long pageSize;

    @ApiModelProperty(name = "content", value = "数据集合", example = "[{},{}]")
    private List<T> content;

    @ApiModelProperty(name = "total", value = "总计有多少条数据", example = "10")
    private Long total;

    /**
     * 将mp的IPage转换为我们自定义的page对象
     *
     * @param page IPage
     * @param <T>  对象
     * @return 自定义page对象
     */
    public static <T> ResponsePage<T> transform(IPage<T> page) {
        ResponsePage<T> respPage = new ResponsePage<>();
        respPage.setPageSize(page.getSize());
        respPage.setCurrentPage(page.getCurrent());
        respPage.setTotal(page.getTotal());
        respPage.setContent(page.getRecords());
        return respPage;
    }

    /**
     * 从list获取分页对象
     *
     * @param list 结果列表
     * @param page PO分页对象
     * @param <T>  PO class
     * @param <D>  结果 class
     * @return 分页对象
     */
    public static <T, D> ResponsePage<D> formList(List<D> list, IPage<T> page) {
        ResponsePage<D> respPage = new ResponsePage<>();
        respPage.setPageSize(page.getSize());
        respPage.setCurrentPage(page.getCurrent());
        respPage.setTotal(page.getTotal());
        respPage.setContent(list);
        return respPage;
    }
}
