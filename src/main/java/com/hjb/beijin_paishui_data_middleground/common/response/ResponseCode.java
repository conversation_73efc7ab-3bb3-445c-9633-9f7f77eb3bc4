package com.hjb.beijin_paishui_data_middleground.common.response;

import lombok.Getter;
import lombok.ToString;

/**
 * 返回状态码
 *
 * <AUTHOR>
 * @create 2022-07-04-16:44
 */

@Getter
@ToString
public enum ResponseCode {

    /**
     * 成功
     */
    SUCCESS(0, "SUCCESS"),
    /**
     * 失败
     */
    FAILURE(-1, "FAILURE"),
    /**
     * 参数错误
     */
    PARAM_ERR(-2, "param error"),
    /**
     * 没有权限
     */
    AUTH_ERR(-3, "authentication error"),
    /**
     * JSON解析错误
     */
    JSON_ERR(-4, "Json analysis error"),
    /**
     * 服务器错误
     */
    SERVER_ERR(-10, "server error"),
    /**
     * 警告
     */
    WARN(1, "warning");

    /**
     * 状态码
     */
    private final int code;
    /**
     * 信息
     */
    private final String msg;

    ResponseCode(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
