package com.hjb.beijin_paishui_data_middleground.common.validation;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.lang.reflect.Field;
import java.util.Date;

/**
 * 日期时间范围校验器
 * 
 * <AUTHOR>
 * @date 2025/1/20
 */
public class DateTimeRangeValidator implements ConstraintValidator<DateTimeRange, Object> {
    
    private String startField;
    private String endField;
    private String message;
    
    @Override
    public void initialize(DateTimeRange constraintAnnotation) {
        this.startField = constraintAnnotation.startField();
        this.endField = constraintAnnotation.endField();
        this.message = constraintAnnotation.message();
    }
    
    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        if (value == null) {
            return true;
        }
        
        try {
            Field startFieldObj = value.getClass().getDeclaredField(startField);
            Field endFieldObj = value.getClass().getDeclaredField(endField);
            
            startFieldObj.setAccessible(true);
            endFieldObj.setAccessible(true);
            
            Date startTime = (Date) startFieldObj.get(value);
            Date endTime = (Date) endFieldObj.get(value);
            
            // 如果任一时间为null，由其他校验注解处理
            if (startTime == null || endTime == null) {
                return true;
            }
            
            // 校验开始时间是否小于结束时间
            boolean isValid = startTime.before(endTime);
            
            if (!isValid) {
                // 自定义错误消息
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate(message)
                       .addPropertyNode(startField)
                       .addConstraintViolation();
            }
            
            return isValid;
            
        } catch (Exception e) {
            return false;
        }
    }
}
