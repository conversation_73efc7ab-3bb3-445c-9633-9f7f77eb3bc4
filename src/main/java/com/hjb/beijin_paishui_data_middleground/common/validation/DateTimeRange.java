package com.hjb.beijin_paishui_data_middleground.common.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 日期时间范围校验注解
 * 用于校验类级别的开始时间和结束时间关系
 * 
 * <AUTHOR>
 * @date 2025/1/20
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = DateTimeRangeValidator.class)
public @interface DateTimeRange {
    
    /**
     * 错误消息
     */
    String message() default "开始时间必须小于结束时间";
    
    /**
     * 开始时间字段名
     */
    String startField() default "startTime";
    
    /**
     * 结束时间字段名
     */
    String endField() default "endTime";
    
    /**
     * 分组
     */
    Class<?>[] groups() default {};
    
    /**
     * 负载
     */
    Class<? extends Payload>[] payload() default {};
}
