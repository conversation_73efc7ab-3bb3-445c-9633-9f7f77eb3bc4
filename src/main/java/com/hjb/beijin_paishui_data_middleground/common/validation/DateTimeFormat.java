package com.hjb.beijin_paishui_data_middleground.common.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 日期时间格式校验注解
 * 用于校验日期时间字符串是否符合指定格式
 * 
 * <AUTHOR>
 * @date 2025/1/20
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = DateTimeFormatValidator.class)
public @interface DateTimeFormat {
    
    /**
     * 错误消息
     */
    String message() default "日期时间格式不正确，请使用 yyyy-MM-dd HH:mm:ss 格式";
    
    /**
     * 日期时间格式模式
     */
    String pattern() default "yyyy-MM-dd HH:mm:ss";
    
    /**
     * 分组
     */
    Class<?>[] groups() default {};
    
    /**
     * 负载
     */
    Class<? extends Payload>[] payload() default {};
}
