package com.hjb.beijin_paishui_data_middleground.common.validation;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 日期时间格式校验器
 * 
 * <AUTHOR>
 * @date 2025/1/20
 */
public class DateTimeFormatValidator implements ConstraintValidator<DateTimeFormat, Date> {
    
    private String pattern;
    
    @Override
    public void initialize(DateTimeFormat constraintAnnotation) {
        this.pattern = constraintAnnotation.pattern();
    }
    
    @Override
    public boolean isValid(Date value, ConstraintValidatorContext context) {
        // 如果值为null，由@NotNull注解处理
        if (value == null) {
            return true;
        }
        
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(pattern);
            sdf.setLenient(false); // 严格模式
            
            // 将Date转换为字符串再解析，验证格式是否正确
            String dateStr = sdf.format(value);
            Date parsedDate = sdf.parse(dateStr);
            
            // 验证解析后的日期是否与原日期一致
            return Math.abs(value.getTime() - parsedDate.getTime()) < 1000; // 允许1秒误差
            
        } catch (ParseException e) {
            return false;
        }
    }
}
