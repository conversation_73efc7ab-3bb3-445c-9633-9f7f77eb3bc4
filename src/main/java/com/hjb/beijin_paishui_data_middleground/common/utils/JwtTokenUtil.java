package com.hjb.beijin_paishui_data_middleground.common.utils;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.hjb.beijin_paishui_data_middleground.common.constant.TokenConstant;

import java.util.Date;
import java.util.List;

/**
 * jwt 工具类
 *
 * <AUTHOR>
 * @create 2022-07-04-17:02
 */
public class JwtTokenUtil {

    /**
     * 携带token的请求头名字
     */
    public final static String TOKEN_HEADER = "Authorization";


    /**
     * 创建token
     *
     * @param username 用户名
     * @param secret   密钥
     * @return token
     */
    public static String createToken(String username, Integer userId, String role, List<String> permissions, String secret) {
        Date expireDate = new Date(System.currentTimeMillis() + TokenConstant.TOKEN_EXPIRE_TIME);
        Algorithm algorithm = Algorithm.HMAC256(secret);
        return JWT.create()
                .withExpiresAt(expireDate)
                .withClaim("username", username)
                .withClaim("userId", userId)
                .withClaim("role", role)
                .withClaim("permissions", permissions)
                .sign(algorithm);
    }

    /**
     * 验证token
     *
     * @param token  token值
     * @param secret 密钥
     * @return 是否成功
     */
    public static boolean verifyToken(String token, String secret) {
        try {
            Algorithm algorithm = Algorithm.HMAC256(secret);
            // 构建JWT验证器，token合法同时payload必须含有私有字段username且值一致
            // token过期也会验证失败
            JWTVerifier verifier = JWT.require(algorithm)
                    .build();
            // 验证token
            verifier.verify(token);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 根据token获取用户名
     *
     * @param token token值
     * @return 用户名
     */
    public static String getUsername(String token) {
        DecodedJWT jwt = JWT.decode(token);
        return jwt.getClaim("username").asString();
    }

    /**
     * 根据token获取用户角色
     *
     * @param token token值
     * @return 用户名
     */
    public static String getUserRole(String token) {
        DecodedJWT jwt = JWT.decode(token);
        return jwt.getClaim("role").asString();
    }

    /**
     * 获取用户id
     *
     * @param token token值
     * @return 用户id
     */
    public static Integer getUserId(String token) {
        DecodedJWT jwt = JWT.decode(token);
        return jwt.getClaim("userId").asInt();
    }


    /**
     * 获取权限列表
     *
     * @param token token值
     * @return 权限列表
     */
    public static List<String> getPermissions(String token) {
        DecodedJWT jwt = JWT.decode(token);
        return jwt.getClaim("permissions").asList(String.class);
    }

}
