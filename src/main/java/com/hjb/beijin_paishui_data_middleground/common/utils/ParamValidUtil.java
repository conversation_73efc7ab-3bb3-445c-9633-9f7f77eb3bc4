package com.hjb.beijin_paishui_data_middleground.common.utils;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;

import javax.validation.ValidationException;
import java.util.ArrayList;
import java.util.List;

/**
 * 参数校验工具
 *
 * <AUTHOR>
 * @create 2022-07-04-17:02
 */
public final class ParamValidUtil {

    public static void valid(BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            List<ParamValidError> errors = new ArrayList<>();
            List<FieldError> fieldErrors = bindingResult.getFieldErrors();
            for (FieldError error : fieldErrors) {
                ParamValidError err = new ParamValidError();
                err.setKey(error.getField());
                err.setMsg(error.getDefaultMessage());
                errors.add(err);
            }
            throw new ValidationException(JsonUtil.toJsonStr(errors));
        }
    }

    @Getter
    @Setter
    @ToString
    public static class ParamValidError {
        private String key;
        private String msg;
    }
}
