package com.hjb.beijin_paishui_data_middleground.common.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.*;

@Slf4j
public class CustomThreadPool {

    // 获取 CPU 核心数
    private static final int CPU_CORES = Runtime.getRuntime().availableProcessors();

    // 自定义线程池
    public static final ThreadPoolExecutor THREAD_POOL = new ThreadPoolExecutor(
            CPU_CORES + 1,                         // 核心线程数
            CPU_CORES * 2,                         // 最大线程数
            60L,                                   // 非核心线程存活时间
            TimeUnit.MINUTES,                      // 存活时间单位
            new LinkedBlockingQueue<>(1000),       // 有界任务队列
            Executors.defaultThreadFactory(),      // 默认线程工厂
            new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略：由调用线程处理任务
    );

    public static void shutdownThreadPool(ThreadPoolExecutor threadPool) {
        ScheduledExecutorService monitorExecutor = Executors.newSingleThreadScheduledExecutor();

        // 每秒检查线程池状态
        monitorExecutor.scheduleAtFixedRate(() -> {
            int activeTasks = threadPool.getActiveCount();
            int queuedTasks = threadPool.getQueue().size();

            log.info("正在处理的任务: {}", activeTasks);
            log.info("队列中等待的任务: {}", queuedTasks);

            // 如果线程池任务全部完成且未关闭，开始关闭
            if (activeTasks == 0 && queuedTasks == 0 && !threadPool.isShutdown()) {
                log.info("没有任务,线程池关闭");
                threadPool.shutdown();

                try {
                    // 等待线程池任务完成
                    if (threadPool.awaitTermination(5, TimeUnit.SECONDS)) {
                        log.info("线程池正常关闭");
                    } else {
                        log.warn("线程池强制关闭");
                        threadPool.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    log.error("线程池终止中断");
                    threadPool.shutdownNow();
                    Thread.currentThread().interrupt();
                }

                // 停止监控线程池状态
                monitorExecutor.shutdown();
            }
        }, 0, 1, TimeUnit.SECONDS);
    }

    public static void main(String[] args) {
        // 提交任务到线程池
        for (int i = 0; i < 100; i++) {
            int taskId = i;
            THREAD_POOL.execute(() -> {
                System.out.println("Executing Task: " + taskId + " by Thread: " + Thread.currentThread().getName());
                try {
                    Thread.sleep(1000); // 模拟任务执行
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
        }

        // 优雅关闭线程池
        shutdownThreadPool(THREAD_POOL);
    }
}
