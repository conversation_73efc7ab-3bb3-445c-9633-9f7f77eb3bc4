package com.hjb.beijin_paishui_data_middleground.common.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.hjb.beijin_paishui_data_middleground.common.advice.CustomException;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseCode;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * 基于FastJson封装JSON
 *
 * <AUTHOR>
 */
@Slf4j
public class JsonUtil {

    /**
     * 将一个对象装换为Json字符串
     */
    public static String toJsonStr(Object object) {
        try {
            return JSONObject.toJSONString(object);
        } catch (Exception e) {
            log.error("", e);
            throw new CustomException(ResponseCode.JSON_ERR, "JSONUtil toJsonString Exception");
        }
    }

    /**
     * 将Json字符串转换为Object类型的
     */
    public static Object toObject(String str) {
        try {
            return JSON.parse(str);
        } catch (Exception e) {
            log.error("", e);
            throw new CustomException(ResponseCode.JSON_ERR, "JSONUtil toObject Exception");
        }
    }

    /**
     * 将Json字符串转换为实例
     */
    public static <T> T toObject(String str, Class<T> t) {
        try {
            return JSON.parseObject(str, t);
        } catch (Exception e) {
            log.error("", e);
            throw new CustomException(ResponseCode.JSON_ERR, "JSONUtil toObject living Exception");
        }
    }

    /**
     * 将Json转换为指定类型的List
     */
    public static <T> List<T> toList(String str, Class<T> t) {
        try {
            return JSON.parseArray(str, t);
        } catch (Exception e) {
            log.error("", e);
            throw new CustomException(ResponseCode.JSON_ERR, "JSONUtil toList Exception");
        }
    }

    /**
     * 将Json转换为Map
     */
    public static Map<String, Object> toMap(String str) {
        try {
            return JSONObject.parseObject(str);
        } catch (Exception e) {
            log.error("", e);
            throw new CustomException(ResponseCode.JSON_ERR, "JSONUtil toMap Exception");
        }
    }
}

