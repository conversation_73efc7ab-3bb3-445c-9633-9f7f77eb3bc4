package com.hjb.beijin_paishui_data_middleground.common.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseCode;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 */
public class WebUtil {
    /**
     * 将字符串渲染到客户端
     *
     * @param response 渲染对象
     * @param string   待渲染的字符串
     * @return null
     */
    public static void writeStr(HttpServletResponse response, String string) {
        try {
            response.setStatus(200);
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().print(string);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 写出结果为json
     *
     * @param data     信息
     * @param response 响应
     * @throws Exception 异常
     */
    public static void writeToJson(String data, HttpServletResponse response) throws Exception {
        ResponseData<String> responseData = new ResponseData<>();
        responseData.setCode(ResponseCode.AUTH_ERR.getCode());
        responseData.setMessage(ResponseCode.AUTH_ERR.getMsg());
        responseData.setData(data);
        response.setCharacterEncoding("utf-8");
        response.setContentType("text/json;charset=UTF-8");
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.writeValue(response.getWriter(), responseData);
    }
}