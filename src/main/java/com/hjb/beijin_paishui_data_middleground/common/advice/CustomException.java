package com.hjb.beijin_paishui_data_middleground.common.advice;

import com.hjb.beijin_paishui_data_middleground.common.response.ResponseCode;
import lombok.Getter;
import lombok.Setter;

/**
 * 自定义异常类
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class CustomException extends RuntimeException {

    private final int code;

    public CustomException(int code, String message) {
        super(message);
        this.code = code;
    }

    public CustomException(ResponseCode responseCode, String message) {
        super(message);
        this.code = responseCode.getCode();
    }

    public CustomException(String message) {
        super(message);
        this.code = ResponseCode.FAILURE.getCode();
    }

}
