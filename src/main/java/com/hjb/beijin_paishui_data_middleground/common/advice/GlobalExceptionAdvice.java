package com.hjb.beijin_paishui_data_middleground.common.advice;

import com.hjb.beijin_paishui_data_middleground.common.response.ResponseCode;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 全局异常处理
 *
 * <AUTHOR>
 */
@RestControllerAdvice
@Slf4j
public class GlobalExceptionAdvice {

    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResponseData<String> handleHttpMessageNotReadableException(HttpMessageNotReadableException e) {
        log.warn("参数解析失败：{}", e.getMessage());
        return new ResponseData.Builder<String>()
                .message(ResponseCode.PARAM_ERR.getMsg())
                .code(ResponseCode.PARAM_ERR.getCode())
                .data("参数解析失败：" + e.getMessage()).build();
    }

    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(CustomException.class)
    public ResponseData<String> handleHttpMessageNotReadableException(CustomException e) {
        log.warn("CustomException：{}", e.getMessage());
        return new ResponseData.Builder<String>()
                .message(e.getMessage())
                .code(e.getCode())
                .data("CustomException：" + e.getMessage()).build();
    }



}
