package com.hjb.beijin_paishui_data_middleground.common.advice;

import com.hjb.beijin_paishui_data_middleground.common.response.ResponseCode;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.common.utils.ParamValidUtil;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.ValidationException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 参数校验异常处理
 *
 * <AUTHOR>
 */
@RestControllerAdvice
@Log4j2
public class ParamValidAdvice {

    @ExceptionHandler(ValidationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseData<List<ParamValidUtil.ParamValidError>> handleValidationException(ValidationException exception) {
        List<ParamValidUtil.ParamValidError> errors = new ArrayList<>();
        //如果是个别参数校验异常
        if (exception instanceof ConstraintViolationException) {
            ConstraintViolationException exs = (ConstraintViolationException) exception;
            Set<ConstraintViolation<?>> violations = exs.getConstraintViolations();
            for (ConstraintViolation<?> item : violations) {
                String s = item.getPropertyPath().toString();
                String[] split = s.split("\\.");
                ParamValidUtil.ParamValidError error = new ParamValidUtil.ParamValidError();
                error.setKey(split[split.length - 1]);
                error.setMsg(item.getMessage());
                errors.add(error);
                //打印验证不通过的信息
                log.warn(item.getMessage());
            }
        } else {
            //如果是实体类参数校验异常（当实体类校验出错时是直接抛出的这个异常）
            System.out.println(exception.getMessage());
            errors = JsonUtil.toList(exception.getMessage(), ParamValidUtil.ParamValidError.class);
            log.warn(exception.getMessage());
        }
        return new ResponseData.Builder<List<ParamValidUtil.ParamValidError>>()
                .code(ResponseCode.PARAM_ERR.getCode())
                .message(ResponseCode.PARAM_ERR.getMsg())
                .data(errors)
                .build();
    }

}
