package com.hjb.beijin_paishui_data_middleground.common.aspect;

import com.alibaba.fastjson2.JSON;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.common.utils.JwtTokenUtil;
import com.hjb.beijin_paishui_data_middleground.common.advice.CustomException;
import com.hjb.beijin_paishui_data_middleground.common.constant.TokenConstant;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;
import org.springframework.validation.BindingResult;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 请求拦截器
 *
 * <AUTHOR>
 **/
@Aspect
@Component
@Slf4j
public class RequestAspect implements Ordered {

    /**
     * 请求响应日志
     */
    @Around("execution(* com.hjb.beijin_paishui_data_middleground.controller.*.*.*.*(..))")
    public Object logInterceptor(ProceedingJoinPoint point) throws Throwable {
        // 请求开始
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        // 获取Request
        RequestAttributes requestAttributes = RequestContextHolder.currentRequestAttributes();
        HttpServletRequest httpServletRequest = ((ServletRequestAttributes) requestAttributes).getRequest();
        // 获取请求头信息
        String header = httpServletRequest.getHeader(TokenConstant.TOKEN_HEADER);
        // 初始化用户名
        String username = "";
        try {
            username = JwtTokenUtil.getUsername(header);
        } catch (Exception e) {
            username = "匿名访问/用户登录";
        }
        // 初始化请求总时间
        long totalTimeMillis = 0L;
        // 初始化返回结果
        Object result = null;
        handleBefore(point, httpServletRequest);
        try {
            result = point.proceed();
            log.info("Username       : {}", username);
            handleAfter(result);
        } catch (CustomException e) {
            log.error("CustomException: ", e);
            ResponseData<Object> errorRes = ResponseData.error(e.getMessage());
            handleAfter(errorRes);
            return errorRes;
        } catch (Exception e) {
            log.error("Exception      : ", e);
            ResponseData<Object> serverErrorRes = ResponseData.serverFail(e.getClass() + ": " + e.getMessage());
            handleAfter(serverErrorRes);
            return serverErrorRes;
        } finally {
            stopWatch.stop();
            totalTimeMillis = stopWatch.getTotalTimeMillis();
            log.info("Time           : {}ms", totalTimeMillis);
            log.info("===========End===========" + System.lineSeparator());
        }
        return result;
    }

    private void handleBefore(ProceedingJoinPoint point, HttpServletRequest httpServletRequest) {
        log.info("==========Start==========");
        log.info("URL            : {}", httpServletRequest.getRequestURL());
        log.info("HTTP Method    : {}", httpServletRequest.getMethod());
        // 类名 方法名
        log.info("Class Method   : {}, {}", point.getSignature().getDeclaringTypeName(), point.getSignature().getName());
        log.info("IP             : {}", getIp(httpServletRequest));
        Object[] pointArgs = point.getArgs();
        List<Object> args = Arrays.stream(pointArgs).filter(object ->
                        !(object instanceof BindingResult))
                .map(object -> {
                    if (object instanceof MultipartFile) {
                        object = ((MultipartFile) object).getOriginalFilename();
                    }
                    return object;
                }).collect(Collectors.toList());
        log.info("Request Args   : {}", JSON.toJSON(args));
    }

    private void handleAfter(Object result) {
        log.info("Response       : {}", JsonUtil.toJsonStr(result));
    }

    /**
     * 获取请求IP
     *
     * @param request request
     * @return IP
     */
    public static String getIp(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (checkIp(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (checkIp(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (checkIp(ip)) {
            ip = request.getRemoteAddr();
        }
        if ("0:0:0:0:0:0:0:1".equals(ip) || "127.0.0.1".equals(ip) || "localhost".equals(ip)) {
            ip = "127.0.0.1";
        }
        return ip;
    }

    private static boolean checkIp(String ip) {
        return ip == null || ip.length() == 0 || "unkown".equalsIgnoreCase(ip)
                || ip.split("\\.").length != 4;
    }

    @Override
    public int getOrder() {
        return 1;
    }
}

