package com.hjb.beijin_paishui_data_middleground.constants;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2025/1/15 下午5:58
 * @description
 */
@Getter
@ToString
public enum SzlsModuleEnum {

    ZHTS("综合态势", 1L),
    PSSS("排水设施", 2L),
    FXDD("防汛调度", 3L),
    JSJB("接诉即办", 4L);


    private final String name;

    private final Long id;


    SzlsModuleEnum(String name, Long id) {
        this.name = name;
        this.id = id;
    }
}
