package com.hjb.beijin_paishui_data_middleground.utils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class Main {
    public static void main(String[] args) {
        String qh40 = "QH0000120230525,QH0000220230525,QH0000320230525,QH0000420230525," +
                "QH0000520230525,QH0000620230525";
        List<String> requestBodyList = Arrays.asList(
                "QH0829220220919", "QH0827220220919", "QH0000120221101",
                "QH0001020220919", "QH0002420220919", "QH0020120220919",
                "QHE0000520220823", "QHE0008020220823", "QHE0000920230601",
                "BXH0000120220606", "BXH0002220220606", "JXQ0007620220515",
                "JXQ0007420220515", "TXQ0009120220515", "GAT0000320220517",
                "GAT0006120220517", "GAT0000720230530", "GBD0116620220506",
                "GBD1918220220608", "GBD0001120230531", "DFZ0046420220525",
                "DFZ0000220220728", "XH1503620230313", "XH1507520230313",
                "XHM0000520230601", "HF0008820230313", "H0013420230313",
                "WJC0002320221013", "WJC0158520221013", "LGQ0054020221020",
                "LGQ0002420221020"
        );
        String[] split = qh40.split(",");
        List<String> sq = new ArrayList<>(Arrays.asList(split));
        requestBodyList.addAll(sq);
        System.out.println(requestBodyList);
    }
}
