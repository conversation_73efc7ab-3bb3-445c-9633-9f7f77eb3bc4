package com.hjb.beijin_paishui_data_middleground.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CoordinateParser {
    public static List<double[]> parseCoordinates(String input) {
        List<double[]> coordinates = new ArrayList<>();
        // 正则匹配所有坐标点
        Pattern pattern = Pattern.compile("(\\d+\\.?\\d*)\\s(\\d+\\.?\\d*)");
        Matcher matcher = pattern.matcher(input);
        while (matcher.find()) {
            double x = Double.parseDouble(matcher.group(1));
            double y = Double.parseDouble(matcher.group(2));
            coordinates.add(new double[]{x, y});
        }
        return coordinates;
    }
}
