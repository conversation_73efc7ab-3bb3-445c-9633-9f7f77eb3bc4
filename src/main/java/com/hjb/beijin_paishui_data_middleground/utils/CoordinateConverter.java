package com.hjb.beijin_paishui_data_middleground.utils;

import org.locationtech.proj4j.*;

import java.util.ArrayList;
import java.util.List;

public class CoordinateConverter {
    private static final String BJ54_PROJ = "+proj=tmerc +ellps=krass +towgs84=此处替换参数,如-12,123,-21,0,0,0,0 +zone=50";
    private static final String WGS84_PROJ = "+proj=longlat +ellps=WGS84 +datum=WGS84 +no_defs";

    public static List<double[]> convertBJ54ToWGS84(List<double[]> coordinates) {
        CRSFactory crsFactory = new CRSFactory();
        CoordinateTransformFactory transformFactory = new CoordinateTransformFactory();

        // 定义坐标系
        CoordinateReferenceSystem bj54 = crsFactory.createFromParameters("BJ54", BJ54_PROJ);
        CoordinateReferenceSystem wgs84 = crsFactory.createFromParameters("WGS84", WGS84_PROJ);

        // 创建转换器
        CoordinateTransform transform = transformFactory.createTransform(bj54, wgs84);

        // 转换每个坐标点
        List<double[]> result = new ArrayList<>();
        ProjCoordinate src = new ProjCoordinate();
        ProjCoordinate dst = new ProjCoordinate();
        for (double[] coord : coordinates) {
            src.x = coord[0];
            src.y = coord[1];
            transform.transform(src, dst);
            result.add(new double[]{dst.x, dst.y});
        }
        return result;
    }
}
