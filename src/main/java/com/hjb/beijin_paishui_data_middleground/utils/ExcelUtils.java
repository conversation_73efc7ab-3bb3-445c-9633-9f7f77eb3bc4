package com.hjb.beijin_paishui_data_middleground.utils;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.flood.StationTypeData;
import com.monitorjbl.xlsx.StreamingReader;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import java.io.*;
import java.util.*;

@Component
@Slf4j
@Transactional
public class ExcelUtils {
    //总行数
    private static int totalRows = 0;
    //总条数
    private static int totalCells = 0;

    //@描述：是否是2007的excel，返回true是2007
    public static boolean isExcel2007 (String filePath) {
        return filePath.matches("^.+\\.(?i)(xlsx)$");
    }
    public static List<StationTypeData> insertExcelDataToDatabase (MultipartFile file){
        List<StationTypeData> allList = new ArrayList<>();
        List<String> headers = null;
        try (InputStream inputStream = file.getInputStream();
             Workbook wk = StreamingReader.builder()
                     .rowCacheSize(100)
                     .bufferSize(4096)
                     .open(inputStream)) {
            Sheet sheet = wk.getSheetAt(0);
            boolean isHeader = true;
            for (Row row : sheet) {
                if (isHeader) {
                    // 处理表头
                    headers = new ArrayList<>();
                    for (Cell cell : row) {
                        if (cell != null) {
                            String header = cell.getStringCellValue().replaceAll("\\(.*?\\)|\\n", "").trim();
                            if (!headers.contains(header)) {
                                headers.add(header);
                            }
                        }
                    }
                    isHeader = false;
                } else {
                    // 处理数据行
                    List<String> rowData = new ArrayList<>();
                    int cellIndex = 0;
                    for (int j = 0; j < headers.size(); j++) {
                        Cell cell = row != null ? row.getCell(j) : null;
                        if (cell != null) {
                            // 根据单元格类型获取值并转换为字符串
                            switch (cell.getCellType()) {
                                case NUMERIC:
                                    if (DateUtil.isCellDateFormatted(cell)) {
                                        // 如果是日期类型，按日期格式获取字符串
                                        rowData.add(cell.getDateCellValue().toString());
                                    } else {
                                        double numericValue = cell.getNumericCellValue();
                                        if (numericValue == (int) numericValue) {
                                            // 如果是整数，转换为 int 类型再转换为字符串
                                            rowData.add(String.valueOf((int) numericValue));
                                        } else {
                                            // 否则按原方式转换为字符串
                                            rowData.add(String.valueOf(numericValue));
                                        }
                                    }
                                    break;
                                case STRING:
                                    rowData.add(cell.getStringCellValue());
                                    break;
                                case BOOLEAN:
                                    rowData.add(String.valueOf(cell.getBooleanCellValue()));
                                    break;
                                case FORMULA:
                                    rowData.add(cell.getCellFormula());
                                    break;
                                case BLANK:
                                    rowData.add("");
                                    break;
                                default:
                                    rowData.add("");
                                    break;
                            }
                        } else {
                            rowData.add("");
                        }
                    }
                    String str = rowData.get(0);
                    String[] parts = str.split("/");
                    String result = parts[parts.length - 1];
                    if ("污水流量计".equals(result) || "厂前溢流流量计".equals(result)
                            || "排河口上游液位计".equals(result) || "暗沟液位计".equals(result)
                            || "排河口流量计".equals(result)){
                        StationTypeData stationTypeData = new StationTypeData();
                        stationTypeData.setGroupName(result);
                        stationTypeData.setCode(rowData.get(1));
                        stationTypeData.setName(rowData.get(2));
                        stationTypeData.setType(rowData.get(3));
                        allList.add(stationTypeData);
                    }
                }
            }
        }catch (Exception e){
            log.info(e.getMessage());
        }
        return allList;
    }


}
