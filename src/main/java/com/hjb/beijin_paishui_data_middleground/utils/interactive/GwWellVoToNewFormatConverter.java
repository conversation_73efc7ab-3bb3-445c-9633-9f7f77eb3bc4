package com.hjb.beijin_paishui_data_middleground.utils.interactive;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.GwWellVo;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.GwWellVo.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 供管井转化为可用的弹窗数据结构
 */
public class GwWellVoToNewFormatConverter {

    public static NewFormat convert(GwWellVo gwWellVo) {
        // 构建外层结构
        NewFormat outer = new NewFormat();
        if(Objects.nonNull(gwWellVo) && Objects.nonNull(gwWellVo.getBasicInformation().getGzw()))
            outer.title = gwWellVo.getBasicInformation().getGzw();
        // 构建 data 列表
        List<Section> sectionList = new ArrayList<>();

        // 构建基本信息部分
        Section basicSection = new Section();
        basicSection.title = "基本信息";
        List<KeyValue> basicKeyValueList = new ArrayList<>();
        BasicInformation basicInformation = gwWellVo.getBasicInformation();
        if (basicInformation != null) {
            basicKeyValueList.add(new KeyValue("台账序号", basicInformation.getGctz() == null? "" : basicInformation.getGctz().toString()));
            basicKeyValueList.add(new KeyValue("使用性质", basicInformation.getSyxz() == null? "" : basicInformation.getSyxz()));
            basicKeyValueList.add(new KeyValue("井盖材质", basicInformation.getJgcz() == null? "" : basicInformation.getJgcz()));
            basicKeyValueList.add(new KeyValue("DEM地面高", basicInformation.getDemDmg() == null? "" : basicInformation.getDemDmg()));
            basicKeyValueList.add(new KeyValue("构筑物", basicInformation.getGzw() == null? "" : basicInformation.getGzw()));
            basicKeyValueList.add(new KeyValue("井面高程", basicInformation.getJmgc() == null? "" : basicInformation.getJmgc()));
            basicKeyValueList.add(new KeyValue("市政道路", basicInformation.getSzdl() == null? "" : basicInformation.getSzdl()));
            basicKeyValueList.add(new KeyValue("旧井代码", basicInformation.getOldCode() == null? "" : basicInformation.getOldCode()));
            basicKeyValueList.add(new KeyValue("井底高程", basicInformation.getJdgc() == null? "" : basicInformation.getJdgc()));
            basicKeyValueList.add(new KeyValue("所属路段", basicInformation.getSsld() == null? "" : basicInformation.getSsld()));
        }
        basicSection.data = basicKeyValueList;
        sectionList.add(basicSection);

        // 构建电子标签部分（这里假设没有对应的实体类，直接使用固定数据填充）
        Section eTagSection = new Section();
        eTagSection.title = "电子标签";
        List<KeyValue> eTagKeyValueList = new ArrayList<>();
        eTagKeyValueList.add(new KeyValue("rfid", "E2806894200050018F72918A"));
        eTagKeyValueList.add(new KeyValue("厂家", "北排装备"));
        eTagKeyValueList.add(new KeyValue("型号", " 750*450"));
        eTagKeyValueList.add(new KeyValue("出场时间", "2023.10.01"));
        eTagKeyValueList.add(new KeyValue("称重等级", "250KN"));
        eTagSection.data = eTagKeyValueList;
        sectionList.add(eTagSection);

        // 构建管理信息部分
        Section managementSection = new Section();
        managementSection.title = "管理信息";
        List<KeyValue> managementKeyValueList = new ArrayList<>();
        ManagementInformation managementInformation = gwWellVo.getManagementInformation();
        if (managementInformation != null) {
            managementKeyValueList.add(new KeyValue("运营单位", managementInformation.getDeptName() == null? "" : managementInformation.getDeptName()));
            managementKeyValueList.add(new KeyValue("运行班", managementInformation.getBanzuName() == null? "" : managementInformation.getBanzuName()));
            managementKeyValueList.add(new KeyValue("行政区", managementInformation.getAXzjName() == null? "" : managementInformation.getAXzjName()));
            managementKeyValueList.add(new KeyValue("污水流域", managementInformation.getAWslyName() == null? "" : managementInformation.getAWslyName()));
            managementKeyValueList.add(new KeyValue("雨水流域", managementInformation.getAAYslyName() == null? "" : managementInformation.getAAYslyName()));
            managementKeyValueList.add(new KeyValue("是否权属", managementInformation.getSfqs() == null? "" : managementInformation.getSfqs()));
            managementKeyValueList.add(new KeyValue("使用状态", managementInformation.getSyzt() == null? "" : managementInformation.getSyzt()));
            managementKeyValueList.add(new KeyValue("报废时间", managementInformation.getBfsj() == null? "" : managementInformation.getBfsj()));
            managementKeyValueList.add(new KeyValue("污水小流域", managementInformation.getAWsXlyName() == null? "" : managementInformation.getAWsXlyName()));
            managementKeyValueList.add(new KeyValue("雨水小流域", managementInformation.getAYsXlyName() == null? "" : managementInformation.getAYsXlyName()));
            managementKeyValueList.add(new KeyValue("运行班属性手动赋值", "否"));
            managementKeyValueList.add(new KeyValue("编辑人", managementInformation.getEditor() == null? "" : managementInformation.getEditor()));
            managementKeyValueList.add(new KeyValue("编辑时间", managementInformation.getEditTime() == null? "" : managementInformation.getEditTime()));
            managementKeyValueList.add(new KeyValue("备注", managementInformation.getBz() == null? "" : managementInformation.getBz()));
        }
        managementSection.data = managementKeyValueList;
        sectionList.add(managementSection);

        // 构建工程信息部分
        Section engineeringSection = new Section();
        engineeringSection.title = "工程信息";
        List<KeyValue> engineeringKeyValueList = new ArrayList<>();
        EngineeringInformation engineeringInformation = gwWellVo.getEngineeringInformation();
        if (engineeringInformation != null) {
            engineeringKeyValueList.add(new KeyValue("项目编号", engineeringInformation.getXmbh() == null? "" : engineeringInformation.getXmbh()));
            engineeringKeyValueList.add(new KeyValue("档案编号", engineeringInformation.getDabh() == null? "" : engineeringInformation.getDabh()));
            engineeringKeyValueList.add(new KeyValue("竣工井号", "")); // 没有对应字段，设为空字符串
            engineeringKeyValueList.add(new KeyValue("测量井号", "")); // 没有对应字段，设为空字符串
            engineeringKeyValueList.add(new KeyValue("工程(设施)", engineeringInformation.getTzName() == null? "" : engineeringInformation.getTzName()));
            engineeringKeyValueList.add(new KeyValue("数据来源", engineeringInformation.getSjly() == null? "" : engineeringInformation.getSjly()));
            engineeringKeyValueList.add(new KeyValue("移交来源", engineeringInformation.getYjly() == null? "" : engineeringInformation.getYjly()));
            engineeringKeyValueList.add(new KeyValue("接入时间", engineeringInformation.getJrsj() == null? "" : engineeringInformation.getJrsj()));
            engineeringKeyValueList.add(new KeyValue("竣工时间", engineeringInformation.getJgsj() == null? "" : engineeringInformation.getJgsj()));
            engineeringKeyValueList.add(new KeyValue("管渠编号", engineeringInformation.getYytz() == null? "" : engineeringInformation.getYytz()));
            engineeringKeyValueList.add(new KeyValue("管渠名称", engineeringInformation.getGqName() == null? "" : engineeringInformation.getGqName()));
            engineeringKeyValueList.add(new KeyValue("所在位置", engineeringInformation.getSzwz() == null? "" : engineeringInformation.getSzwz()));
        }
        engineeringSection.data = engineeringKeyValueList;
        sectionList.add(engineeringSection);

        // 构建最新调查信息部分
        Section investigationSection = new Section();
        investigationSection.title = "最新调查信息";
        List<KeyValue> investigationKeyValueList = new ArrayList<>();
        InvestigationInformation investigationInformation = gwWellVo.getInvestigationInformation();
        if (investigationInformation != null) {
            investigationKeyValueList.add(new KeyValue("调查时间", investigationInformation.getDcDate() == null? "" : investigationInformation.getDcDate()));
            investigationKeyValueList.add(new KeyValue("计划编号", investigationInformation.getBianHao() == null? "" : investigationInformation.getBianHao()));
            investigationKeyValueList.add(new KeyValue("计划时间", investigationInformation.getTzName() == null? "" : investigationInformation.getTzName()));
            investigationKeyValueList.add(new KeyValue("调查人", investigationInformation.getDcSaveUser() == null? "" : investigationInformation.getDcSaveUser()));
            investigationKeyValueList.add(new KeyValue("井盖类型", "")); // 没有对应字段，设为空字符串
            investigationKeyValueList.add(new KeyValue("井盖防盗", investigationInformation.getDcFd() == null? "" : investigationInformation.getDcFd()));
            investigationKeyValueList.add(new KeyValue("防坠子盖", investigationInformation.getDcZg() == null? "" : investigationInformation.getDcZg()));
            investigationKeyValueList.add(new KeyValue("防坠护网", investigationInformation.getDcHw() == null? "" : investigationInformation.getDcHw()));
            investigationKeyValueList.add(new KeyValue("护网安装时间", investigationInformation.getDcHwazDate() == null? "" : investigationInformation.getDcHwazDate()));
            investigationKeyValueList.add(new KeyValue("井盖权属", investigationInformation.getDcQs() == null? "" : investigationInformation.getDcQs()));
            investigationKeyValueList.add(new KeyValue("踏步", investigationInformation.getDcTb() == null? "" : investigationInformation.getDcTb()));
            investigationKeyValueList.add(new KeyValue("井壁", investigationInformation.getDcJb() == null? "" : investigationInformation.getDcJb()));
            investigationKeyValueList.add(new KeyValue("管口", investigationInformation.getDcGk() == null? "" : investigationInformation.getDcGk()));
            investigationKeyValueList.add(new KeyValue("流槽", investigationInformation.getDcLc() == null? "" : investigationInformation.getDcLc()));
            investigationKeyValueList.add(new KeyValue("井底", investigationInformation.getDcJd() == null? "" : investigationInformation.getDcJd()));
        }
        investigationSection.data = investigationKeyValueList;
        sectionList.add(investigationSection);

        // 构建最新气体检测信息部分（这里假设没有对应的实体类，直接使用固定数据填充）
        Section gasDetectionSection = new Section();
        gasDetectionSection.title = "最新气体检测信息";
        List<KeyValue> gasDetectionKeyValueList = new ArrayList<>();
        gasDetectionKeyValueList.add(new KeyValue("检测时间", "2025-04-08"));
        gasDetectionKeyValueList.add(new KeyValue("计划编号", "Y-202504-0475"));
        gasDetectionKeyValueList.add(new KeyValue("计划时间", "倒虹吸养护"));
        gasDetectionKeyValueList.add(new KeyValue("O₂(%）", "20.9"));
        gasDetectionKeyValueList.add(new KeyValue("H₂S(mg/m³)", "0"));
        gasDetectionKeyValueList.add(new KeyValue("可燃气(LEL(%))", "0"));
        gasDetectionKeyValueList.add(new KeyValue("CO(mg/m³)", "0"));
        gasDetectionSection.data = gasDetectionKeyValueList;
        sectionList.add(gasDetectionSection);

        outer.data = sectionList;
        return outer;
    }

    // 定义新格式的外层类
    @Data
    public static class NewFormat {
        String title;
        List<Section> data;
    }
    @Data
    // 定义新格式的Section类
    static class Section {
        String title;
        List<KeyValue> data;
    }

    // 定义新格式的KeyValue类
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    static class KeyValue {
        String label;
        String value;
    }

}
