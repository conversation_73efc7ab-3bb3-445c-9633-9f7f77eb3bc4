package com.hjb.beijin_paishui_data_middleground.utils.interactive;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.hjb.beijin_paishui_data_middleground.entity.sub.vo.StationMonitoringVo;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

public class StationMonitoringVoToNewFormatConverter {

    public static NewFormat convert(StationMonitoringVo stationMonitoringVo) {
        // 构建外层结构
        NewFormat outer = new NewFormat();
        outer.title = "积水监测点";

        // 构建 data 列表
        List<Section> sectionList = new ArrayList<>();

        // 构建基本信息部分
        Section basicSection = new Section();
        basicSection.title = "基本信息";
        List<KeyValue> basicKeyValueList = new ArrayList<>();
        if (stationMonitoringVo != null) {
            basicKeyValueList.add(new KeyValue("名称", stationMonitoringVo.getStationName() == null? "" : stationMonitoringVo.getStationName()));
            basicKeyValueList.add(new KeyValue("积水液位", stationMonitoringVo.getLiquidLevel() == null? "" : stationMonitoringVo.getLiquidLevel()));
        }
        basicSection.data = basicKeyValueList;
        sectionList.add(basicSection);

        outer.data = sectionList;
        return outer;
    }
    @Data
    // 定义新格式的外层类
    public static class NewFormat {
        String title;
        List<Section> data;
    }

    // 定义新格式的Section类
    @Data
    static class Section {
        String title;
        List<KeyValue> data;
    }

    // 定义新格式的KeyValue类
    @Data
    @AllArgsConstructor
    static class KeyValue {
        String label;
        String value;
    }

}
