package com.hjb.beijin_paishui_data_middleground.utils.interactive;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.GwPipeVo;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.GwPipeVo.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 供管线转化为可用的弹窗数据结构
 */
public class GwPipeVoToNewFormatConverter {

    public static NewFormat convert(GwPipeVo gwPipeVo) {
        // 构建外层结构
        NewFormat outer = new NewFormat();
        if(Objects.nonNull(gwPipeVo) && Objects.nonNull(gwPipeVo.getBasicInformation().getSyxz())){
                outer.title = gwPipeVo.getBasicInformation().getSyxz() + "管线";
        }
        // 构建 data 列表
        List<Section> sectionList = new ArrayList<>();
        // 构建基本信息部分
        Section basicSection = new Section();
        basicSection.title = "基本信息";
        List<KeyValue> basicKeyValueList = new ArrayList<>();
        BasicInformation basicInformation = gwPipeVo.getBasicInformation();
        basicKeyValueList.add(new KeyValue("台账序号", basicInformation != null? basicInformation.getGctz().toString() : null));
        basicKeyValueList.add(new KeyValue("使用性质", basicInformation != null? basicInformation.getSyxz() : null));
        basicKeyValueList.add(new KeyValue("长度(米)", basicInformation != null? basicInformation.getPipelength().toString() : null));
        basicKeyValueList.add(new KeyValue("结构形式", basicInformation != null? basicInformation.getJgxs() : null));
        basicKeyValueList.add(new KeyValue("管径", basicInformation != null? basicInformation.getGj() : null));
        basicKeyValueList.add(new KeyValue("管径类型", basicInformation != null? basicInformation.getGjType() : null));
        basicKeyValueList.add(new KeyValue("上游管底高", basicInformation != null? basicInformation.getSygdg().toString() : null));
        basicKeyValueList.add(new KeyValue("下游管底高", basicInformation != null? basicInformation.getXygdg().toString() : null));
        basicKeyValueList.add(new KeyValue("管线类型", basicInformation != null? basicInformation.getGxlx() : null));
        basicKeyValueList.add(new KeyValue("特殊管道", basicInformation != null? basicInformation.getTsgd() : null));
        basicKeyValueList.add(new KeyValue("管道材质", basicInformation != null? basicInformation.getGdcz() : null));
        basicKeyValueList.add(new KeyValue("条数", basicInformation != null? basicInformation.getTs().toString() : null));
        basicKeyValueList.add(new KeyValue("坡度", basicInformation != null? basicInformation.getPd().toString() : null));
        basicKeyValueList.add(new KeyValue("旧管道代码", basicInformation != null? basicInformation.getOldCode() : null));
        basicKeyValueList.add(new KeyValue("所属路段", basicInformation != null? basicInformation.getSsld() : null));
        basicSection.data = basicKeyValueList;
        sectionList.add(basicSection);

        // 构建管理信息部分
        Section managementSection = new Section();
        managementSection.title = "管理信息";
        List<KeyValue> managementKeyValueList = new ArrayList<>();
        ManagementInformation managementInformation = gwPipeVo.getManagementInformation();
        managementKeyValueList.add(new KeyValue("运营单位", managementInformation != null? managementInformation.getDeptName() : null));
        managementKeyValueList.add(new KeyValue("运行班", managementInformation != null? managementInformation.getBanzuName() : null));
        managementKeyValueList.add(new KeyValue("行政区", managementInformation != null? managementInformation.getAXzjName() : null));
        managementKeyValueList.add(new KeyValue("污水流域", managementInformation != null? managementInformation.getAWslyName() : null));
        managementKeyValueList.add(new KeyValue("雨水流域", managementInformation != null? managementInformation.getAAYslyName() : null));
        managementKeyValueList.add(new KeyValue("是否权属", managementInformation != null? managementInformation.getSfqs() : null));
        managementKeyValueList.add(new KeyValue("使用状态", managementInformation != null? managementInformation.getSyzt() : null));
        managementKeyValueList.add(new KeyValue("报废时间", managementInformation != null? managementInformation.getBfsj() : null));
        managementKeyValueList.add(new KeyValue("污水小流域", managementInformation != null? managementInformation.getAWsXlyName() : null));
        managementKeyValueList.add(new KeyValue("雨水小流域", managementInformation != null? managementInformation.getAYsXlyName() : null));
        managementKeyValueList.add(new KeyValue("运行班属性手动赋值", "否"));
        managementKeyValueList.add(new KeyValue("编辑人", managementInformation != null? managementInformation.getEditor() : null));
        managementKeyValueList.add(new KeyValue("编辑时间", managementInformation != null? managementInformation.getEditTime() : null));
        managementKeyValueList.add(new KeyValue("备注", managementInformation != null? managementInformation.getBz() : null));
        managementSection.data = managementKeyValueList;
        sectionList.add(managementSection);

        // 构建工程信息部分
        Section engineeringSection = new Section();
        engineeringSection.title = "工程信息";
        List<KeyValue> engineeringKeyValueList = new ArrayList<>();
        EngineeringInformation engineeringInformation = gwPipeVo.getEngineeringInformation();
        engineeringKeyValueList.add(new KeyValue("项目编号", engineeringInformation != null? engineeringInformation.getXmbh() : null));
        engineeringKeyValueList.add(new KeyValue("档案编号", engineeringInformation != null? engineeringInformation.getDabh() : null));
        engineeringKeyValueList.add(new KeyValue("数据来源", engineeringInformation != null? engineeringInformation.getSjly() : null));
        engineeringKeyValueList.add(new KeyValue("移交来源", engineeringInformation != null? engineeringInformation.getYjly() : null));
        engineeringKeyValueList.add(new KeyValue("管渠编号", engineeringInformation != null? engineeringInformation.getYytz() : null));
        engineeringKeyValueList.add(new KeyValue("管渠名称", engineeringInformation != null? engineeringInformation.getGqName() : null));
        engineeringKeyValueList.add(new KeyValue("工程(设施)", engineeringInformation != null? engineeringInformation.getTzName() : null));
        engineeringKeyValueList.add(new KeyValue("接入时间", engineeringInformation != null? engineeringInformation.getJrsj() : null));
        engineeringKeyValueList.add(new KeyValue("竣工时间", engineeringInformation != null? engineeringInformation.getJgsj() : null));
        engineeringKeyValueList.add(new KeyValue("所在位置", engineeringInformation != null? engineeringInformation.getSzwz() : null));
        engineeringSection.data = engineeringKeyValueList;
        sectionList.add(engineeringSection);

        // 构建养护周期信息部分
        Section maintenanceSection = new Section();
        maintenanceSection.title = "养护周期信息";
        List<KeyValue> maintenanceKeyValueList = new ArrayList<>();
        MaintenanceCycleInformation maintenanceCycleInformation = gwPipeVo.getMaintenanceCycleInformation();
        maintenanceKeyValueList.add(new KeyValue("养护周期(手工赋值)", maintenanceCycleInformation != null? maintenanceCycleInformation.getYhzq() : null));
        maintenanceKeyValueList.add(new KeyValue("存泥深度(mm)", maintenanceCycleInformation != null? maintenanceCycleInformation.getYhzqCnsd() : null));
        maintenanceKeyValueList.add(new KeyValue("沉泥速率(mm/天)", maintenanceCycleInformation != null? maintenanceCycleInformation.getYhzqCnsl() : null));
        maintenanceKeyValueList.add(new KeyValue("据前一天记录天数", maintenanceCycleInformation != null? maintenanceCycleInformation.getYhzqDaySpan() : null));
        maintenanceKeyValueList.add(new KeyValue("当期计算养护周期(天)", maintenanceCycleInformation != null? maintenanceCycleInformation.getYhzqCurrent() : null));
        maintenanceKeyValueList.add(new KeyValue("平均养护周期(天)", maintenanceCycleInformation != null? maintenanceCycleInformation.getYhzqAll() : null));
        maintenanceSection.data = maintenanceKeyValueList;
        sectionList.add(maintenanceSection);

        // 构建最新养护信息部分
        Section latestMaintenanceSection = new Section();
        latestMaintenanceSection.title = "最新养护信息";
        List<KeyValue> latestMaintenanceKeyValueList = new ArrayList<>();
        latestMaintenanceKeyValueList.add(new KeyValue("计划编号", null));
        latestMaintenanceKeyValueList.add(new KeyValue("作业时间", null));
        latestMaintenanceSection.data = latestMaintenanceKeyValueList;
        sectionList.add(latestMaintenanceSection);

        // 构建最新CCTV检测信息部分
        Section cctvSection = new Section();
        cctvSection.title = "最新CCTV检测信息";
        List<KeyValue> cctvKeyValueList = new ArrayList<>();
        CctvDetectionInformation cctvDetectionInformation = gwPipeVo.getCctvDetectionInformation();
        cctvKeyValueList.add(new KeyValue("计划编号", cctvDetectionInformation != null? cctvDetectionInformation.getBianHao() : null));
        cctvKeyValueList.add(new KeyValue("计划名称", cctvDetectionInformation != null? cctvDetectionInformation.getTzName() : null));
        cctvKeyValueList.add(new KeyValue("计划类别", cctvDetectionInformation != null? cctvDetectionInformation.getPlanType() : null));
        cctvKeyValueList.add(new KeyValue("存泥深度(mm)", cctvDetectionInformation != null? cctvDetectionInformation.getCctvCnsd() : null));
        cctvKeyValueList.add(new KeyValue("检测时间", cctvDetectionInformation != null? cctvDetectionInformation.getCctvJcDate() : null));
        cctvKeyValueList.add(new KeyValue("功能等级", cctvDetectionInformation != null? cctvDetectionInformation.getCctvGndj() : null));
        cctvKeyValueList.add(new KeyValue("结构等级", cctvDetectionInformation != null? cctvDetectionInformation.getCctvJgdj() : null));
        cctvKeyValueList.add(new KeyValue("缺陷数", cctvDetectionInformation != null? cctvDetectionInformation.getCctvBhCount() : null));
        cctvKeyValueList.add(new KeyValue("充满度(%)", cctvDetectionInformation != null? cctvDetectionInformation.getCctvCmd() : null));
        cctvKeyValueList.add(new KeyValue("存泥度(%)", cctvDetectionInformation != null? cctvDetectionInformation.getCctvCnl() : null));
        cctvKeyValueList.add(new KeyValue("流速", "正常"));
        cctvKeyValueList.add(new KeyValue("符合状况", "0"));
        cctvKeyValueList.add(new KeyValue("地区重要性", cctvDetectionInformation != null? cctvDetectionInformation.getCctvDqzyx() : null));
        cctvKeyValueList.add(new KeyValue("土壤重要性", cctvDetectionInformation != null? cctvDetectionInformation.getCctvTrzyx() : null));
        cctvKeyValueList.add(new KeyValue("功能缺陷描述", cctvDetectionInformation != null? cctvDetectionInformation.getCctvGnqxInfo() : null));
        cctvKeyValueList.add(new KeyValue("结构缺陷描述", cctvDetectionInformation != null? cctvDetectionInformation.getCctvJgqxInfo() : null));
        cctvKeyValueList.add(new KeyValue("老化状况", cctvDetectionInformation != null? cctvDetectionInformation.getCctvLhzk() : null));
        cctvKeyValueList.add(new KeyValue("检测设备", cctvDetectionInformation != null? cctvDetectionInformation.getCctvJcsb() : null));
        cctvSection.data = cctvKeyValueList;
        sectionList.add(cctvSection);

        // 构建最新调查信息部分
        Section investigationSection = new Section();
        investigationSection.title = "最新调查信息";
        List<KeyValue> investigationKeyValueList = new ArrayList<>();
        InvestigationInformation investigationInformation = gwPipeVo.getInvestigationInformation();
        investigationKeyValueList.add(new KeyValue("调查时间", investigationInformation != null? investigationInformation.getDcDate() : null));
        investigationKeyValueList.add(new KeyValue("计划编号", investigationInformation != null? investigationInformation.getBianHao() : null));
        investigationKeyValueList.add(new KeyValue("充满度(%)", investigationInformation != null? investigationInformation.getDcCmd() : null));
        investigationKeyValueList.add(new KeyValue("调查人", investigationInformation != null? investigationInformation.getDcSaveUser() : null));
        investigationKeyValueList.add(new KeyValue("存泥度(%)", investigationInformation != null? investigationInformation.getDcCnl() : null));
        investigationKeyValueList.add(new KeyValue("运行状况", investigationInformation != null? investigationInformation.getDcYxzk() : null));
        investigationKeyValueList.add(new KeyValue("畅通等级", investigationInformation != null? investigationInformation.getDcGndj() : null));
        investigationKeyValueList.add(new KeyValue("功能病害", investigationInformation != null? investigationInformation.getDcGnbh() : null));
        investigationKeyValueList.add(new KeyValue("计划名称", investigationInformation != null? investigationInformation.getTzName() : null));
        investigationKeyValueList.add(new KeyValue("功能病害描述", investigationInformation != null? investigationInformation.getDcGnbz() : null));
        investigationKeyValueList.add(new KeyValue("结构病害程度", investigationInformation != null? investigationInformation.getDcJgdj() : null));
        investigationKeyValueList.add(new KeyValue("结构病害", investigationInformation != null? investigationInformation.getDcJgbh() : null));
        investigationKeyValueList.add(new KeyValue("结构病害描述", investigationInformation != null? investigationInformation.getDcJgbz() : null));
        investigationKeyValueList.add(new KeyValue("存泥深度(mm)", investigationInformation != null? investigationInformation.getDcCnsd() : null));
        investigationSection.data = investigationKeyValueList;
        sectionList.add(investigationSection);

        // 构建最新消隐信息部分
        Section hiddenSection = new Section();
        hiddenSection.title = "最新消隐信息";
        List<KeyValue> hiddenKeyValueList = new ArrayList<>();
        HiddenInformation hiddenInformation = gwPipeVo.getHiddenInformation();
        hiddenKeyValueList.add(new KeyValue("计划编号", hiddenInformation != null? hiddenInformation.getBianHao() : null));
        hiddenKeyValueList.add(new KeyValue("计划名称", hiddenInformation != null? hiddenInformation.getName() : null));
        hiddenKeyValueList.add(new KeyValue("计划状态", hiddenInformation != null? hiddenInformation.getStatus() : null));
        hiddenKeyValueList.add(new KeyValue("计划类型", hiddenInformation != null? hiddenInformation.getPlanType() : null));
        hiddenKeyValueList.add(new KeyValue("计划时间", hiddenInformation != null? hiddenInformation.getJhsj() : null));
        hiddenKeyValueList.add(new KeyValue("开工时间", hiddenInformation != null? hiddenInformation.getKgsj() : null));
        hiddenKeyValueList.add(new KeyValue("完工时间", hiddenInformation != null? hiddenInformation.getWgsj() : null));
        hiddenKeyValueList.add(new KeyValue("施工工方", hiddenInformation != null? hiddenInformation.getWgSggf() : null));
        hiddenKeyValueList.add(new KeyValue("竣工验收时间", hiddenInformation != null? hiddenInformation.getJgsj() : null));
        hiddenSection.data = hiddenKeyValueList;
        sectionList.add(hiddenSection);
        outer.data = sectionList;
        return outer;
    }
    @Data
    // 定义新格式的外层类
    public static class NewFormat {
        String title;
        List<Section> data;
    }
    @Data
    // 定义新格式的Section类
    static class Section {
        String title;
        List<KeyValue> data;
    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    // 定义新格式的KeyValue类
    static class KeyValue {
        String label;
        String value;
    }
}
