package com.hjb.beijin_paishui_data_middleground.entity.szls.po.event;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import java.io.Serializable;
import java.util.List;

import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.event.EventFacilityAppealEventEditDto;
import lombok.Data;

/**
 * 设施诉求类事件
 * @TableName event_facility_appeal_event
 */
@TableName(value ="event_facility_appeal_event")
@Data
public class EventFacilityAppealEvent extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 总数
     */
    private String totalNum;

    /**
     * 事件分类
     */
    private String eventClassification;

    @TableField(exist = false)
    private List<EventFacilityAppealEventEditDto.TableData> eventClassificationData;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
