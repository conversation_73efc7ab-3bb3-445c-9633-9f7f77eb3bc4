package com.hjb.beijin_paishui_data_middleground.entity.szls.po.flood;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import java.io.Serializable;
import java.util.List;

import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.flood.FloodEmergencyUnitEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.flood.FloodRecessedBridgeEditDto;
import lombok.Data;

/**
 * 抢险单元
 * @TableName flood_emergency_unit
 */
@TableName(value ="flood_emergency_unit")
@Data
public class FloodEmergencyUnit extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 抢险单元
     */
    private String emergencyUnit;

    @TableField(exist = false)
    private List<FloodEmergencyUnitEditDto.TableData> emergencyUnitData;

    /**
     * 查询条件-类型
     */
    private String searchType;

    /**
     * 查询条件-分公司
     */
    private String searchCompany;

    /**
     * 查询条件-大单元
     */
    private String searchUnit;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
