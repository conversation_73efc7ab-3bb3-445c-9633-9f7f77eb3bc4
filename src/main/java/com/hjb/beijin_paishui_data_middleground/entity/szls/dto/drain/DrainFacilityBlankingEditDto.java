package com.hjb.beijin_paishui_data_middleground.entity.szls.dto.drain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 设施消隐
 *
 * @TableName drain_facility_blanking
 */
@Data
public class DrainFacilityBlankingEditDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private Long id;
    /**
     * 更新改造长度
     */
    private Double renewalLength;
    /**
     * 局部维修数
     */
    private String localMaintenance;
    /**
     * 井盖治理数
     */
    private String manholeCoverTreatment;
    /**
     * 年度趋势（时间）
     */
    @ApiModelProperty(name = "annualTrend", value = "年度趋势")
    private EchartsValue annualTrendValue;
    /**
     * 按类型
     */
    @ApiModelProperty(name = "solutionInformation", value = "按类型")
    private EchartsValueSolution solutionInformationValue;

    @Data
    @ApiModel("按时间类型")
    public static class EchartsValue {
        @ApiModelProperty(name = "xValue", value = "x 轴数据集合")
        private List<String> xValue;

        @ApiModelProperty(name = "seriesValue", value = "series数据集合")
        private List<Integer> seriesValue;
    }
    @Data
    @ApiModel("按类型图标")
    public static class EchartsValueSolution {
        @ApiModelProperty(name = "xValue", value = "x 轴数据集合")
        private List<String> xValue;

        @ApiModelProperty(name = "resolvedValue", value = "已解决数据集合")
        private List<Integer> resolvedValue;
        @ApiModelProperty(name = "unresolvedValue", value = "未解决数据集合")
        private List<Integer> unresolvedValue;
        @ApiModelProperty(name = "resolvedRateValue", value = "当月解决率数据集合")
        private List<Integer> resolvedRateValue;
    }

}
