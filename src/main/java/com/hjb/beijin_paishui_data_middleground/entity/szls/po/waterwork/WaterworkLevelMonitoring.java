package com.hjb.beijin_paishui_data_middleground.entity.szls.po.waterwork;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.waterwork.WaterworkLevelMonitoringEditDto;
import lombok.Data;

import java.io.Serializable;

/**
 * 液位检测
 * @TableName waterwork_level_monitoring
 */
@TableName(value = "waterwork_level_monitoring")
@Data
public class WaterworkLevelMonitoring extends BaseEntity implements Serializable {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 液位监测
     */
    private String LevelMonitoring;

    @TableField(exist = false)
    private WaterworkLevelMonitoringEditDto.EchartsValue levelMonitoringValue;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
