package com.hjb.beijin_paishui_data_middleground.entity.szls.base;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/1/10 上午9:47
 * @description 基础实体类
 */
@Data
public class BaseEntity {

    /**
     * 数据是否为填报数据 0-否 1-是
     */
    private Integer isReported;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private String createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateTime;

    /**
     * 逻辑删除 0-否 1-是
     */
    @TableLogic
    private Integer isDelete;

}
