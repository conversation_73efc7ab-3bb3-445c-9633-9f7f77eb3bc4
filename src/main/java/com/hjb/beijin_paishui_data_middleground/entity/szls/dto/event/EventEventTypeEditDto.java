package com.hjb.beijin_paishui_data_middleground.entity.szls.dto.event;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 事件类型
 * @TableName event_event_type
 */
@Data
public class EventEventTypeEditDto implements Serializable {
    /**
     * 主键
     */
    private Long id;
    /**
     * 事件类型
     */
    private List<EchartsValue> eventTypeValue;
    private static final long serialVersionUID = 1L;
    @Data
    @ApiModel("事件类型图表")
    public static class EchartsValue {
        /**
         * 类型名称
         */
        private String name;
        /**
         * 类型个数
         */
        private Integer value;
        /**
         * 类型同比
         */
        private Double ringCompare;
    }
}
