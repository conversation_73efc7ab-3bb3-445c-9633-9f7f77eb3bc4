package com.hjb.beijin_paishui_data_middleground.entity.szls.dto.event;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 设施诉求类事件
 * @TableName event_facility_appeal_event
 */
@Data
public class EventFacilityAppealEventEditDto implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 总数
     */
    private String totalNum;

    /**
     * 事件分类
     */
    private List<TableData> eventClassificationData;

    private static final long serialVersionUID = 1L;

    @Data
    @ApiModel("设施诉求类事件图标")
    public static class TableData {
        private String name;

        private Integer value;
        private Double proportion;
    }

}
