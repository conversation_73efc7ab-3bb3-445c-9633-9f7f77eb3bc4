package com.hjb.beijin_paishui_data_middleground.entity.szls.dto.synthetical;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 防汛保障
 * @TableName synthetical_flood_control
 */
@Data
public class SyntheticalFloodControlEditDto implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 出动防汛人员同比
     */
    @ApiModelProperty(name = "floodControlPersonnelYoyValue", value = "出动防汛人员同比")
    private EchartsValue floodControlPersonnelYoyValue;

    /**
     * 累计抽升量同比
     */
    @ApiModelProperty(name = "cumulativeLiftYoyValue", value = "累计抽升量同比")
    private EchartsValue cumulativeLiftYoyValue;

    /**
     * 处置积水事件同比
     */
    @ApiModelProperty(name = "waterDisposalEventYoyValue", value = "处置积水事件同比")
    private EchartsValue waterDisposalEventYoyValue;


    private static final long serialVersionUID = 1L;

    @Data
    @ApiModel("防汛保障图表")
    public static class EchartsValue {

        @ApiModelProperty(name = "xValues", value = "x轴数据")
        private List<String> xValues;

        @ApiModelProperty(name = "nowYearValues", value = "series 数据-今年的")
        private List<Integer> nowYearValues;

        @ApiModelProperty(name = "oldYearValues", value = "series 数据-去年的")
        private List<Integer> oldYearValues;

    }
}