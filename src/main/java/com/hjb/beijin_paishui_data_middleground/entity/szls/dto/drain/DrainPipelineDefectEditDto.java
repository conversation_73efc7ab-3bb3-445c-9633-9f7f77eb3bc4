package com.hjb.beijin_paishui_data_middleground.entity.szls.dto.drain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 管道缺陷
 * @TableName drain_pipeline_defect
 */
@Data
public class DrainPipelineDefectEditDto implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 按类型
     */
    private List<EchartsValue> pipelineDefectTypeValue;

    /**
     * 按时间
     */
    private List<EchartsValue> pipelineDefectDegreeValue;

    private static final long serialVersionUID = 1L;

    @Data
    @ApiModel("管道缺陷图表")
    public static class EchartsValue {

        private String name;

        private String value;

    }
}