package com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import java.io.Serializable;

import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.synthetical.SyntheticalFloodControlEditDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 防汛保障
 * @TableName synthetical_flood_control
 */
@TableName(value ="synthetical_flood_control")
@Data
public class SyntheticalFloodControl extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 出动防汛人员同比
     */
    private String floodControlPersonnelYoy;
    /**
     * 累计抽升量同比
     */
    private String cumulativeLiftYoy;
    /**
     * 处置积水事件同比
     */
    private String waterDisposalEventYoy;
    @ApiModelProperty(name = "floodControlPersonnelYoyValue", value = "出动防汛人员同比")
    @TableField(exist = false)
    private SyntheticalFloodControlEditDto.EchartsValue floodControlPersonnelYoyValue;
    /**
     * 累计抽升量同比
     */
    @ApiModelProperty(name = "cumulativeLiftYoyValue", value = "累计抽升量同比")
    @TableField(exist = false)
    private SyntheticalFloodControlEditDto.EchartsValue cumulativeLiftYoyValue;
    /**
     * 处置积水事件同比
     */
    @ApiModelProperty(name = "waterDisposalEventYoyValue", value = "处置积水事件同比")
    @TableField(exist = false)
    private SyntheticalFloodControlEditDto.EchartsValue waterDisposalEventYoyValue;
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
