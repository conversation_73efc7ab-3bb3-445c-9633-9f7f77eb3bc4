package com.hjb.beijin_paishui_data_middleground.entity.szls.dto.synthetical;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 管网运营
 * @TableName synthetical_pipeline_operation
 */
@Data
public class SyntheticalPipelineOperationEditDto {
    /**
     * 主键
     */
    private Long id;

    /**
     * 管网总里程
     */
    @ApiModelProperty(name = "totalPipelineLength", value = "管网总里程")
    private String totalPipelineLength;

    /**
     * 雨水管网
     */
    @ApiModelProperty(name = "rainwaterPipeline", value = "雨水管网")
    private String rainwaterPipeline;

    /**
     * 污水管网
     */
    @ApiModelProperty(name = "sewagePipeline", value = "污水管网")
    private String sewagePipeline;

    /**
     * 井盖总数
     */
    @ApiModelProperty(name = "manholeTotal", value = "检查井")
    private String manholeTotal;

    /**
     * 雨水箅总数
     */
    @ApiModelProperty(name = "rainwaterGrateTotal", value = "雨水口")
    private String rainwaterGrateTotal;

    /**
     * 排河口
     */
    @ApiModelProperty(name = "riverOutlet", value = "排河口")
    private String riverOutlet;

    /**
     * 管线详情 TODO 数据格式待确定
     */
    @ApiModelProperty(name = "pipelineDetails", value = "管线详情 数据格式待确定")
    private String pipelineDetails;

}