package com.hjb.beijin_paishui_data_middleground.entity.szls.dto.synthetical;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 污水处理
 * @TableName synthetical_wewage_treatment
 */
@Data
public class SyntheticalSewageTreatmentEditDto implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 再生水数据 Json
     */
    @ApiModelProperty(name = "reclaimedWaterDataList", value = "再生水厂数据")
    private List<ReclaimedWater> reclaimedWaterDataList;

    private static final long serialVersionUID = 1L;

    @Data
    public static class ReclaimedWater {
        @ApiModelProperty(name = "catchment", value = "流域名称")
        private String catchment;

        @ApiModelProperty(name = "reclaimedWaterFactoryNum", value = "再生水厂数量")
        private String reclaimedWaterFactoryNum;

        @ApiModelProperty(name = "processingCapacity", value = "处理能力")
        private String processingCapacity;
    }
}

