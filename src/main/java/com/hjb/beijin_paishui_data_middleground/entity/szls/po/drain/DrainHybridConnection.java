package com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import java.io.Serializable;
import java.util.List;

import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.drain.DrainHybridConnectionEditDto;
import lombok.Data;

/**
 * 混接
 * @TableName drain_hybrid_connection
 */
@TableName(value ="drain_hybrid_connection")
@Data
public class DrainHybridConnection extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 事件总数
     */
    private String totalEvents;

    /**
     * 完成度
     */
    private String completionDegree;

    /**
     * 政区占比
     */
    private String administrativeProportion;

    @TableField(exist = false)
    private List<DrainHybridConnectionEditDto.EchartsValue> administrativeProportionValue;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
