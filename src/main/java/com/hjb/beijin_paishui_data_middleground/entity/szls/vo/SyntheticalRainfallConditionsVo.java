package com.hjb.beijin_paishui_data_middleground.entity.szls.vo;

import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.synthetical.SyntheticalRainfallConditionsEditDto;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 降雨情况
 *
 * @TableName synthetical_rainfall_conditions
 */
@Data
public class SyntheticalRainfallConditionsVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<BasicData> basicDataList = new ArrayList<>();

    private List<EchartsData> echartsDataList = new ArrayList<>();

    @Data
    @ApiModel("降雨情况 基本数据VO")
    public static class BasicData {
        /**
         * 降雨数量
         */
        private String rainfallAmount;

        /**
         * 降雨数量同比
         */
        private String rainfallAmountYoy;

        /**
         * 累计平均降雨量
         */
        private String cumulativeAverageRainfall;

        /**
         * 累计平均降雨量同比
         */
        private String cumulativeAverageRainfallYoy;

        /**
         * 查询条件-年度
         */
        private String searchYear;
    }

    @Data
    @ApiModel("降雨情况 图表数据VO")
    public static class EchartsData {
        /**
         * 累计平均降雨量TOP10雨量站
         */
        private SyntheticalRainfallConditionsEditDto.EchartsValue top10RainfallStationsValue;

        /**
         * 行政区累计雨量
         */
        private SyntheticalRainfallConditionsEditDto.EchartsValue administrativeCumulativeRainfallValue;

        /**
         * 分公司累计雨量
         */
        private SyntheticalRainfallConditionsEditDto.EchartsValue officeRainfallAdministrativeValue;

        /**
         * 流域累计雨量
         */
        private SyntheticalRainfallConditionsEditDto.EchartsValue riverRainfallAdministrativeValue;

        /**
         * 查询条件-年度
         */
        private String searchYear;


    }

}