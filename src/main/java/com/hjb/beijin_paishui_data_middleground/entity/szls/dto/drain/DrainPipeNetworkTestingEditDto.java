package com.hjb.beijin_paishui_data_middleground.entity.szls.dto.drain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 管网检测
 */
@Data
public class DrainPipeNetworkTestingEditDto implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 管网完好率
     */
    private String pipeNetworkGoodRate;

    /**
     * 实际检测长度
     */
    private String actualDetectionLength;

    /**
     * 雨水检测长度
     */
    private String rainDetectionLength;

    /**
     * 污水检测长度
     */
    private String sewageDetectionLength;

    /**
     * 管网检测情况
     */
    private EchartsValue pipeNetworkInspectionValue;

    private static final long serialVersionUID = 1L;

    @Data
    @ApiModel("管网检测图表")
    public static class EchartsValue {
        @ApiModelProperty(name = "xValue", value = "x 轴数据集合")
        private List<String> xValue;

        @ApiModelProperty(name = "seriesValue", value = "series数据集合")
        private List<Integer> seriesValue;

    }
}