package com.hjb.beijin_paishui_data_middleground.entity.szls.po.flood;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import java.io.Serializable;
import lombok.Data;

/**
 * 雨水蓄排
 * @TableName flood_rainwater_storage
 */
@TableName(value ="flood_rainwater_storage")
@Data
public class FloodRainwaterStorage extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 泵站总数（在线、离线）
     */
    private String pumpStationTotal;

    /**
     * 实时运行
     */
    private String realTimeOperation;

    /**
     * 累计运行
     */
    private String cumulativeOperation;

    /**
     * 累计抽升量
     */
    private String cumulativeLift;

    /**
     * 初期池蓄水量
     */
    private String initialStorageCapacity;

    /**
     * 调蓄池蓄水量
     */
    private String tankStorageCapacity;

    /**
     * 雨水排蓄量
     */
    private String rainwaterDrainageCapacity;

    /**
     * 查询条件-公司
     */
    private String searchCompany;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
