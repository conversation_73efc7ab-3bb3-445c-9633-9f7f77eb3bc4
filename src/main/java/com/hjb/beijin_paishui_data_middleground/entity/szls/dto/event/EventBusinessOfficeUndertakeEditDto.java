package com.hjb.beijin_paishui_data_middleground.entity.szls.dto.event;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 按办理类型业务部室承办量
 * @TableName event_business_office_undertake
 */
@Data
public class EventBusinessOfficeUndertakeEditDto implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 按办理类型业务部室承办量
     */
    private EchartsValue businessOfficeUndertakeValue;

    private static final long serialVersionUID = 1L;

    @Data
    @ApiModel("按办理类型业务部室承办量图表")
    public static class EchartsValue {
        private List<String> xValue;

        private List<Integer> seriesValue;
    }
}