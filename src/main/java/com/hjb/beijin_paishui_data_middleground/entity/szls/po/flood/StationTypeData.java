package com.hjb.beijin_paishui_data_middleground.entity.szls.po.flood;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.models.auth.In;
import lombok.Data;

@Data
@TableName("station_type_data")
public class StationTypeData {
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 组名
     */
    private String groupName;
    /**
     * 编码
     */
    private String code;
    /**
     * 站点名称
     */
    private String name;
    /**
     * 站点类型
     */
    private String type;
}
