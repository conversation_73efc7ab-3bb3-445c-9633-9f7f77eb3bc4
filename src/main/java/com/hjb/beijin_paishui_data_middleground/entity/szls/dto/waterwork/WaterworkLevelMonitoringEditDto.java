package com.hjb.beijin_paishui_data_middleground.entity.szls.dto.waterwork;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 液位检测
 * @TableName waterwork_level_monitoring
 */
@Data
public class WaterworkLevelMonitoringEditDto implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 液位检测
     */
    private EchartsValue LevelMonitoringValue;

    private static final long serialVersionUID = 1L;

    @Data
    @ApiModel("液位检测图表")
    public static class EchartsValue {

        private List<String> xValue;

        private List<Integer> lineValue;

        private List<Integer> barValue;

    }
}
