package com.hjb.beijin_paishui_data_middleground.entity.szls.po.waterwork;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.waterwork.WaterworkTreatedWaterEditDto;
import lombok.Data;

import java.io.Serializable;

/**
 * 处理水量
 * @TableName waterwork_treated_water
 */
@TableName(value = "waterwork_treated_water")
@Data
public class WaterworkTreatedWater extends BaseEntity implements Serializable {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 液位监测
     */
    private String treatedWater;

    @TableField(exist = false)
    private WaterworkTreatedWaterEditDto.EchartsValue treatedWaterValue;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
