package com.hjb.beijin_paishui_data_middleground.entity.szls.dto.flood;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 抢险单元
 *
 * @TableName flood_emergency_unit
 */
@Data
public class FloodEmergencyUnitEditDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private Long id;

    // /**
    //  * 查询条件-类型
    //  */
    // private String searchType;
    //
    // /**
    //  * 查询条件-分公司
    //  */
    // private String searchCompany;
    //
    // /**
    //  * 查询条件-大单元
    //  */
    // private String searchUnit;
    /**
     * 抢险单元
     */
    private List<TableData> emergencyUnitData;

    @Data
    @ApiModel("抢险单元表格")
    public static class TableData {
        @ApiModelProperty(name = "callNo", value = "呼号")
        private String callNo;
        @ApiModelProperty(name = "carNumber", value = "车牌号")
        private String carNumber;

        @ApiModelProperty(name = "ability", value = "能力")
        private String ability;

        @ApiModelProperty(name = "distance", value = "距离")
        private String distance;

        @ApiModelProperty(name = "planArrivalTime", value = "预计到达时间")
        private String planArrivalTime;

        @ApiModelProperty(name = "workTime", value = "作业时长")
        private String workTime;

    }
}
