package com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import java.io.Serializable;
import lombok.Data;

/**
 * 管网及设施
 * @TableName drain_pipe_network_and_facilities
 */
@TableName(value ="drain_pipe_network_and_facilities")
@Data
public class DrainPipeNetworkAndFacilities extends BaseEntity implements Serializable {
    /**
     *
     */
    @TableId
    private Long id;

    /**
     * 管网总里程
     */
    private String totalMileage;

    /**
     * 雨水
     */
    private String rainwater;

    /**
     * 污水
     */
    private String sewage;

    /**
     * 合流
     */
    private String combinedSewer;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
