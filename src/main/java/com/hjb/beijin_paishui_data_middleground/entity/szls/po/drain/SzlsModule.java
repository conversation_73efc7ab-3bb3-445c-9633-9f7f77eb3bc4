package com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import lombok.Data;

/**
 * 模块表
 * @TableName szls_module
 */
@TableName(value ="szls_module")
@Data
public class SzlsModule implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 模块名称
     */
    private String moduleName;

    /**
     * 父 ID
     */
    private Long parentId;

    /**
     * 数据是否为填报数据 0-否 1-是
     */
    private Integer isReported;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private String createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateTime;

    /**
     * 逻辑删除 0-否 1-是
     */
    private Integer isDelete;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
