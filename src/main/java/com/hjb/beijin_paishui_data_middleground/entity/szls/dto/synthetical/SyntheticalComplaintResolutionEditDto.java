package com.hjb.beijin_paishui_data_middleground.entity.szls.dto.synthetical;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 接诉即办
 * @TableName synthetical_complaint_resolution
 */
@Data
public class SyntheticalComplaintResolutionEditDto implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 事件总量
     */
    @ApiModelProperty(name = "totalEvents", value = "事件总量")
    private String totalEvents;

    /**
     * 运营范围
     */
    @ApiModelProperty(name = "operationalScope", value = "运营范围")
    private String operationalScope;

    /**
     * 非运营范围
     */
    @ApiModelProperty(name = "nonOperationalScope", value = "非运营范围")
    private String nonOperationalScope;

    /**
     * 确权中
     */
    @ApiModelProperty(name = "rightsConfirmation", value = "确权中")
    private String rightsConfirmation;

    /**
     * 事件总量环比
     */
    @ApiModelProperty(name = "totalEventsRatio", value = "事件总量环比")
    private String totalEventsRatio;

    /**
     * 运营范围环比
     */
    @ApiModelProperty(name = "operationalScopeRatio", value = "运营范围环比")
    private String operationalScopeRatio;

    /**
     * 非运营范围环比
     */
    @ApiModelProperty(name = "nonOperationalScopeRatio", value = "非运营范围环比")
    private String nonOperationalScopeRatio;

    /**
     * 确权中环比
     */
    @ApiModelProperty(name = "rightsConfirmationRatio", value = "确权中环比")
    private String rightsConfirmationRatio;

    private static final long serialVersionUID = 1L;
}