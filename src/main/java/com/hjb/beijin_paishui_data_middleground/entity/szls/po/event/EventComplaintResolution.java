package com.hjb.beijin_paishui_data_middleground.entity.szls.po.event;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import java.io.Serializable;
import lombok.Data;

/**
 * 接诉即办
 * @TableName event_complaint_resolution
 */
@TableName(value ="event_complaint_resolution")
@Data
public class EventComplaintResolution extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 响应率
     */
    private String respondRate;

    /**
     * 解决率
     */
    private String workOutRate;

    /**
     * 满意率
     */
    private String satisfactionRate;

    /**
     * 一次性处置合格率
     */
    private String oneTimeDisposalQualifiedRate;

    /**
     * 一次性处置满意率
     */
    private String oneTimeDisposalSatisfactionRate;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
