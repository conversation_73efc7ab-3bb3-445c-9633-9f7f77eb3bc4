package com.hjb.beijin_paishui_data_middleground.entity.szls.po.event;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import java.io.Serializable;
import lombok.Data;

/**
 * 事件来源
 * @TableName event_event_source
 */
@TableName(value ="event_event_source")
@Data
public class EventEventSource extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 12345 数量
     */
    private String quantity12345;

    /**
     * 12345 环比
     */
    private String quantity12345Ratio;

    /**
     * 96159 数量
     */
    private String quantity96159;

    /**
     * 96159 环比
     */
    private String quantity96159Ratio;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
