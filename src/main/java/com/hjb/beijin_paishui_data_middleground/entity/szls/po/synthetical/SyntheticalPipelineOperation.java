package com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical;

import com.baomidou.mybatisplus.annotation.*;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import lombok.Data;

/**
 * 管网运营
 * @TableName synthetical_pipeline_operation
 */
@TableName(value ="synthetical_pipeline_operation")
@Data
public class SyntheticalPipelineOperation extends BaseEntity {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 管网总里程
     */
    private String totalPipelineLength;

    /**
     * 雨水管网
     */
    private String rainwaterPipeline;

    /**
     * 污水管网
     */
    private String sewagePipeline;

    /**
     * 井盖总数
     */
    private String manholeTotal;

    /**
     * 雨水口总数
     */
    private String rainwaterGrateTotal;

    /**
     * 排河口
     */
    private String riverOutlet;

    /**
     * 管线详情
     */
    private String pipelineDetails;
}
