package com.hjb.beijin_paishui_data_middleground.entity.szls.po.event;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import java.io.Serializable;

import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.event.EventBusinessOfficeUndertakeEditDto;
import lombok.Data;

/**
 * 按办理类型业务部室承办量
 * @TableName event_business_office_undertake
 */
@TableName(value ="event_business_office_undertake")
@Data
public class EventBusinessOfficeUndertake extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 按办理类型业务部室承办量
     */
    private String businessOfficeUndertake;

    @TableField(exist = false)
    private EventBusinessOfficeUndertakeEditDto.EchartsValue businessOfficeUndertakeValue;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
