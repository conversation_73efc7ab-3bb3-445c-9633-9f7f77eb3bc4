package com.hjb.beijin_paishui_data_middleground.entity.szls.dto.flood;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * gwp
 * @TableName flood_real_time_rainfall
 */
@Data
public class FloodRealTimeRainfallEditDto implements Serializable {
    /**
     *
     */

    private Long id;

    /**
     * 实时雨量
     */
    private EchartsValue realTimeRainfallValue;

    private static final long serialVersionUID = 1L;

    @Data
    @ApiModel("实时雨量图表")
    public static class EchartsValue {

        private List<String> xValue;

        private List<Integer> lineValue;

        private List<Integer> barValue;

    }
}
