package com.hjb.beijin_paishui_data_middleground.entity.szls.dto.synthetical;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 泵站运营
 * @TableName synthetical_pump_station_operation
 */
@Data
public class SyntheticalPumpStationOperationEditDto implements Serializable {
    /**
     * 
     */
    private Long id;

    /**
     * 泵站总数
     */
    @ApiModelProperty(name = "pumpStationTotal", value = "泵站总数")
    private String pumpStationTotal;

    /**
     * 雨水泵站
     */
    @ApiModelProperty(name = "rainPumpStation", value = "雨水泵站")
    private String rainPumpStation;

    /**
     * 污水泵站
     */
    @ApiModelProperty(name = "sewagePumpStation", value = "污水泵站")
    private String sewagePumpStation;

    /**
     * 总抽升能力
     */
    @ApiModelProperty(name = "totalLiftingCapacity", value = "总抽升能力")
    private String totalLiftingCapacity;

    /**
     * 总调蓄能力
     */
    @ApiModelProperty(name = "totalStorageCapacity", value = "总调蓄能力")
    private String totalStorageCapacity;

    /**
     * 初期池
     */
    @ApiModelProperty(name = "initialPool", value = "初期池")
    private String initialPool;

    /**
     * 调蓄池
     */
    @ApiModelProperty(name = "storagePool", value = "调蓄池")
    private String storagePool;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}