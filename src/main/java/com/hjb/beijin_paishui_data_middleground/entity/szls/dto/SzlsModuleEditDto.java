package com.hjb.beijin_paishui_data_middleground.entity.szls.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 模块表
 * @TableName szls_module
 */
@Data
@ApiModel("模块表编辑 DTO")
public class SzlsModuleEditDto implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 模块名称
     */
    @ApiModelProperty(name = "moduleName", value = "模块名称", required = true)
    @NotBlank(message = "模块名称不能为空")
    private String moduleName;

    /**
     * 父 ID
     */
    @ApiModelProperty(name = "parentId", value = "父 ID 一级为0", required = true)
    @NotNull(message = "模块父级ID不能为空")
    private Long parentId;

    /**
     * 数据是否为填报数据 0-否 1-是
     */
    @ApiModelProperty(name = "isReported", value = "数据是否为填报数据 0-否 1-是", required = true)
    @NotNull(message = "数据是否为填报数据不能为空")
    private Integer isReported;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}