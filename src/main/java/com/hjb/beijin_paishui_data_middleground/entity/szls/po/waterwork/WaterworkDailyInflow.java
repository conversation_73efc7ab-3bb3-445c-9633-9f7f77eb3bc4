package com.hjb.beijin_paishui_data_middleground.entity.szls.po.waterwork;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.waterwork.WaterworkFlowMonitoringEditDto;
import lombok.Data;

import java.io.Serializable;


/**
 * 当日进水量
 * @TableName waterwork_daily_inflow
 */
@TableName(value = "waterwork_daily_inflow")
@Data
public class WaterworkDailyInflow extends BaseEntity implements Serializable {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 当日进水量
     */
    private String dailyInflow;

    @TableField(exist = false)
    private WaterworkFlowMonitoringEditDto.EchartsValue dailyInflowValue;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
