package com.hjb.beijin_paishui_data_middleground.entity.szls.dto.synthetical;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 污泥处置
 * @TableName synthetical_sludge_disposal
 */
@Data
public class SyntheticalSludgeDisposalEditDto implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 处置中心（座）
     */
    @ApiModelProperty(name = "disposalCenter", value = "处置中心（座）")
    private String disposalCenter;

    /**
     * 处置能力（吨）
     */
    @ApiModelProperty(name = "disposalCapacity", value = "处置能力（吨）")
    private String disposalCapacity;

    /**
     * 产品达标率
     */
    @ApiModelProperty(name = "productComplianceRate", value = "产品达标率")
    private String productComplianceRate;

    private static final long serialVersionUID = 1L;
}