package com.hjb.beijin_paishui_data_middleground.entity.szls.po.flood;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import java.io.Serializable;
import java.util.List;

import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.flood.FloodPipeNetworkMonitorEditDto;
import lombok.Data;

/**
 * 管网监测
 * @TableName flood_pipe_network_monitor
 */
@TableName(value ="flood_pipe_network_monitor")
@Data
public class FloodPipeNetworkMonitor extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 雨水管道液位50%以下
     */
    private String rainwaterPipeLevelLow50;

    /**
     * 雨水管道液位50%-80%
     */
    private String rainwaterPipeLevel50To80;

    /**
     * 雨水管道液位80%-100%
     */
    private String rainwaterPipeLevel80To100;

    /**
     * 测站详情
     */
    private String stationDetails;

    /**
     * 查询条件-类型
     */
    private String searchType;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
