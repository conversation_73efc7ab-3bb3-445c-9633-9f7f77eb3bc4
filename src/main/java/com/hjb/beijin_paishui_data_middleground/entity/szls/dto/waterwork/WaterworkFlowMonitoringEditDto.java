package com.hjb.beijin_paishui_data_middleground.entity.szls.dto.waterwork;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 流量监测
 * @TableName waterwork_flow_monitoring
 */
@Data
public class WaterworkFlowMonitoringEditDto implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 流量监测
     */
    private EchartsValue WaterworkFlowMonitoringValue;

    private static final long serialVersionUID = 1L;

    @Data
    @ApiModel("流量监测图表")
    public static class EchartsValue {

        private List<String> xValue;

        private List<Integer> lineValue;

        private List<Integer> barValue;
    }
}
