package com.hjb.beijin_paishui_data_middleground.entity.szls.po.event;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import java.io.Serializable;
import java.util.List;

import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.event.EventEventTypeEditDto;
import lombok.Data;

/**
 * 事件类型
 * @TableName event_event_type
 */
@TableName(value ="event_event_type")
@Data
public class EventEventType extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 事件类型
     */
    private String eventType;

    @TableField(exist = false)
    private List<EventEventTypeEditDto.EchartsValue> eventTypeValue;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
