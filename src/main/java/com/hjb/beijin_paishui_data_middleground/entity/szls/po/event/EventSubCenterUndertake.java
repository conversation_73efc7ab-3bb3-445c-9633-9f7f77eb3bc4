package com.hjb.beijin_paishui_data_middleground.entity.szls.po.event;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import java.io.Serializable;

import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.event.EventSubCenterUndertakeEditDto;
import lombok.Data;

/**
 * 按办理类型各分中心承办量
 * @TableName event_sub_center_undertake
 */
@TableName(value ="event_sub_center_undertake")
@Data
public class EventSubCenterUndertake extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 按办理类型各分中心承办量
     */
    private String subCenterUndertake;

    @TableField(exist = false)
    private EventSubCenterUndertakeEditDto.EchartsValue subCenterUndertakeValue;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
