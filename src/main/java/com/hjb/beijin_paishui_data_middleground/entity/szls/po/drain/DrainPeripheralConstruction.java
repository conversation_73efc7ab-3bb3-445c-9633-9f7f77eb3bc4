package com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import java.io.Serializable;
import java.util.List;

import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.drain.DrainPeripheralConstructionEditDto;
import lombok.Data;

/**
 * 周边施工
 * @TableName drain_peripheral_construction
 */
@TableName(value ="drain_peripheral_construction")
@Data
public class DrainPeripheralConstruction extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 项目类型占比
     */
    private String itemTypeProportion;

    /**
     * 工程进展占比
     */
    private String engineeringProgressProportion;

    /**
     * 项目类型占比
     */
    @TableField(exist = false)
    private List<DrainPeripheralConstructionEditDto.EchartsValue> itemTypeProportionValue;

    /**
     * 工程进展占比
     */
    @TableField(exist = false)
    private List<DrainPeripheralConstructionEditDto.EchartsValue> engineeringProgressProportionValue;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
