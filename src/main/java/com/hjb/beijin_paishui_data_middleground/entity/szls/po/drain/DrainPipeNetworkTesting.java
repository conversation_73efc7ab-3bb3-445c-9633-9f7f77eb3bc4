package com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import java.io.Serializable;

import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.drain.DrainPipeNetworkTestingEditDto;
import lombok.Data;

/**
 * 管网检测
 * @TableName drain_pipe_network_testing
 */
@TableName(value ="drain_pipe_network_testing")
@Data
public class DrainPipeNetworkTesting extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 管网完好率
     */
    private String pipeNetworkGoodRate;

    /**
     * 实际检测长度
     */
    private String actualDetectionLength;

    /**
     * 雨水检测长度
     */
    private String rainDetectionLength;

    /**
     * 污水检测长度
     */
    private String sewageDetectionLength;

    /**
     * 管网检测情况
     */
    private String pipeNetworkInspection;

    @TableField(exist = false)
    private DrainPipeNetworkTestingEditDto.EchartsValue pipeNetworkInspectionValue;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
