package com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import java.io.Serializable;

import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.drain.DrainEmergencyRepairEditDto;
import lombok.Data;

/**
 * 应急抢修
 * @TableName drain_emergency_repair
 */
@TableName(value ="drain_emergency_repair")
@Data
public class DrainEmergencyRepair extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 全部
     */
    private String emergencyRepairAll;

    /**
     * 抢险
     */
    private String emergencyRescue;

    /**
     * 抢修
     */
    private String rushToRepair;

    /**
     * 防汛
     */
    private String floodPrevention;

    /**
     * 全部
     */
    @TableField(exist = false)
    private DrainEmergencyRepairEditDto.BaseData emergencyRepairAllValue;

    /**
     * 抢险
     */
    @TableField(exist = false)
    private DrainEmergencyRepairEditDto.BaseData emergencyRescueValue;

    /**
     * 抢修
     */
    @TableField(exist = false)
    private DrainEmergencyRepairEditDto.BaseData rushToRepairValue;

    /**
     * 防汛
     */
    @TableField(exist = false)
    private DrainEmergencyRepairEditDto.BaseData floodPreventionValue;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
