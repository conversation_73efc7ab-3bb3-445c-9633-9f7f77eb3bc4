package com.hjb.beijin_paishui_data_middleground.entity.szls.dto.synthetical;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 管网板块
 * @TableName synthetical_pipeline_plate
 */
@Data
public class SyntheticalPipelinePlateEditDto implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 养护量
     */
    @ApiModelProperty(name = "amountOfCuring", value = "养护量")
    private String amountOfCuring;

    /**
     * 检测里程
     */
    @ApiModelProperty(name = "detectingMileage", value = "检测里程")
    private String detectingMileage;

    /**
     * 巡查里程
     */
    @ApiModelProperty(name = "inspectionMileage", value = "巡查里程")
    private String inspectionMileage;

    private static final long serialVersionUID = 1L;
}