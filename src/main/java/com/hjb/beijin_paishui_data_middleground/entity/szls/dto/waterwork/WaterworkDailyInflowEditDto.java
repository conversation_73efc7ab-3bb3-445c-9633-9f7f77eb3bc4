package com.hjb.beijin_paishui_data_middleground.entity.szls.dto.waterwork;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 当日进水量
 * @TableName waterwork_daily_inflow
 */
@Data
public class WaterworkDailyInflowEditDto implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 当日进水量
     */
    private EchartsValue dailyInflowValue;

    private static final long serialVersionUID = 1L;

    @Data
    @ApiModel("当日进水量图表")
    public static class EchartsValue {

        private List<String> xValue;

        private List<Integer> lineValue;

        private List<Integer> barValue;;
    }

}
