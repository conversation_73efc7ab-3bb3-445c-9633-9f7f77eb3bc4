package com.hjb.beijin_paishui_data_middleground.entity.szls.dto.drain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 养护生产
 * @TableName drain_maintenance_production
 */
@Data
public class DrainMaintenanceProductionEditDto implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 管网畅通率
     */
    private String pipeNetworkSmooth;

    /**
     * 实际养护长度
     */
    private Double actualCuringLength;

    /**
     * 雨水出泥率
     */
    private String rainSludge;

    /**
     * 污水出泥率
     */
    private String sewageSludge;

    /**
     * 年度趋势
     */
    private EchartsValue annualTrendValue;

    private static final long serialVersionUID = 1L;

    @Data
    @ApiModel("养护生产图标")
    public static class EchartsValue {

        private List<String> xValues;

        @ApiModelProperty(name = "seriesBarPlanRainwater", value = "计划雨水")
        private List<Integer> seriesBarPlanRainwater;

        @ApiModelProperty(name = "seriesBarPlanRainwater", value = "实际雨水")
        private List<Integer> seriesBarRealRainwater;

        @ApiModelProperty(name = "seriesBarPlanRainwater", value = "计划污水")
        private List<Integer> seriesBarPlanSewage;

        @ApiModelProperty(name = "seriesBarPlanRainwater", value = "实际污水")
        private List<Integer> seriesBarRealSewage;

        @ApiModelProperty(name = "seriesBarPlanRainwater", value = "完成率")
        private List<Double> seriesLineFinishReta;

    }
}
