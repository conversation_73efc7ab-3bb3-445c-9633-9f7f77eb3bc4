package com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;

import java.io.Serializable;

import lombok.Data;

/**
 * 附属设施
 *
 * @TableName drain_ancillary_facilities
 */
@TableName(value = "drain_ancillary_facilities")
@Data
public class DrainAncillaryFacilities extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 井盖
     */
    private String manholeCover;

    /**
     * 雨水
     */
    private String rainwater;

    /**
     * 污水
     */
    private String sewage;

    /**
     * 合流
     */
    private String combinedFlow;

    /**
     * 篦子
     */
    private String grate;

    /**
     * 雨水泵站
     */
    private String rainwaterPumpingStation;

    /**
     * 雨水口
     */
    private String rainwaterInlet;

    /**
     * 排河口（权）
     */
    private String drainageOutletAuth;

    /**
     * 排河口（非）
     */
    private String drainageOutletUnauth;

    /**
     * 初期池
     */
    private String initialPond;

    /**
     * 调蓄池
     */
    private String storagePond;

    /**
     * 污水泵站
     */
    private String sewagePumpStation;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
