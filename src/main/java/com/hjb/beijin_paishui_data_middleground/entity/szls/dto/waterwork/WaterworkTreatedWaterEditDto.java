package com.hjb.beijin_paishui_data_middleground.entity.szls.dto.waterwork;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * 处理水量
 * @TableName waterwork_treated_water
 */
@Data
public class WaterworkTreatedWaterEditDto {

    /**
     * 主键
     */
    private Long id;

    /**
     * 处理水量
     */
    private EchartsValue treatedWaterValue;

    private static final long serialVersionUID = 1L;

    @Data
    @ApiModel("处理水量图表")
    public static class EchartsValue {

        private List<String> xValue;

        private List<Integer> lineValue;

        private List<Integer> barValue;
    }
}
