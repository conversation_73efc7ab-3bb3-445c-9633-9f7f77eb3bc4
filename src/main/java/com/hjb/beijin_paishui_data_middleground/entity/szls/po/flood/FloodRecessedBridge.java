package com.hjb.beijin_paishui_data_middleground.entity.szls.po.flood;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import java.io.Serializable;
import java.util.List;

import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.flood.FloodRecessedBridgeEditDto;
import lombok.Data;

/**
 * 下凹桥
 * @TableName flood_recessed_bridge
 */
@TableName(value ="flood_recessed_bridge")
@Data
public class FloodRecessedBridge extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 下凹桥
     */
    private String recessedBridge;

    @TableField(exist = false)
    private List<FloodRecessedBridgeEditDto.TableData> recessedBridgeData;

    /**
     * 查询条件-行政区
     */
    private String searchAdministrative;

    /**
     * 查询条件-分公司
     */
    private String searchCompany;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
