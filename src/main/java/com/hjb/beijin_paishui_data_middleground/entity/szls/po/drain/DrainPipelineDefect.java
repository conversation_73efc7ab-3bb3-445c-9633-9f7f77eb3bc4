package com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import java.io.Serializable;
import java.util.List;

import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.drain.DrainPipelineDefectEditDto;
import lombok.Data;

/**
 * 管道缺陷
 * @TableName drain_pipeline_defect
 */
@TableName(value ="drain_pipeline_defect")
@Data
public class DrainPipelineDefect extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 按类型
     */
    private String pipelineDefectType;

    /**
     * 按时间
     */
    private String pipelineDefectDegree;

    /**
     * 按类型
     */
    @TableField(exist = false)
    private List<DrainPipelineDefectEditDto.EchartsValue> pipelineDefectTypeValue;

    /**
     * 按时间
     */
    @TableField(exist = false)
    private List<DrainPipelineDefectEditDto.EchartsValue> pipelineDefectDegreeValue;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
