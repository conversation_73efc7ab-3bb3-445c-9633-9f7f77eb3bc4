package com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import java.io.Serializable;
import lombok.Data;

/**
 * 特殊设施
 * @TableName drain_special_facility
 */
@TableName(value ="drain_special_facility")
@Data
public class DrainSpecialFacility extends BaseEntity implements Serializable {
    /**
     *
     */
    @TableId
    private Long id;

    /**
     * 截流井
     */
    private String catchPit;

    /**
     * 调水闸井
     */
    private String sluicShaft;

    /**
     * 倒虹吸
     */
    private String invertedSiphon;

    /**
     * edit
     */
    private String deodorizer;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
