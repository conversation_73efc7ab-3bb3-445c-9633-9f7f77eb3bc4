package com.hjb.beijin_paishui_data_middleground.entity.szls.dto.flood;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 风险点
 * @TableName flood_risk_point
 */
@Data
public class FloodRiskPointEditDto implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 风险点
     */
    private List<TableData> riskPointData;

    private static final long serialVersionUID = 1L;

    @Data
    @ApiModel("下凹桥表格")
    public static class TableData {

        @ApiModelProperty(name = "name", value = "名称")
        private String name;

        @ApiModelProperty(name = "name", value = "所属单位")
        private String AffiliatedCompany;

        @ApiModelProperty(name = "name", value = "所属政府")
        private String AffiliatedAdministrative;

    }
}