package com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import java.io.Serializable;
import lombok.Data;

/**
 * 管网板块
 * @TableName synthetical_pipeline_plate
 */
@TableName(value ="synthetical_pipeline_plate")
@Data
public class SyntheticalPipelinePlate extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 养护量
     */
    private String amountOfCuring;

    /**
     * 检测里程
     */
    private String detectingMileage;

    /**
     * 巡查里程
     */
    private String inspectionMileage;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
