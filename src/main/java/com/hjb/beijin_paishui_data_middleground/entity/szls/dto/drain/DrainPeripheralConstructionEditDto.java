package com.hjb.beijin_paishui_data_middleground.entity.szls.dto.drain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 周边施工
 * @TableName drain_peripheral_construction
 */
@Data
public class DrainPeripheralConstructionEditDto implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 项目类型占比
     */
    private List<EchartsValue> itemTypeProportionValue;

    /**
     * 工程进展占比
     */
    private List<EchartsValue> engineeringProgressProportionValue;

    private static final long serialVersionUID = 1L;

    @Data
    public static class EchartsValue {
        private String name;

        private String value;
    }

}