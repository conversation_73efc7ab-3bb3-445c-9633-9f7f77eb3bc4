package com.hjb.beijin_paishui_data_middleground.entity.szls.po.flood;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import java.io.Serializable;
import java.util.List;

import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.flood.FloodRiskPointEditDto;
import lombok.Data;

/**
 * 风险点
 * @TableName flood_risk_point
 */
@TableName(value ="flood_risk_point")
@Data
public class FloodRiskPoint extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 风险点
     */
    private String riskPoint;

    @TableField(exist = false)
    private List<FloodRiskPointEditDto.TableData> riskPointData;

    /**
     * 查询条件-行政区
     */
    private String searchAdministrative;

    /**
     * 查询条件-分公司
     */
    private String searchCompany;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
