package com.hjb.beijin_paishui_data_middleground.entity.szls.dto.synthetical;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 降雨情况
 *
 * @TableName synthetical_rainfall_conditions
 */
@TableName(value = "synthetical_rainfall_conditions")
@Data
public class SyntheticalRainfallConditionsEditDto extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private Long id;
    /**
     * 降雨数量
     */
    @ApiModelProperty(name = "rainfallAmount", value = "降雨数量")
    private String rainfallAmount;
    /**
     * 降雨数量同比
     */
    @ApiModelProperty(name = "rainfallAmountYoy", value = "降雨数量同比")
    private String rainfallAmountYoy;
    /**
     * 累计平均降雨量
     */
    @ApiModelProperty(name = "cumulativeAverageRainfall", value = "累计平均降雨量")
    private String cumulativeAverageRainfall;
    /**
     * 累计平均降雨量同比
     */
    @ApiModelProperty(name = "cumulativeAverageRainfallYoy", value = "累计平均降雨量同比")
    private String cumulativeAverageRainfallYoy;
    /**
     * 累计平均降雨量TOP10雨量站
     */
    @ApiModelProperty(name = "top10RainfallStationsValue", value = "累计平均降雨量TOP10雨量站")
    private EchartsValue top10RainfallStationsValue;
    /**
     * 行政区累计雨量
     */
    @ApiModelProperty(name = "administrativeCumulativeRainfallValue", value = "行政区累计雨量")
    private EchartsValue administrativeCumulativeRainfallValue;
    /**
     * 分公司累计雨量
     */
    @ApiModelProperty(name = "officeRainfallAdministrativeValue", value = "分公司累计雨量")
    private EchartsValue officeRainfallAdministrativeValue;
    /**
     * 流域累计雨量
     */
    @ApiModelProperty(name = "riverRainfallAdministrativeValue", value = "流域累计雨量")
    private EchartsValue riverRainfallAdministrativeValue;
    /**
     * 查询条件-年度
     */
    @ApiModelProperty(name = "searchYear", value = "年度")
    private String searchYear;

    @Data
    @ApiModel("降雨情况图表")
    public static class EchartsValue {

        @ApiModelProperty(name = "xValue", value = "x 轴数据集合")
        private List<String> xValue;

        @ApiModelProperty(name = "seriesValue", value = "series数据集合")
        private List<Integer> seriesValue;
    }

    @Data
    public static class EchartsValueDto {
        @ApiModelProperty(name = "searchYear", value = "年度")
        private String searchYear;

        @ApiModelProperty(name = "type", value = "类型 1-top10 2-行政区 3-分公司 4-流域累计")
        private Integer type;

        private EchartsValue echartsValue;
    }


    @Data
    public static class SyntheticalRainfallConditionsBasic {
        /**
         * 降雨数量
         */
        @ApiModelProperty(name = "rainfallAmount", value = "降雨数量")
        private String rainfallAmount;

        /**
         * 降雨数量同比
         */
        @ApiModelProperty(name = "rainfallAmountYoy", value = "降雨数量同比")
        private String rainfallAmountYoy;

        /**
         * 累计平均降雨量
         */
        @ApiModelProperty(name = "cumulativeAverageRainfall", value = "累计平均降雨量")
        private String cumulativeAverageRainfall;

        /**
         * 累计平均降雨量同比
         */
        @ApiModelProperty(name = "cumulativeAverageRainfallYoy", value = "累计平均降雨量同比")
        private String cumulativeAverageRainfallYoy;

        @ApiModelProperty(name = "searchYear", value = "年度")
        private String searchYear;
    }

}