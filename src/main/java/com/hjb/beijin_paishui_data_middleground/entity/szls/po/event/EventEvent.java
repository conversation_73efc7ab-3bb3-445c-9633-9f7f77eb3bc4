package com.hjb.beijin_paishui_data_middleground.entity.szls.po.event;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import java.io.Serializable;
import lombok.Data;

/**
 * 事件
 * @TableName event_event
 */
@TableName(value ="event_event")
@Data
public class EventEvent extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 事件总量
     */
    private String totalEvents;

    /**
     * 运营范围
     */
    private String operationalScope;

    /**
     * 非运营范围
     */
    private String nonOperationalScope;

    /**
     * 确权中
     */
    private String rightsConfirmation;

    /**
     * 事件总量环比
     */
    private String totalEventsRatio;

    /**
     * 运营范围环比
     */
    private String operationalScopeRatio;

    /**
     * 非运营范围环比
     */
    private String nonOperationalScopeRatio;

    /**
     * 确权中环比
     */
    private String rightsConfirmationRatio;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
