package com.hjb.beijin_paishui_data_middleground.entity.szls.dto.synthetical;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 水处理板块
 * @TableName synthetical_water_treatment_plate
 */
@Data
public class SyntheticalWaterTreatmentPlateEditDto implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 日处理量
     */
    @ApiModelProperty(name = "dailyCapacity", value = "日处理量")
    private String dailyCapacity;

    /**
     * 再生水供给量
     */
    @ApiModelProperty(name = "reclaimedWaterSupplied", value = "再生水供给量")
    private String reclaimedWaterSupplied;

    private static final long serialVersionUID = 1L;
}