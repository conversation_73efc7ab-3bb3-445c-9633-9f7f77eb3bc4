package com.hjb.beijin_paishui_data_middleground.entity.szls.dto.drain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 应急抢修
 * @TableName drain_emergency_repair
 */
@Data
public class DrainEmergencyRepairEditDto implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 全部
     */
    private BaseData emergencyRepairAllValue;

    /**
     * 抢险
     */
    private BaseData emergencyRescueValue;

    /**
     * 抢修
     */
    private BaseData rushToRepairValue;

    /**
     * 防汛
     */
    private BaseData floodPreventionValue;

    private static final long serialVersionUID = 1L;

    @Data
    @ApiModel("应急抢修数据")
    public static class BaseData {

        @ApiModelProperty(name = "emergencyRepair", value = "应急事件个数")
        private String emergencyRepair;

        private EchartsValue echartsValue;

    }

    @Data
    @ApiModel("应急抢修图表")
    public static class EchartsValue {
        @ApiModelProperty(name = "xValue", value = "x 轴数据集合")
        private List<String> xValue;

        @ApiModelProperty(name = "seriesValue", value = "series数据集合")
        private List<Integer> seriesValue;
    }

}
