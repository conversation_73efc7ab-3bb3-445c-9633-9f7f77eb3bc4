package com.hjb.beijin_paishui_data_middleground.entity.szls.po.event;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import java.io.Serializable;
import lombok.Data;

/**
 * 呼叫
 * @TableName event_call_out
 */
@TableName(value ="event_call_out")
@Data
public class EventCallOut extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 接诉率
     */
    private String answerRate;

    /**
     * 呼入量
     */
    private String inComingCall;

    /**
     * 呼出量
     */
    private String outComingCall;

    /**
     * 呼入量环比
     */
    private String inComingCallRatio;

    /**
     * 呼出量环比
     */
    private String outComingCallRatio;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
