package com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import java.io.Serializable;
import lombok.Data;

/**
 * 污泥处置
 * @TableName synthetical_sludge_disposal
 */
@TableName(value ="synthetical_sludge_disposal")
@Data
public class SyntheticalSludgeDisposal extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 处置中心（座）
     */
    private String disposalCenter;

    /**
     * 处置能力（吨）
     */
    private String disposalCapacity;

    /**
     * 产品达标率
     */
    private String productComplianceRate;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
