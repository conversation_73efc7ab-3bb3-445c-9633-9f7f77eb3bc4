package com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import java.io.Serializable;
import lombok.Data;

/**
 * 再生水供应
 * @TableName synthetical_reclaimed_water_supply
 */
@TableName(value ="synthetical_reclaimed_water_supply")
@Data
public class SyntheticalReclaimedWaterSupply extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 供水泵房
     */
    private String pumpHouse;

    /**
     * 输配管网
     */
    private String distributionNetwork;

    /**
     * 输配能力
     */
    private String distributionCapacity;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
