package com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import java.io.Serializable;
import lombok.Data;

/**
 * 水处理板块
 * @TableName synthetical_water_treatment_plate
 */
@TableName(value ="synthetical_water_treatment_plate")
@Data
public class SyntheticalWaterTreatmentPlate extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 日处理量
     */
    private String dailyCapacity;

    /**
     * 再生水供给量
     */
    private String reclaimedWaterSupplied;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
