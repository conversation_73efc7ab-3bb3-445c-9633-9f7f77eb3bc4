package com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import java.io.Serializable;

import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.drain.DrainFacilityBlankingEditDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 设施消隐
 * @TableName drain_facility_blanking
 */
@TableName(value ="drain_facility_blanking")
@Data
public class DrainFacilityBlanking extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 更新改造长度
     */
    private Double renewalLength;

    /**
     * 局部维修数
     */
    private String localMaintenance;

    /**
     * 井盖治理数
     */
    private String manholeCoverTreatment;

    /**
     * 年度趋势
     */
    private String annualTrend;

    /**
     * 解决情况
     */
    private String solutionInformation;

    /**
     * 按时间
     */
    @ApiModelProperty(name = "annualTrend", value = "按时间")
    @TableField(exist = false)
    private DrainFacilityBlankingEditDto.EchartsValue annualTrendValue;
    /**
     * 按类型
     */
    @ApiModelProperty(name = "solutionInformation", value = "按类型")
    @TableField(exist = false)
    private DrainFacilityBlankingEditDto.EchartsValueSolution solutionInformationValue;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
