package com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import java.io.Serializable;

import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.drain.DrainMaintenanceProductionEditDto;
import lombok.Data;

/**
 * 养护生产
 * @TableName drain_maintenance_production
 */
@TableName(value ="drain_maintenance_production")
@Data
public class DrainMaintenanceProduction extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 管网畅通率
     */
    private String pipeNetworkSmooth;

    /**
     * 实际养护长度
     */
    private Double actualCuringLength;

    /**
     * 雨水出泥率
     */
    private String rainSludge;

    /**
     * 污水出泥率
     */
    private String sewageSludge;

    /**
     * 年度趋势
     */
    private String annualTrend;

    @TableField(exist = false)
    private DrainMaintenanceProductionEditDto.EchartsValue annualTrendValue;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
