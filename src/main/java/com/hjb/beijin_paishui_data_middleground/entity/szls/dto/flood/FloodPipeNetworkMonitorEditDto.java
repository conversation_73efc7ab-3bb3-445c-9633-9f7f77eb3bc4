package com.hjb.beijin_paishui_data_middleground.entity.szls.dto.flood;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 管网监测
 * @TableName flood_pipe_network_monitor
 */
@Data
public class FloodPipeNetworkMonitorEditDto implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 雨水管道液位50%以下
     */
    private String rainwaterPipeLevelLow50;

    /**
     * 雨水管道液位50%-80%
     */
    private String rainwaterPipeLevel50To80;

    /**
     * 雨水管道液位80%-100%
     */
    private String rainwaterPipeLevel80To100;

    /**
     * 测站详情
     */
    private List<TableData> stationDetailsData;

    /**
     * 查询条件-类型
     */
    private String searchType;

    private static final long serialVersionUID = 1L;

    @Data
    @ApiModel("网管检测表格")
    public static class TableData {

        @ApiModelProperty(name = "waterPlateName", value = "水厂名称")
        private String waterPlateName;

        @ApiModelProperty(name = "waterPlateName", value = "设施代码")
        private String facilityCode;

        @ApiModelProperty(name = "waterPlateName", value = "充满度")
        private String FillingDegree;

        @ApiModelProperty(name = "waterPlateName", value = "液位")
        private String LiquidLevel;

        @ApiModelProperty(name = "waterPlateName", value = "流速")
        private String VelocityOfFlow;
    }
}