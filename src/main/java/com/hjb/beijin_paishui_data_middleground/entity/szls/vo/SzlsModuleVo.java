package com.hjb.beijin_paishui_data_middleground.entity.szls.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 模块表
 * @TableName szls_module
 */
@Data
public class SzlsModuleVo implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 模块名称
     */
    private String moduleName;

    /**
     * 父 ID
     */
    private Long parentId;

    /**
     * 数据是否为填报数据 0-否 1-是
     */
    private Integer isReported;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    List<SzlsModuleVo> children = new ArrayList<>();

    private static final long serialVersionUID = 1L;
}