package com.hjb.beijin_paishui_data_middleground.entity.szls.dto.synthetical;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 再生水供应
 * @TableName synthetical_reclaimed_water_supply
 */
@Data
public class SyntheticalReclaimedWaterSupplyEditDto implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 供水泵房
     */
    @ApiModelProperty(name = "pumpHouse", value = "供水泵房")
    private String pumpHouse;

    /**
     * 输配管网
     */
    @ApiModelProperty(name = "distributionNetwork", value = "输配管网")
    private String distributionNetwork;

    /**
     * 输配能力
     */
    @ApiModelProperty(name = "distributionCapacity", value = "输配能力")
    private String distributionCapacity;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}