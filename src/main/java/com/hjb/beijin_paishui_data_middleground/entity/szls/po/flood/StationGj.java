package com.hjb.beijin_paishui_data_middleground.entity.szls.po.flood;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.models.auth.In;
import lombok.Data;

/**
 * @Description: 无法在pipe表找到对应管径的测站及其管径实体类
 * @Author: lvhongen
 * @Date: 2025-05-07 14:15
 * @Version： 1.0
 **/
@Data
@TableName("station_gj")
public class StationGj {
    private Integer id;
    private String stationCode;
    private Integer gj;
}
