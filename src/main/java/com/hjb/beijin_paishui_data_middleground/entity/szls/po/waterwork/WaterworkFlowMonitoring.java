package com.hjb.beijin_paishui_data_middleground.entity.szls.po.waterwork;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.waterwork.WaterworkFlowMonitoringEditDto;
import lombok.Data;

import java.io.Serializable;

/**
 * 流量监控
 * @TableName waterwork-flow-monitoring
 */
@TableName(value = "waterwork_flow_monitoring")
@Data
public class WaterworkFlowMonitoring extends BaseEntity implements Serializable {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 流量监测
     */
    private String flowMonitoring;

    @TableField(exist = false)
    private WaterworkFlowMonitoringEditDto.EchartsValue flowMonitoringValue;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
