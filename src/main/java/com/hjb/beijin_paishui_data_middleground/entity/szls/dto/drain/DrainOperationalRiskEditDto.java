package com.hjb.beijin_paishui_data_middleground.entity.szls.dto.drain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 运行风险
 * @TableName drain_operational_risk
 */
@Data
public class DrainOperationalRiskEditDto implements Serializable {
    /**
     * 主键
     */

    private Long id;

    /**
     * gis 长度
     */
    private Double gisLength;

    /**
     * 环比
     */
    private String chainRatio;

    /**
     * 已解决
     */
    private String resolved;

    /**
     * 已解决占比
     */
    private String resolvedProportion;

    /**
     * 未解决
     */
    private String unsolved;

    /**
     * 未解决占比
     */
    private String unsolvedProportion;
    /**
     * 合计
     */
    @ApiModelProperty(name = "amountCondition", value = "合计图表")
    private List<EchartsValue> amountCondition;
    /**
     * 雨水
     */
    @ApiModelProperty(name = "rainCondition", value = "雨水图表")
    private List<EchartsValue> rainCondition;
    /**
     * 污水
     */
    @ApiModelProperty(name = "sewageCondition", value = "污水图表")
    private List<EchartsValue> sewageCondition;

    private static final long serialVersionUID = 1L;

    @Data
    @ApiModel("运行风险图表")
    public static class EchartsValue {
        private String name;

        private String value;
    }

}
