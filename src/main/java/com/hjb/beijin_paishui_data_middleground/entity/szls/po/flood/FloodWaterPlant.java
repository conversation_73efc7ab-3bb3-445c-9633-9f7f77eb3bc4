package com.hjb.beijin_paishui_data_middleground.entity.szls.po.flood;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import java.io.Serializable;
import java.util.List;

import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.flood.FloodWaterPlantEditDto;
import lombok.Data;

/**
 * 水厂
 * @TableName flood_water_plant
 */
@TableName(value ="flood_water_plant")
@Data
public class FloodWaterPlant extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 溢流
     */
    private String overflow;

    /**
     * 跨越
     */
    private String stride;

    /**
     * 水厂详情
     */
    private String waterPlantDetails;

    @TableField(exist = false)
    private List<FloodWaterPlantEditDto.DetailData> waterPlantDetailsData;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
