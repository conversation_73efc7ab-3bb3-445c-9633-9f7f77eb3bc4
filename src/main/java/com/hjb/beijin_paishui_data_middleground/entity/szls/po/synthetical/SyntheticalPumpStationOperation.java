package com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import java.io.Serializable;
import lombok.Data;

/**
 * 泵站运营
 * @TableName synthetical_pump_station_operation
 */
@TableName(value ="synthetical_pump_station_operation")
@Data
public class SyntheticalPumpStationOperation extends BaseEntity implements Serializable {
    /**
     *
     */
    @TableId
    private Long id;

    /**
     * 泵站总数
     */
    private String pumpStationTotal;

    /**
     * 雨水泵站
     */
    private String rainPumpStation;

    /**
     * 污水泵站
     */
    private String sewagePumpStation;

    /**
     * 总抽升能力
     */
    private String totalLiftingCapacity;

    /**
     * 总调蓄能力
     */
    private String totalStorageCapacity;

    /**
     * 初期池
     */
    private String initialPool;

    /**
     * 调蓄池
     */
    private String storagePool;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
