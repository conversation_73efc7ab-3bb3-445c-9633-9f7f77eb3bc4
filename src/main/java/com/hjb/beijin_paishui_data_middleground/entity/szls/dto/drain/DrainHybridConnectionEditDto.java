package com.hjb.beijin_paishui_data_middleground.entity.szls.dto.drain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 混接
 * @TableName drain_hybrid_connection
 */
@Data
public class DrainHybridConnectionEditDto implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 事件总数
     */
    private String totalEvents;

    /**
     * 完成度
     */
    private String completionDegree;

    /**
     * 政区占比
     */
    private List<EchartsValue> administrativeProportionValue;

    private static final long serialVersionUID = 1L;

    @Data
    @ApiModel("混接图表")
    public static class EchartsValue {
        private String name;

        private String value;
    }

}