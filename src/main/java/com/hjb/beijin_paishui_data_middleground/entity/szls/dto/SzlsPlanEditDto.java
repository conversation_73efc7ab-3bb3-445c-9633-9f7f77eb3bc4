package com.hjb.beijin_paishui_data_middleground.entity.szls.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 淹没方案表
 * @TableName szls_plan
 */
@Data
public class SzlsPlanEditDto {
    /**
     * 计划id
     */
    private String planId;

    /**
     * 模型id 1: 概化模型, 2: 细化模型
     */
    private Integer modelId;

    /**
     * 方案名称
     */
    private String planName;

    /**
     * 计算开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 计算结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 输出步长
     */
    private Integer outputStep;

    /**
     * 方案创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtCreate;
}