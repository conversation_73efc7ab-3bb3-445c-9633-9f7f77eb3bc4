package com.hjb.beijin_paishui_data_middleground.entity.szls.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hjb.beijin_paishui_data_middleground.common.validation.DateTimeRange;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 淹没方案表
 * @TableName szls_plan
 */
@Data
@ApiModel("淹没方案推送DTO")
@DateTimeRange(startField = "startTime", endField = "endTime", message = "计算开始时间必须小于结束时间")
public class SzlsPlanEditDto {
    /**
     * 计划id
     */
    @ApiModelProperty(name = "planId", value = "计划唯一标识ID", required = true, example = "PLAN_20250101_001")
    @NotBlank(message = "计划ID不能为空")
    private String planId;

    /**
     * 模型id 1: 概化模型, 2: 细化模型
     */
    @ApiModelProperty(name = "modelId", value = "模型类型：1-概化模型，2-细化模型", required = true, example = "1")
    @NotNull(message = "模型ID不能为空")
    @Min(value = 1, message = "模型ID必须为1或2")
    @Max(value = 2, message = "模型ID必须为1或2")
    private Integer modelId;

    /**
     * 方案名称
     */
    @ApiModelProperty(name = "planName", value = "方案名称", required = true, example = "北京市暴雨淹没方案_2025")
    @NotBlank(message = "方案名称不能为空")
    private String planName;

    /**
     * 计算开始时间
     */
    @ApiModelProperty(name = "startTime", value = "计算开始时间，格式：yyyy-MM-dd HH:mm:ss", required = true, example = "2025-01-01 08:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @NotNull(message = "计算开始时间不能为空")
    private Date startTime;

    /**
     * 计算结束时间
     */
    @ApiModelProperty(name = "endTime", value = "计算结束时间，格式：yyyy-MM-dd HH:mm:ss", required = true, example = "2025-01-01 20:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @NotNull(message = "计算结束时间不能为空")
    private Date endTime;

    /**
     * 输出步长
     */
    @ApiModelProperty(name = "outputStep", value = "输出步长（分钟）", required = true, example = "30")
    @NotNull(message = "输出步长不能为空")
    @Min(value = 1, message = "输出步长必须大于0")
    private Integer outputStep;

    /**
     * 方案创建时间
     */
    @ApiModelProperty(name = "gmtCreate", value = "方案创建时间，格式：yyyy-MM-dd HH:mm:ss", required = false, example = "2025-01-01 07:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtCreate;
}