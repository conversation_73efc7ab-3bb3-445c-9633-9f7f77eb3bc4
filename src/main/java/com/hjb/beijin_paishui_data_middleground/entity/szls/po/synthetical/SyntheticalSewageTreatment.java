package com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import java.io.Serializable;
import java.util.List;

import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.synthetical.SyntheticalSewageTreatmentEditDto;
import lombok.Data;

/**
 * 污水处理
 * @TableName synthetical_wewage_treatment
 */
@TableName(value ="synthetical_sewage_treatment")
@Data
public class SyntheticalSewageTreatment extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 再生水数据 Json
     */
    private String reclaimedWaterData;

    @TableField(exist = false)
    private List<SyntheticalSewageTreatmentEditDto.ReclaimedWater> reclaimedWaterDataList;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}

