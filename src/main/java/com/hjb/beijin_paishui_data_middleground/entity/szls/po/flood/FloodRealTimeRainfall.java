package com.hjb.beijin_paishui_data_middleground.entity.szls.po.flood;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import java.io.Serializable;

import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.flood.FloodRealTimeRainfallEditDto;
import lombok.Data;

/**
 * 实时雨量
 * @TableName flood_real_time_rainfall
 */
@TableName(value ="flood_real_time_rainfall")
@Data
public class FloodRealTimeRainfall extends BaseEntity implements Serializable {
    /**
     *
     */
    @TableId
    private Long id;

    /**
     * 实时雨量
     */
    private String realTimeRainfall;

    @TableField(exist = false)
    private FloodRealTimeRainfallEditDto.EchartsValue realTimeRainfallValue;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
