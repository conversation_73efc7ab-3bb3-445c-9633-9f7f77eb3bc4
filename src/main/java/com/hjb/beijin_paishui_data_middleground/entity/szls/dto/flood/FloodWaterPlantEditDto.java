package com.hjb.beijin_paishui_data_middleground.entity.szls.dto.flood;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 水厂
 * @TableName flood_water_plant
 */
@Data
public class FloodWaterPlantEditDto implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty(name = "id", value = "id")
    private Long id;

    /**
     * 溢流
     */
    @ApiModelProperty(name = "overflow", value = "溢流")
    private String overflow;

    /**
     * 跨越
     */
    @ApiModelProperty(name = "stride", value = "跨越")
    private String stride;

    /**
     * 水厂详情
     */
    @ApiModelProperty(name = "waterPlantDetailsData", value = "水厂详情")
    private List<DetailData> waterPlantDetailsData;

    private static final long serialVersionUID = 1L;

    @Data
    @ApiModel("水厂详细表格")
    public static class DetailData {

        @ApiModelProperty(name = "waterPlantName", value = "水厂名称")
        private String waterPlantName;

        @ApiModelProperty(name = "instantaneousLift", value = "瞬时抽升量")
        private String instantaneousLift;

        @ApiModelProperty(name = "frontLevel", value = "栅前液位")
        private String frontLevel;

        @ApiModelProperty(name = "overflowLevel", value = "溢流液位")
        private String overflowLevel;

        @ApiModelProperty(name = "whetherToCross", value = "是否跨越")
        private String whetherToCross;

    }
}
