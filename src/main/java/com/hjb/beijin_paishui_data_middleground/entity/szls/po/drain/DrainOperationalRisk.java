package com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import java.io.Serializable;
import java.util.List;

import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.drain.DrainOperationalRiskEditDto;
import lombok.Data;

/**
 * 运行风险
 * @TableName drain_operational_risk
 */
@TableName(value ="drain_operational_risk")
@Data
public class DrainOperationalRisk extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * gis 长度
     */
    private Double gisLength;

    /**
     * 环比
     */
    private String chainRatio;

    /**
     * 已解决
     */
    private String resolved;

    /**
     * 已解决占比
     */
    private String resolvedProportion;

    /**
     * 未解决
     */
    private String unsolved;

    /**
     * 未解决占比
     */
    private String unsolvedProportion;
    /**
     * 合计
     */
    private String amountCondition;
    /**
     * 雨水
     */
    private String rainCondition;
    /**
     * 污水
     */
    private String sewageCondition;



    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
