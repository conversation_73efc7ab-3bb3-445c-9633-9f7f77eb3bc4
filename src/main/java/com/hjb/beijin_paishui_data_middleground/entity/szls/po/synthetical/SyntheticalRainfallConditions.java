package com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.base.BaseEntity;
import java.io.Serializable;

import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.synthetical.SyntheticalRainfallConditionsEditDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 降雨情况
 * @TableName synthetical_rainfall_conditions
 */
@TableName(value ="synthetical_rainfall_conditions")
@Data
public class SyntheticalRainfallConditions extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 降雨数量
     */
    private String rainfallAmount;

    /**
     * 降雨数量同比
     */
    private String rainfallAmountYoy;

    /**
     * 累计平均降雨量
     */
    private String cumulativeAverageRainfall;

    /**
     * 累计平均降雨量同比
     */
    private String cumulativeAverageRainfallYoy;

    /**
     * 累计平均降雨量TOP10雨量站
     */
    private String top10RainfallStations;

    /**
     * 行政区累计雨量
     */
    private String administrativeCumulativeRainfall;

    /**
     * 分公司累计雨量
     */
    private String officeRainfallAdministrative;

    /**
     * 流域累计雨量
     */
    private String riverRainfallAdministrative;

    /**
     * 累计平均降雨量TOP10雨量站
     */
    @ApiModelProperty(name = "top10RainfallStationsValue", value = "累计平均降雨量TOP10雨量站")
    @TableField(exist = false)
    private SyntheticalRainfallConditionsEditDto.EchartsValue top10RainfallStationsValue;

    /**
     * 行政区累计雨量
     */
    @ApiModelProperty(name = "administrativeCumulativeRainfallValue", value = "行政区累计雨量")
    @TableField(exist = false)
    private SyntheticalRainfallConditionsEditDto.EchartsValue administrativeCumulativeRainfallValue;

    /**
     * 分公司累计雨量
     */
    @ApiModelProperty(name = "officeRainfallAdministrativeValue", value = "分公司累计雨量")
    @TableField(exist = false)
    private SyntheticalRainfallConditionsEditDto.EchartsValue officeRainfallAdministrativeValue;

    /**
     * 流域累计雨量
     */
    @ApiModelProperty(name = "riverRainfallAdministrativeValue", value = "流域累计雨量")
    @TableField(exist = false)
    private SyntheticalRainfallConditionsEditDto.EchartsValue riverRainfallAdministrativeValue;

    /**
     * 查询条件-年度
     */
    private String searchYear;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
