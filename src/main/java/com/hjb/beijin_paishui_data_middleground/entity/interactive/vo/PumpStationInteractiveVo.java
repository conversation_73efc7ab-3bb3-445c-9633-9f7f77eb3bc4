package com.hjb.beijin_paishui_data_middleground.entity.interactive.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 泵站交互信息
 * @Author: lvhongen
 * @Date: 2025-05-21 11:04
 * @Version： 1.0
 **/
@Data
public class PumpStationInteractiveVo {
    @ApiModelProperty(value = "名称")
    private String stationName;
    @ApiModelProperty(value = "经度")
    private String lng;
    @ApiModelProperty(value = "维度")
    private String lat;
    /**
     * 累计抽升量
     */
    @ApiModelProperty(value = "累计抽升量")
    private String kpFl;
    @ApiModelProperty(value = "起泵数量")
    private String pumpNumber;
    @ApiModelProperty(value = "运行状态")
    private String pumpStatus;
}
