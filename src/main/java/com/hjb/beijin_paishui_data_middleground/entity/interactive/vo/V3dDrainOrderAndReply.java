package com.hjb.beijin_paishui_data_middleground.entity.interactive.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description: 历史告警事件详情Vo
 * @Author: lvhongen
 * @Date: 2025-05-14 15:37
 * @Version： 1.0
 **/
@Data
public class V3dDrainOrderAndReply {
    private String id;
    /**
     * 事件编号
     */
    private String orderSn;
    /**
     * 登记时间
     */
    private String registerTime;
    /**
     * 事件所在位置
     */
    private String locationAddress;
    /**
     * 类型1名称
     */
    private String classifyOneName;
    /**
     * 类型3名称
     */
    private String classifyThreeName;

}
