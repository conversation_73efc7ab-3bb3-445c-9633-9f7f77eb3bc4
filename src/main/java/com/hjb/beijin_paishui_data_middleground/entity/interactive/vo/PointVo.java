package com.hjb.beijin_paishui_data_middleground.entity.interactive.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 点位信息集合
 * @Author: lvhongen
 * @Date: 2025-04-25 10:08
 * @Version： 1.0
 **/
@Data
public class PointVo {
    @ApiModelProperty(value = "点位ID")
    private String code;

    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "对应点位")
    private String sgeom;
}
