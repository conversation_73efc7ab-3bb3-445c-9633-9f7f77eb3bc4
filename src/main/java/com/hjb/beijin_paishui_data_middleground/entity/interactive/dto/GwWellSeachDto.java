package com.hjb.beijin_paishui_data_middleground.entity.interactive.dto;

import com.hjb.beijin_paishui_data_middleground.common.response.CommonPage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 管井台账查询条件
 * @Author: lvhongen
 * @Date: 2025-04-26 11:33
 * @Version： 1.0
 **/
@Data
public class GwWellSeachDto extends CommonPage implements Serializable {
    @ApiModelProperty("所属单位")
    private String dept;
    @ApiModelProperty("台账序号")
    private String gctzCode;
    @ApiModelProperty("设施名称")
    private String gctzName;
    @ApiModelProperty("管渠编号")
    private String yyTzCode;
    @ApiModelProperty("管渠名称")
    private String yyTzName;
    @ApiModelProperty("代码")
    private String code;
    @ApiModelProperty("权属")
    private String sfqs;
    @ApiModelProperty("使用性质")
    private String syxz;
    @ApiModelProperty("使用状态")
    private String syzt;
    @ApiModelProperty("gwz")
    private String gzw;
    @ApiModelProperty("管线类型")
    private String gxlx;
    private static final long serialVersionUID = 1L;
}
