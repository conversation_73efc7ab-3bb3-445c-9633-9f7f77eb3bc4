package com.hjb.beijin_paishui_data_middleground.entity.interactive.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 管井管道联查详细详细
 * @Author: lvhongen
 * @Date: 2025-05-08 17:48
 * @Version： 1.0
 **/
@Data
public class GwWellAndGzTzAndGwYyTzInfo {
    /**
     * 管道代码
     */
    private String code;
   /**台账序号*/
    private Long gctz;
    /**
     * 构筑物类型
     */
   /**构筑物*/
    private String gzw;
   /**井面高程*/
    private String jmgc;
   /**井底高程*/
    private String jdgc;
    /**
     * DEM地面高
     */
   /**DEM地面高*/
    private String demDmg;
    /**
     * 井盖材质
     */
   /**井盖材质*/
    private String jgcz;
   /**旧井代码*/
    private String oldCode;
    /**
     * 使用性质
     */
   /**使用性质*/
    private String syxz;
   /**所属路段*/
    private String ssld;
   /**市政道理*/
    private String szdl;
    private String banzuName;
    private String deptName;
    /**
     * 项目编号
     */
    private String xmbh;
    /**
     * 移交来源
     */
    private String yjly;
    /**
     * 工程/设施名称
     */
    private String tzName;
    /**
     * 管渠名称
     */
    private String gqName;
    /**
     * 报废时间
     */
    private String bfsj;
    /**
     * 备注
     */
    private String bz;
    /**
     * 编辑人
     */
    private String editor;
    /**
     * 编辑时间
     */
    private String editTime;

    /**
     * 是否权属
     */
    private String sfqs;
}
