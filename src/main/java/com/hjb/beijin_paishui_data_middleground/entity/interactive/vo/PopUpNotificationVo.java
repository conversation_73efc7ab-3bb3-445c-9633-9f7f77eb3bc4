package com.hjb.beijin_paishui_data_middleground.entity.interactive.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description: 弹窗通用Vo
 * @Author: lv<PERSON>en
 * @Date: 2025-05-20 15:37
 * @Version： 1.0
 **/
@Data
public class PopUpNotificationVo {
    String title;
    List<Section> data;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    // 定义新格式的Section类
    public static class Section {
        String title;
        List<KeyValue> data;
    }

    // 定义新格式的KeyValue类
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class KeyValue {
        String label;
        String value;
    }
}
