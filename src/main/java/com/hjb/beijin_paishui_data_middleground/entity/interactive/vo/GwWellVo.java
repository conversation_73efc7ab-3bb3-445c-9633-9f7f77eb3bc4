package com.hjb.beijin_paishui_data_middleground.entity.interactive.vo;

import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description: 管井交互Vo实体类
 * @Author: lvhongen
 * @Date: 2025-04-25 16:53
 * @Version： 1.0
 **/
@Data
public class GwWellVo {
    private GwWellVo.OperationsInformation operationsInformation;
    private GwWellVo.BasicInformation basicInformation;
    private GwWellVo.ManagementInformation managementInformation;
    private GwWellVo.EngineeringInformation engineeringInformation;
    private GwWellVo.InvestigationInformation investigationInformation;
    private GwWellVo.HiddenInformation hiddenInformation;

    @Data
    @ApiModel("运营信息")
    public static class OperationsInformation {
        @ApiModelProperty(value = "当前调查计划")
        private String dcPlan;
        @ApiModelProperty(value = "当前消隐计划")
        private String gcgzPlan;
    }

    @Data
    @ApiModel("基本信息")
    public static class BasicInformation {
        /**
         * 设施台账序号
         */
        @ApiModelProperty(value = "台账序号")
        private Long gctz;
        /**
         * 构筑物类型
         */
        @ApiModelProperty(value = "构筑物")
        private String gzw;
        @ApiModelProperty(value = "井面高程")
        private String jmgc;
        @ApiModelProperty(value = "井底高程")
        private String jdgc;
        /**
         * DEM地面高
         */
        @ApiModelProperty(value = "DEM地面高")
        private String demDmg;
        /**
         * 井盖材质
         */
        @ApiModelProperty(value = "井盖材质")
        private String jgcz;
        @ApiModelProperty(value = "旧井代码")
        private String oldCode;
        /**
         * 使用性质
         */
        @ApiModelProperty(value = "使用性质")
        private String syxz;
        @ApiModelProperty(value = "所属路段")
        private String ssld;
        @ApiModelProperty(value = "市政道理")
        private String szdl;
    }

    @Data
    @ApiModel("管理信息")
    public static class ManagementInformation {
        @ApiModelProperty(value = "运营单位")
        private String deptName;
        @ApiModelProperty(value = "行政区名称")
        @SerializedName("a_xzj_name")
        private String aXzjName;
        @ApiModelProperty(value = "运行班名称")
        private String banzuName;
        @ApiModelProperty(value = "污水流域名称")
        @SerializedName("a_wsly_name")
        private String aWslyName;
        @ApiModelProperty(value = "雨水流域名称")
        @SerializedName("a_a_ysly_name")
        private String aAYslyName;
        @SerializedName("a_ws_xly_name")
        @ApiModelProperty(value = "污水小流域名称")
        private String aWsXlyName;
        @SerializedName("a_ys_xly_name")
        @ApiModelProperty(value = "雨水小流域名称")
        private String aYsXlyName;
        @ApiModelProperty("报废时间")
        private String bfsj;
        @ApiModelProperty(value = "备注")
        private String bz;
        @ApiModelProperty(value = "编辑人")
        private String editor;
        @ApiModelProperty(value = "编辑时间")
        private String editTime;
        @ApiModelProperty(value = "是否权属")
        private String sfqs;
        @ApiModelProperty(value = "使用状态")
        private String syzt;
    }

    @Data
    @ApiModel("工程信息")
    public static class EngineeringInformation {
        @ApiModelProperty("项目编号")
        private String xmbh;
        @ApiModelProperty("档案编号")
        private String dabh;
        @ApiModelProperty("工程/设施名称")
        private String tzName;
        @ApiModelProperty("移交来源")
        private String yjly;
        @ApiModelProperty("数据来源")
        private String sjly;
        @ApiModelProperty("竣工时间")
        private String jgsj;
        @ApiModelProperty("接入时间")
        private String jrsj;
        @ApiModelProperty("管渠编号")
        private String yytz;
        @ApiModelProperty("管渠名称")
        private String gqName;
        @ApiModelProperty("所在位置")
        private String szwz;
    }
    @Data
    @ApiModel("最近调查信息")
    public static class InvestigationInformation{
        @ApiModelProperty(value = "计划编号")
        private String bianHao;
        @ApiModelProperty(value = "计划名称")
        private String tzName;
        /**
         * 调查记录人
         */
        @ApiModelProperty("调查记录人")
        private String dcSaveUser;
        /**
         * 调查时间
         */
        @ApiModelProperty("调查时间")
        private String dcDate;
        /**
         * 是否五防
         */
        @ApiModelProperty("是否五防")
        private String dcType5f;
        /**
         * 井盖防盗
         */
        @ApiModelProperty("井盖防盗")
        private String dcFd;
        /**
         * 是否有防坠落子盖
         */
        @ApiModelProperty("是否有防坠落子盖")
        private String dcZg;
        /**
         * 是否有防坠护网
         */
        @ApiModelProperty("是否有防坠护网")
        private String dcHw;
        /**
         * 户外安装时间
         */
        @ApiModelProperty("户外安装时间")
        private String dcHwazDate;
        /**
         * 井盖权属
         */
        @ApiModelProperty("井盖权属")
        private String dcQs;
        /**
         * 踏步情况
         */
        @ApiModelProperty("踏步情况")
        private String dcTb;
        /**
         * 井壁情况
         */
        @ApiModelProperty("井壁情况")
        private String dcJb;
        /**
         * 管口状况
         */
        @ApiModelProperty("管口状况")
        private String dcGk;
        /**
         * 流槽状况
         */
        @ApiModelProperty("流槽状况")
        private String dcLc;
        /**
         * 井底状况
         */
        @ApiModelProperty("井底状况")
        private String dcJd;
    }
    @Data
    @ApiModel("最新消隐信息")
    public static class HiddenInformation {
        @ApiModelProperty(value = "计划编号")
        private String bianHao;
        @ApiModelProperty(value = "计划名称")
        private String name;
        @ApiModelProperty(value = "计划类别")
        private String planType;
        @ApiModelProperty(value = "计划状态")
        private String status;
        @ApiModelProperty(value = "计划时间")
        private String jhsj;
        @ApiModelProperty(value = "开工时间")
        private String kgsj;
        @ApiModelProperty(value = "完工时间")
        private String wgsj;
        @ApiModelProperty(value = "施工工法")
        private String wgSggf;
        @ApiModelProperty(value = "竣工验收时间")
        private String jgsj;
    }
}
