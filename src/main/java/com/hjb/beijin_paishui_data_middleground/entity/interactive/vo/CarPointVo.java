package com.hjb.beijin_paishui_data_middleground.entity.interactive.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 车辆实时位置信息
 * @Author: lvhongen
 * @Date: 2025-05-12 15:30
 * @Version： 1.0
 **/
@Data
public class CarPointVo {
    @ApiModelProperty(value = "车牌号")
    @JsonProperty("vi")
    private String carNo;
    @ApiModelProperty(value = "单元类型")
    private String typeName;
    @ApiModelProperty(value = "经纬度（维度,经度）")
    @JsonProperty("pos")
    private String pos;
}
