package com.hjb.beijin_paishui_data_middleground.entity.interactive.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description: 下拉款中数据
 * @Author: lvhongen
 * @Date: 2025-04-21 14:08
 * @Version： 1.0
 **/
@Data
public class SyntheticaSelectData {
    @ApiModelProperty(required = true,value = "设施台账下拉框内容")
    private List<Select> facilityLedger;
    @ApiModelProperty(required = true,value = "管道台账下拉框内容")
    private List<Select> pipelineLedger;
    @ApiModelProperty(required = true,value = "管井台账下拉框内容")

    private List<Select> pipeWellLedger;
    @Data
    @ApiModel("下拉框")
    public static class Select {
        @ApiModelProperty(required = true,value = "下拉款显示名称")
        private String name;
        @ApiModelProperty(required = true,value = "选择下拉框中实际传到后端的值")
        private List<SelectData> value;
    }
    @Data
    @ApiModel("下拉框内容")
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SelectData {
        @ApiModelProperty(required = true,value = "下拉款显示名称")
        private String name;
        @ApiModelProperty(required = true,value = "选择下拉框中实际传到后端的值")
        private String value;
    }

}
