package com.hjb.beijin_paishui_data_middleground.entity.interactive.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 立交桥交互Vo
 * @Author: lvhongen
 * @Date: 2025-04-25 11:35
 * @Version： 1.0
 **/
@Data
public class ALjqVo {
    @ApiModelProperty(value = "编号")
    private String code;
    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "环路")
    private String huanlu;
    @ApiModelProperty(value = "方向")
    private String fangxing;
    @ApiModelProperty(value = "类型")
    private String leixing;
    @ApiModelProperty(value = "ch单位")
    private String chdw;
    @ApiModelProperty(value = "位置")
    private String weizhi;
    @ApiModelProperty(value = "X坐标")
    private String xCoordinate;
    @ApiModelProperty(value = "Y坐标")
    private String yCoordinate;
}
