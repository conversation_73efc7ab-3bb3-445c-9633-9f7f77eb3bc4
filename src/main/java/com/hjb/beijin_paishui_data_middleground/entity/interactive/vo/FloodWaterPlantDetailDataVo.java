package com.hjb.beijin_paishui_data_middleground.entity.interactive.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 水厂详细详细Vo
 * @Author: lvhongen
 * @Date: 2025-05-13 16:41
 * @Version： 1.0
 **/
@Data
public class FloodWaterPlantDetailDataVo {
    @ApiModelProperty(name = "waterPlantName", value = "水厂名称")
    private String waterPlantName;

    @ApiModelProperty(name = "instantaneousLift", value = "瞬时抽升量")
    private String instantaneousLift;
    @ApiModelProperty(name = "status",value = "运行状态")
    private String status;

    @ApiModelProperty(name = "frontLevel", value = "栅前液位")
    private String frontLevel;

    @ApiModelProperty(name = "overflowLevel", value = "溢流液位")
    private String overflowLevel;

    @ApiModelProperty(name = "whetherToCross", value = "是否跨越")
    private String whetherToCross;
}
