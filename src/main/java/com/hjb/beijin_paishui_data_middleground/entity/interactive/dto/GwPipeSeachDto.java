package com.hjb.beijin_paishui_data_middleground.entity.interactive.dto;

import com.hjb.beijin_paishui_data_middleground.common.response.CommonPage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 管道台账查询Dto
 * @Author: lvhongen
 * @Date: 2025-04-26 09:25
 * @Version： 1.0
 **/
@Data
public class GwPipeSeachDto extends CommonPage implements Serializable {
    @ApiModelProperty("所属单位")
    private String dept;
    @ApiModelProperty("台账序号")
    private String gctzCode;
    @ApiModelProperty("设施名称")
    private String gctzName;
    @ApiModelProperty("管渠编号")
    private String yyTzCode;
    @ApiModelProperty("管渠名称")
    private String yyTzName;
    @ApiModelProperty("代码")
    private String code;
    @ApiModelProperty("权属")
    private String sfqs;
    @ApiModelProperty("使用性质")
    private String syxz;
    @ApiModelProperty("使用状态")
    private String syzt;
    @ApiModelProperty("特殊管段")
    private String tsgd;
    @ApiModelProperty("管线类型")
    private String gxlx;
    private static final long serialVersionUID = 1L;
}
