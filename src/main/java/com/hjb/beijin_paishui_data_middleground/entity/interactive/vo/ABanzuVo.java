package com.hjb.beijin_paishui_data_middleground.entity.interactive.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 班住交互Api
 * @Author: lvhongen
 * @Date: 2025-04-25 10:04
 * @Version： 1.0
 **/
@Data
public class ABanzuVo {
    @ApiModelProperty(value = "编号")
    private String code;
    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "运行班名称")
    private String banzuDesc;
    private String sgeom;
    @ApiModelProperty(value = "运营单位")
    private String deptName;
    @ApiModelProperty(value = "周长")
    private String a_length;
    @ApiModelProperty(value = "面积")
    private String a_area;
}
