package com.hjb.beijin_paishui_data_middleground.entity.interactive.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 管井管道联查详细信息
 * @Author: lvhongen
 * @Date: 2025-05-08 15:46
 * @Version： 1.0
 **/
@Data
public class GwPipeAndGzTzAndGwYyTzInfo {
    @ApiModelProperty(name = "emergencyRepair", value = "应急事件个数")
    private String emergencyRepair;
    /**
     * 管道代码
     */
    private String code;
    /**
     * 设施台账序号
     */
    /**
     * 台账序号
     */
    private Long gctz;
    /**
     * 管长
     */
    /**
     * 长度
     */
    private Double pipelength;
    /**
     * 管径
     */
    /**
     * 管径
     */
    private String gj;
    /**
     * 管线类型
     */
    /**
     * 管线类型
     */
    private String gxlx;
    /**
     * 使用性质
     */
    /**
     * 使用性质
     */
    private String syxz;
    /**
     * 结构形式 round圆形，rect矩形
     */
    /**
     * 结构形式
     */
    private String jgxs;
    /**
     * 下游井代码
     */
    /**
     * 下游井代码
     */
    private String xyjdm;
    //上游井代码
    /**
     * 上游井代码
     */
    private String syjdm;
    /**
     * 上游管地低高
     */
    /**
     * 上游管地低高
     */
    private Double sygdg;
    /**
     * 下游管地低高
     */
    /**
     * 下游管地低高
     */
    private Double xygdg;
    /**
     * 坡度
     */
    /**
     * 坡度
     */
    private Double pd;
    /**
     * 条数
     */
    /**
     * 条数
     */
    private Integer ts;

    //mid中型，min小型,big大型,bbb特大型
    /**
     * 管线类型
     */
    private String gjType;
    /**
     * 旧管井代码
     */
    private String oldCode;
    /**
     * 特殊管道
     */
    /**
     * 特殊管道
     */
    private String tsgd;
    /**
     * 管道材质hnt-混凝土，zq砖砌
     */
    /**
     * 管道材质
     */
    private String gdcz;
    /**
     * 所属路段
     */
    private String ssld;
    /**
     * 市政道理
     */
    private String szdl;
    /**
     * 项目编号
     */
    private String xmbh;
    /**
     * 工程/设施名称
     */
    private String tzName;
    private String banzuName;
    private String deptName;
    /**
     * 移交来源
     */
    private String yjly;
    /**
     * 管渠名称
     */
    private String gqName;
    /**
     * 报废时间
     */
    private String bfsj;
    /**
     * 备注
     */
    private String bz;
    /**
     * 编辑人
     */
    private String editor;
    /**
     * 编辑时间
     */
    private String editTime;

    /**
     * 是否权属
     */
    private String sfqs;
}
