package com.hjb.beijin_paishui_data_middleground.entity.interactive.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.locationtech.jts.geom.Geometry;

/**
 * @Description: 部分交互Vo
 * @Author: lv<PERSON>en
 * @Date: 2025-04-21 16:49
 * @Version： 1.0
 **/
@Data
public class PartialInteractionVo {
    @ApiModelProperty(value = "编号")
    private String code;
    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "周长")
    private String a_length;
    @ApiModelProperty(value = "面积")
    private String a_area;
}
