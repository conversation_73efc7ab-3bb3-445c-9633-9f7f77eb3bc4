package com.hjb.beijin_paishui_data_middleground.entity.interactive.vo;

import com.google.gson.annotations.SerializedName;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.drain.DrainEmergencyRepairEditDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;

/**
 * @Description: 管线交互实体类
 * @Author: lvhongen
 * @Date: 2025-04-25 14:19
 * @Version： 1.0
 **/
@Data
public class GwPipeVo {
    private OperationsInformation operationsInformation;
    private BasicInformation basicInformation;
    private ManagementInformation managementInformation;
    private EngineeringInformation engineeringInformation;
    private MaintenanceCycleInformation maintenanceCycleInformation;
    private CctvDetectionInformation cctvDetectionInformation;
    private InvestigationInformation investigationInformation;
    private HiddenInformation hiddenInformation;
    @Data
    @ApiModel("运营信息")
    public static class OperationsInformation {
        @ApiModelProperty(value = "当前养护计划")
        private String yhPlan;
        @ApiModelProperty(value = "当前监测计划")
        private String cctvPlan;
        @ApiModelProperty(value = "当前调查计划")
        private String dcPlan;
        @ApiModelProperty(value = "当前消隐计划")
        private String gcgzPlan;
        @ApiModelProperty(value = "上次养护时间")
        private String lastYhDate;
        @ApiModelProperty(value = "下次养护时间(手工赋值)")
        private String nextYhDate;
        @ApiModelProperty(value = "下次养护时间(沉泥计算)")
        private String nextYhDateCal;
    }
    @Data
    @ApiModel("基本信息")
    public static class BasicInformation {
        @ApiModelProperty(name = "emergencyRepair", value = "应急事件个数")
        private String emergencyRepair;
        @ApiModelProperty(value = "管道代码")
        private String code;
        /**
         * 设施台账序号
         */
        @ApiModelProperty(value = "台账序号")
        private Long gctz;
        /**
         * 管长
         */
        @ApiModelProperty(value = "长度")
        private Double pipelength;
        /**
         * 管径
         */
        @ApiModelProperty(value = "管径")
        private String gj;
        /**
         * 管线类型
         */
        @ApiModelProperty(value = "管线类型")
        private String gxlx;
        /**
         * 使用性质
         */
        @ApiModelProperty(value = "使用性质")
        private String syxz;
        /**
         * 结构形式 round圆形，rect矩形
         */
        @ApiModelProperty(value = "结构形式")
        private String jgxs;
        /**
         * 下游井代码
         */
        @ApiModelProperty(value = "下游井代码")
        private String xyjdm;
        //上游井代码
        @ApiModelProperty(value = "上游井代码")
        private String syjdm;
        /**
         * 上游管地低高
         */
        @ApiModelProperty(value = "上游管地低高")
        private Double sygdg;
        /**
         * 下游管地低高
         */
        @ApiModelProperty(value = "下游管地低高")
        private Double xygdg;
        /**
         * 坡度
         */
        @ApiModelProperty(value = "坡度")
        private Double pd;
        /**
         * 条数
         */
        @ApiModelProperty(value = "条数")
        private Integer ts;

        //mid中型，min小型,big大型,bbb特大型
        @ApiModelProperty(value = "管线类型")
        private String gjType;
        @ApiModelProperty(value = "旧管井代码")
        private String oldCode;
        /**
         * 特殊管道
         */
        @ApiModelProperty(value = "特殊管道")
        private String tsgd;
        /**
         * 管道材质hnt-混凝土，zq砖砌
         */
        @ApiModelProperty(value = "管道材质")
        private String gdcz;
        @ApiModelProperty(value = "所属路段")
        private String ssld;
        @ApiModelProperty(value = "市政道理")
        private String szdl;
    }
    @Data
    @ApiModel("管理信息")
    public static class ManagementInformation {
        @ApiModelProperty(value = "运营单位")
        private String deptName;
        @ApiModelProperty(value = "行政区名称")
        @SerializedName("a_xzj_name")
        private String aXzjName;
        @ApiModelProperty(value = "运行班名称")
        private String banzuName;
        @ApiModelProperty(value = "污水流域名称")
        @SerializedName("a_wsly_name")
        private String aWslyName;
        @ApiModelProperty(value = "雨水流域名称")
        @SerializedName("a_a_ysly_name")
        private String aAYslyName;
        @SerializedName("a_ws_xly_name")
        @ApiModelProperty(value = "污水小流域名称")
        private String aWsXlyName;
        @SerializedName("a_ys_xly_name")
        @ApiModelProperty(value = "雨水小流域名称")
        private String aYsXlyName;
        @ApiModelProperty("报废时间")
        private String bfsj;
        @ApiModelProperty(value = "备注")
        private String bz;
        @ApiModelProperty(value = "编辑人")
        private String editor;
        @ApiModelProperty(value = "编辑时间")
        private String editTime;
        @ApiModelProperty(value = "是否权属")
        private String sfqs;
        @ApiModelProperty(value = "使用状态")
        private String syzt;
    }
    @Data
    @ApiModel("工程信息")
    public static class EngineeringInformation {
        @ApiModelProperty("项目编号")
        private String xmbh;
        @ApiModelProperty("档案编号")
        private String dabh;
        @ApiModelProperty("工程/设施名称")
        private String tzName;
        @ApiModelProperty("移交来源")
        private String yjly;
        @ApiModelProperty("数据来源")
        private String sjly;
        @ApiModelProperty("竣工时间")
        private String jgsj;
        @ApiModelProperty("接入时间")
        private String jrsj;
        @ApiModelProperty("管渠编号")
        private String yytz;
        @ApiModelProperty("管渠名称")
        private String gqName;
        @ApiModelProperty("所在位置")
        private String szwz;
    }
    @Data
    @ApiModel("养护周期信息")
    public static class MaintenanceCycleInformation {
        @ApiModelProperty(value = "养护周期(手工赋值)")
        private String yhzq;
        @ApiModelProperty(value = "存泥深度")
        private String yhzqCnsd;
        @ApiModelProperty(value = "沉泥速率")
        private String yhzqCnsl;
        @ApiModelProperty(value = "距前一记录天数")
        private String yhzqDaySpan;
        @ApiModelProperty(value = "当前计算养护周期")
        private String yhzqCurrent;
        @ApiModelProperty(value = "平均养护天数")
        private String yhzqAll;
    }
    @Data
    @ApiModel("cctv最新检测信息")
    public static class CctvDetectionInformation{
        @ApiModelProperty(value = "计划编号")
        private String bianHao;
        @ApiModelProperty(value = "计划名称")
        private String tzName;
        @ApiModelProperty(value = "计划类别")
        private String planType;
        @ApiModelProperty(value = "检测时间")
        private String cctvJcDate;
        @ApiModelProperty(value = "功能等级")
        private String cctvGndj;
        @ApiModelProperty(value = "结构等级")
        private String cctvJgdj;
        @ApiModelProperty(value = "缺陷数量")
        private String cctvBhCount;
        @ApiModelProperty(value = "功能缺陷描述")
        private String cctvGnqxInfo;
        @ApiModelProperty(value = "结构缺陷描述")
        private String cctvJgqxInfo;
        @ApiModelProperty(value = "充满度")
        private String cctvCmd;
        @ApiModelProperty(value = "存泥深度")
        private String cctvCnsd;
        @ApiModelProperty(value = "存泥率")
        private String cctvCnl;
        @ApiModelProperty(value = "地区重要性")
        private String cctvDqzyx;
        @ApiModelProperty(value = "土壤重要性")
        private String cctvTrzyx;
        @ApiModelProperty(value = "老化状况")
        private String cctvLhzk;
        @ApiModelProperty(value = "负荷状况")
        private String cctvFhzkDesc;
        @ApiModelProperty(value = "检测设备")
        private String cctvJcsb;
    }
    @Data
    @ApiModel("最近调查信息")
    public static class InvestigationInformation{
        @ApiModelProperty(value = "计划编号")
        private String bianHao;
        @ApiModelProperty(value = "计划名称")
        private String tzName;
        @ApiModelProperty("调查记录人")
        private String dcSaveUser;
        @ApiModelProperty("调查时间")
        private String dcDate;
        @ApiModelProperty("充满度")
        private String dcCmd;
        @ApiModelProperty("存泥深度")
        private String dcCnsd;
        @ApiModelProperty("存泥率")
        private String dcCnl;
        @ApiModelProperty("运行状况")
        private String dcYxzk;
        @ApiModelProperty("畅通等级")
        private String dcGndj;
        @ApiModelProperty("功能病害")
        private String dcGnbh;
        @ApiModelProperty("功能病害描述")
        private String dcGnbz;
        @ApiModelProperty("结构病害程度")
        private String dcJgdj;
        @ApiModelProperty("结构病害描述")
        private String dcJgbz;
        @ApiModelProperty("结构病害")
        private String dcJgbh;
    }
    @Data
    @ApiModel("最新消隐信息")
    public static class HiddenInformation{
        @ApiModelProperty(value = "计划编号")
        private String bianHao;
        @ApiModelProperty(value = "计划名称")
        private String name;
        @ApiModelProperty(value = "计划类别")
        private String planType;
        @ApiModelProperty(value = "计划状态")
        private String status;
        @ApiModelProperty(value = "计划时间")
        private String jhsj;
        @ApiModelProperty(value = "开工时间")
        private String kgsj;
        @ApiModelProperty(value = "完工时间")
        private String wgsj;
        @ApiModelProperty(value = "施工工法")
        private String wgSggf;
        @ApiModelProperty(value = "竣工验收时间")
        private String jgsj;
    }
}
