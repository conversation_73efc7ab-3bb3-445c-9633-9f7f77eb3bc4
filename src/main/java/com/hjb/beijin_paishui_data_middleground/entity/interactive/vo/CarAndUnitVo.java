package com.hjb.beijin_paishui_data_middleground.entity.interactive.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 车辆与呼号信息对应Vo
 * @Author: lvhongen
 * @Date: 2025-05-12 14:58
 * @Version： 1.0
 **/
@Data
public class CarAndUnitVo {
    @ApiModelProperty(value = "单元名称")
    private String name;
    @ApiModelProperty(value = "呼号")
    private String callNo;
    @ApiModelProperty(value = "车牌号")
    private String carNo;
    @ApiModelProperty(value = "车辆类型")
    private String carType;
    @ApiModelProperty(value = "单元类型")
    private String typeName;
    @ApiModelProperty(value = "所属公司")
    private String comnm;
    @ApiModelProperty(value = "首要联系电话")
    private String telephone;
}
