package com.hjb.beijin_paishui_data_middleground.entity.interactive.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 雨水小流域Vo
 * @Author: lvhongen
 * @Date: 2025-04-25 11:04
 * @Version： 1.0
 **/
@Data
public class AYsXlyVo {
    @ApiModelProperty(value = "编号")
    private String code;
    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "运营单位")
    private String deptName;
    @ApiModelProperty(value = "班组名称")
    private String banzuName;
}
