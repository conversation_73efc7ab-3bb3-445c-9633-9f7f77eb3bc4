package com.hjb.beijin_paishui_data_middleground.entity.interactive.dto;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 设施台账查询dto
 * @Author: lvhongen
 * @Date: 2025-04-26 09:08
 * @Version： 1.0
 **/
@Data
public class GcTzDto implements Serializable {
    @ApiModelProperty("所属单位")
    private String dept;
    @ApiModelProperty("档案编码")
    private String docid;
    @ApiModelProperty("是否权属")
    private String sfqs;
    @ApiModelProperty("设施序号")
    private String code;
    @ApiModelProperty("使用性质")
    private String syxz;
    @ApiModelProperty("行政区id")
    private String xzjid;
    private static final long serialVersionUID = 1L;
}
