package com.hjb.beijin_paishui_data_middleground.entity.interactive.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 巡查范围Vo
 * @Author: lvhongen
 * @Date: 2025-04-25 11:25
 * @Version： 1.0
 **/
@Data
public class AXunchaVo {
    @ApiModelProperty(value = "编号")
    private String code;
    @ApiModelProperty(value = "名称")
    private String name;
    private String sgeom;
    @ApiModelProperty(value = "运营单位")
    private String deptName;
    @ApiModelProperty(value = "班组名称")
    private String banzuName;
    @ApiModelProperty(value = "周长")
    private String a_length;
    @ApiModelProperty(value = "面积")
    private String a_area;
}
