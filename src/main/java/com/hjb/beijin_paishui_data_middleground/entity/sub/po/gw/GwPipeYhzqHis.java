package com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description: 养护周期历史记录表
 * @Author: lvhongen
 * @Date: 2025-03-19 23:13
 * @Version： 1.0
 **/
@Data
@TableName("yy.gw_pipe_yhzq_his")
public class GwPipeYhzqHis implements Serializable {
    /**
     * 养护ID
     */

    private String yhzqUuid;
    /**
     * 管道ID
     */
    private String yhzqGddm;
    /**
     * 作业时间
     */

    private LocalDateTime yhzqDate;
    /**
     * 存泥深度
     **/
    private String yhzqCnsd;
    /**
     * 沉泥速率
     **/
    private String yhzqCnsl;
    /**
     * 距前一记录天数
     **/
    private String yhzqDaySpan;
    /**
     * 当前计算养护周期
     **/
    private String yhzqCurrent;
    /**
     * 平均养护天数
     **/
    private String yhzqAll;
    private static final long serialVersionUID = 1L;
}
