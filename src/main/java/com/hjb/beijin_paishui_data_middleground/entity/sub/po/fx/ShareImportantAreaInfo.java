package com.hjb.beijin_paishui_data_middleground.entity.sub.po.fx;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 重点地区实体类
 * @Author: lvhongen
 * @Date: 2025-03-21 16:34
 * @Version： 1.0
 **/
@Data
@TableName("FXYJ.SHARE_IMPORTANT_AREA_INFO")
public class ShareImportantAreaInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 名称
     */
    private String name;

    /**
     * 所属单位
     */
    private String comnm;
    /**
     * 政区
     */
    private String adnm;
    /**
     * 经度
     */
    private String LGTD;
    /**
     * 维度
     */
    private String lttd;
}
