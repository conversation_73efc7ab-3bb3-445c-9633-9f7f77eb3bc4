package com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * @Description: 运行风险实体类
 * @Author: lvhongen
 * @Date: 2025-03-21 16:54
 * @Version： 1.0
 **/
@Data
@TableName("yy.gw_wbfx")
public class GwWbfx implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * id
     */

    private String uuid;
    /**
     * 编号
     */
    private String bianHao;
    /**
     * 名称
     */
    private String name;
    /**
     * 状态 （已解决jjj,未解决wjj）
     */
    private String status;
    /**
     * 风险类型（例如占压zhanya，圈占quanzhan，穿凿chuanzao，掩埋chuanzao，气体超标gas，缩进sj，高负荷gfh，断头管dtg，涉河道shd
     * 积水点jsd）
     */
    private String fxType;
    /**
     * 风险时间
     */
    private Date fxsj;
    /**
     * 对应管道信息
     */
    private String oldGwSumInfo;
}
