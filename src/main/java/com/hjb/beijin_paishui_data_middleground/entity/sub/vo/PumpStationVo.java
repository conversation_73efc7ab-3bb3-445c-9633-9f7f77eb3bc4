package com.hjb.beijin_paishui_data_middleground.entity.sub.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @Description: 接收雨污水泵站JSON数据
 * @Author: lvhongen
 * @Date: 2025-05-21 10:42
 * @Version： 1.0
 **/
@Data
public class PumpStationVo {
    @JsonProperty("station_name")
    private String stationName;
    @JsonProperty("station_code")
    private String stationCode;
    @JsonProperty("station_id")
    private String stationId;
    /**
     * 泵房运行状态
     */
    @JsonProperty("KP1_K")
    private String kp1K;
    @JsonProperty("KP2_K")
    private String kp2K;
    @JsonProperty("KP3_K")
    private String kp3K;
    @JsonProperty("KP4_K")
    private String kp4K;
    @JsonProperty("KP5_K")
    private String kp5K;
    @JsonProperty("KP6_K")
    private String kp6K;
    @JsonProperty("KP7_K")
    private String kp7K;
    @JsonProperty("KP8_K")
    private String kp8K;
    @JsonProperty("KP9_K")
    private String kp9K;
    @JsonProperty("KP10_K")
    private String kp10K;
    @JsonProperty("KP11_K")
    private String kp11K;
    @JsonProperty("KP12_K")
    private String kp12K;
    @JsonProperty("KP13_K")
    private String kp13K;
    @JsonProperty("KP14_K")
    private String kp14K;
    /**
     * 累计抽升量
     */
    @JsonProperty("KP_FL")
    private String kpFl;
}
