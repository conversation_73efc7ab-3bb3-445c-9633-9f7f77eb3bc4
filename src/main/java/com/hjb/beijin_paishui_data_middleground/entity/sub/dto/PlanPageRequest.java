package com.hjb.beijin_paishui_data_middleground.entity.sub.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class PlanPageRequest {

    private String modelId;

    private String planName;

    // 指定日期格式
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    // 默认值设置为 1
    private Integer currentPage = 1;

    // 默认值设置为 10
    private Integer pageSize = 10;
}
