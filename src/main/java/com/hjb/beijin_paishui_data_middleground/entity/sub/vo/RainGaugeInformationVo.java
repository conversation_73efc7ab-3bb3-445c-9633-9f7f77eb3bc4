package com.hjb.beijin_paishui_data_middleground.entity.sub.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description: 用于接收雨量计监测数据
 * @Author: lvhongen
 * @Date: 2025-05-09 14:53
 * @Version： 1.0
 **/
@Data
public class RainGaugeInformationVo{
    /**
     * 测站编码
     */
    @JsonProperty("station_code")
    private String stationCode;
    /**
     * 雨量站名称
     */
    @JsonProperty("station_name")
    private String stationName;
    /**
     * 五分钟内雨强
     */
    @JsonProperty("raininess5M")
    private String raininess5M;
    /**
     * 实时雨量
     */
    @JsonProperty("RAIN")
    private String rain;
    /**
     * 收集时间
     */
    @JsonProperty("collect_time")
    private String collectTime;
    /**
     * 记录时间
     */
    @JsonProperty("record_time")
    private String recordTime;
}
