package com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 设施台账表
 */
@Data
@TableName("gw.gw_gc_tz")
public class GwGcTz implements Serializable {
    /**
     * 设置台施表序号
     */
    private Long code;
    /**
     * 是否权属
     */
    private String sfqs;
    private static final long serialVersionUID = 1L;
}
