package com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description: 应急事件
 * @Author: lvhongen
 * @Date: 2025-03-20 16:54
 * @Version： 1.0
 **/
@Data
@TableName("yy.yjsj")
public class Yjsj implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 唯一ID
     */
    private String uuid;
    /**
     * 接报类型
     */
    private String jblx;
    /**
     * 是否权属
     */
    private String isQuanshu;
    /**
     * 发生事件
     */
    private LocalDateTime fssj;
}
