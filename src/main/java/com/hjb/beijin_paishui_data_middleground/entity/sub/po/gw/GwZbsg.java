package com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 周边施工信息表
 * @Author: lvhongen
 * @Date: 2025-03-20 15:28
 * @Version： 1.0
 **/
@Data
@TableName("yy.gw_zbsg")
public class GwZbsg implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    private String uuid;
    /**
     * 项目类型
     */
    private String type;
    /**
     * 工程进展
     */
    private String gczt;
}
