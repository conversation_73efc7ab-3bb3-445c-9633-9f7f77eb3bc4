package com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 混接表
 * @Author: lvhongen
 * @Date: 2025-03-20 15:04
 * @Version： 1.0
 **/
@Data
@TableName("yy.gw_hj")
public class GwHj implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 混接编码
     */

    private String code;
    /**
     * 行政区
     */
    private String xzjid;
    /**
     * 是否完成
     */
    private String sfwc;
}
