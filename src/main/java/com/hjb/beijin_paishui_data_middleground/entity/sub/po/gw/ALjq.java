package com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 立交桥实体类
 * @Author: lvhongen
 * @Date: 2025-04-23 09:06
 * @Version： 1.0
 **/
@Data
@TableName("a_ljq")
public class ALjq implements Serializable {
    private String code;
    private String name;
    private String sgeom;
    private String huanlu;
    private String fangxing;
    private String leixing;
    private String chdw;
    private String weizhi;
    private static final long serialVersionUID = 1L;
}
