package com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.locationtech.jts.geom.Geometry;

import java.io.Serializable;

/**
 * @Description: 管网分公司(运营单位)
 * @Author: lvhongen
 * @Date: 2025-04-21 14:21
 * @Version： 1.0
 **/
@Data
@TableName("gw.a_dept")
public class ADept implements Serializable {
    @TableId
    private String code;
    private String name;
    private String sgeom;
    private static final long serialVersionUID = 1L;
}
