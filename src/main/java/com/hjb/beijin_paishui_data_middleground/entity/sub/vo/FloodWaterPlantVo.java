package com.hjb.beijin_paishui_data_middleground.entity.sub.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.flood.FloodWaterPlantEditDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 水厂检测Vo
 * @Author: lvhongen
 * @Date: 2025-04-29 18:04
 * @Version： 1.0
 **/
@Data
public class FloodWaterPlantVo {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 溢流
     */
    private String overflow;

    /**
     * 跨越
     */
    private String stride;

    /**
     * 水厂详情
     */
    private String waterPlantDetails;

    @TableField(exist = false)
    private List<DetailData> waterPlantDetailsData;

    @Data
    @ApiModel("水厂详细表格")
    class DetailData {

        @ApiModelProperty(name = "waterPlantName", value = "水厂名称")
        private String waterPlantName;

        @ApiModelProperty(name = "instantaneousLift", value = "瞬时抽升量")
        private String instantaneousLift;
        @ApiModelProperty(name = "waterPlantStatusVoList",value = "水厂状态集合")
        private List<WaterPlantStatusVo> waterPlantStatusVoList;
        @ApiModelProperty(name = "frontLevel", value = "栅前液位")
        private String frontLevel;

        @ApiModelProperty(name = "overflowLevel", value = "溢流液位")
        private String overflowLevel;
    }
}

