package com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

@Data
@TableName("yy.cctv_bh")
public class CctvBh {

    /**
     * 主键
     */
    private String bhUuid;

    /**
     * 缺陷的序号
     */
    private Integer bhNum;

    /**
     * 缺陷所在管道代码
     */
    private String bhGddm;

    /**
     * 关联的检测作业记录的id
     */
    private String bhWorkId;

    /**
     * 关联的管道检测记录id
     */
    private String cctvHisUuid;
    /**
     * 方向
     */
    private Integer bhFx;

    /**
     * 功能缺陷类型
     */
    private String bhGnqx;

    /**
     * 功能缺陷程度
     */
    private String bhGnqxcd;

    /**
     * 功能缺陷长度（米）
     */
    private BigDecimal bhGncd;

    /**
     * 结构缺陷类别
     */
    private String bhJgqx;

    /**
     * 结构缺陷程度
     */
    private String bhJgqxcd;

    /**
     * 结构缺陷长度（米）
     */
    private BigDecimal bhJgcd;

    /**
     * 缺陷的位置（米）
     */
    private BigDecimal bhPos;

    /**
     * 缺陷点x坐标
     */
    private BigDecimal bhX;

    /**
     * 缺陷点y坐标
     */
    private BigDecimal bhY;

}
