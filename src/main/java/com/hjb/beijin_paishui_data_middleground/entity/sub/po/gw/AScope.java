package com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.locationtech.jts.geom.Geometry;

import java.io.Serializable;

/**
 * @Description: 桥区建模范围实体类
 * @Author: lvhongen
 * @Date: 2025-04-22 15:58
 * @Version： 1.0
 **/

@TableName("gw.a_scope")
@Data
public class AScope implements Serializable {
    private String code;
    private String name;
    private String sgeom;
    private static final long serialVersionUID = 1L;
}
