package com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * @Description: 设施消隐计划管理表
 * @Author: lvhongen
 * @Date: 2025-03-21 23:01
 * @Version： 1.0
 **/
@Data
@TableName("yy.gw_gxgz")
public class GwGxgz implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * id
     */

    private String uuid;
    /**
     * 完工时间
     */
    private Date wgsj;
    /**
     * 状态 （完工 'wangong'）
     */
    private String status;

    /**
     * 检查井个数
     */
    private Integer wgJcjCount;
    /**
     * 雨水口（座）个数
     */
    private Integer wgYskCount;
    /**
     * 雨水口（处）个数
     */
    private Integer wgBzCount;
    /**
     * 计划类型 （井盖治理为‘jgzl’）
     */

    private String planType;
    /**
     *完工信息
     */
    private String newGwSumInfo;
    /**计划编号**/
    private String bianHao;
    /**计划名称**/
    private String name;
    /**计划时间**/
    private String jhsj;
    /**开工时间**/
    private String kgsj;
    /**施工工法**/
    private String wgSggf;
    /**竣工验收时间**/
    private String jgsj;

}
