package com.hjb.beijin_paishui_data_middleground.entity.sub.vo;

import io.swagger.models.auth.In;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 下凹桥区点位Vo
 * @Author: lvhongen
 * @Date: 2025-05-06 16:49
 * @Version： 1.0
 **/
@Data
public class ShareBrgInfoVo {
    /**
     * 编码
     */
    private String brCode;
    /**
     * 经度
     */
    private String lgtd;
    /**
     * 维度
     */
    private String lttd;
}
