package com.hjb.beijin_paishui_data_middleground.entity.sub.po.fx;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 厂前溢流固定表
 * @Author: lvhongen
 * @Date: 2025-04-18 16:43
 * @Version： 1.0
 **/
@Data
@TableName("cqyl_station_type_data")
public class CqylStationTypeData implements Serializable {
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 组名
     */
    private String groupName;
    /**
     * 编码
     */
    private String code;
    /**
     * 站点名称
     */
    private String name;
    /**
     * 站点类型
     */
    private String type;
    private static final long serialVersionUID = 1L;
}
