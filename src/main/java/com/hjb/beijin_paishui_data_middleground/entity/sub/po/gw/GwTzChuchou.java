package com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 管网台账除臭设施
 * @Author: lvhongen
 * @Date: 2025-03-20 22:45
 * @Version： 1.0
 **/
@Data
@TableName("gw.gw_tz_chuchou")
public class GwTzChuchou implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * id
     */

    private String code;
    /**
     * 使用状态
     */
    private String status;
}
