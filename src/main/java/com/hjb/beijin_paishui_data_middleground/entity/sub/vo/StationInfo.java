package com.hjb.beijin_paishui_data_middleground.entity.sub.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 接收物联网接口测站数据
 */
@Data
public class StationInfo{
    @JsonProperty("station_name")
    private String stationName;
    @JsonProperty("liquidLevel")
    private String liquidLevel;
    @JsonProperty("flowVel")
    private String flowVel;
    @JsonProperty("station_id")
    private String stationId;
    @JsonProperty("flowRate")
    private String flowRate;
    @JsonProperty("station_code")
    private String stationCode;
    @JsonProperty("collect_time")
    private String collectTime;
    @JsonProperty("record_time")
    private String recordTime;
    @JsonProperty("fullLiquidLevel")
    private String fullLiquidLevel;
}
