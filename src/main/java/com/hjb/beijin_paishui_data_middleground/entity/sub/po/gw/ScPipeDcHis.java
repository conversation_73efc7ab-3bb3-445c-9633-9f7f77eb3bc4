package com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * @Description: 管线调查表实体类
 * @Author: lvhongen
 * @Date: 2025-05-09 12:29
 * @Version： 1.0
 **/
@Data
@TableName("yy.sc_pipe_dc_his")
public class ScPipeDcHis implements Serializable {
    /**
     * 管线代码
     */
    private String dcGddm;
    /**
     * 对应的作业id
     */
    private String dcWorkId;
    /**
     * 调查记录人
     */
    private String dcSaveUser;
    /**
     * 调查时间
     */
    private LocalDate dcDate;
    /**
     * 充满度
     */
    private String dcCmd;
    /**
     * 存泥深度
     */
    private String dcCnsd;
    /**
     * 存泥率
     */
    private String dcCnl;

    /**
     * 运行状况
     */
    private String dcYxzk;
    /**
     * 功能级别
     */
    private String dcGndj;

    /**
     * 功能备注
     */
    private String dcGnbz;
    /**
     * 结构级别
     */
    private String dcJgdj;
    /**
     * 结构备注
     */
    private String dcJgbz;
    /**
     * 功能缺陷列表
     */
    private String dcGnbh;
    /**
     * 结构缺陷列表
     */
    private String dcJgbh;
    private static final long serialVersionUID = 1L;
}
