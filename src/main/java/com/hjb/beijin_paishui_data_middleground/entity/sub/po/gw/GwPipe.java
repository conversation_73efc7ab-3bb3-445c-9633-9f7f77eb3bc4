package com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;

import java.io.Serializable;

/**
 * 管线数据表
 */
@Data
@TableName("gw.gw_pipe")
public class GwPipe implements Serializable {
    /**
     * 管道代码
     */
    private String code;
    private String dept;
    /**
     * 管线类型
     */
    private String gxlx;
    /**
     * 使用性质
     */
    private String syxz;
    /**
     * 结构形式 round圆形，rect矩形，tixing
     */
    private String jgxs;
    /**
     * 管长
     */
    private Double pipelength;
    /**
     * 管径
     */
    private String gj;
    private String gk;
    /**
     * 坡度
     */
    private Double pd;
    /**
     * 条数
     */
    private Integer ts;
    /**
     * 设施台账序号
     */
    private Long gctz;
    //mid中型，min小型,big大型,bbb特大型
    private String gjType;
    /**
     * 下游井代码
     */
    private String xyjdm;
    //上游井代码
    private String syjdm;
    private String oldCode;
    /**
     * 上游管地低高
     */
    private Double sygdg;
    /**
     * 下游管地低高
     */
    private Double xygdg;
    /**
     * 特殊管道
     */
    private String tsgd;
    /**
     * 管道材质hnt-混凝土，zq砖砌
     */
    private String gdcz;
    /**
     * 所在位置
     */
    private String szwz;
    /**
     * 使用状态
     */
    private String syzt;
    /**
     * 档案编号
     */
    private String dabh;
    /**
     * 数据来源
     */
    private String sjly;
    /**
     * 竣工时间
     */
    private String jgsj;
    /**
     * 接入时间
     */
    private String jrsj;
    /**
     * 管渠编号
     */
    private String yytz;

    private static final long serialVersionUID = 1L;
}
