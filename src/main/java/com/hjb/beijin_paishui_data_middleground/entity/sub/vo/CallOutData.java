package com.hjb.beijin_paishui_data_middleground.entity.sub.vo;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * 接收呼叫数据
 * "callInTotalCount": 21, // 呼入总数
 * "callInQueueCount": 14, // 呼入技能组总数
 * "callInAnswerCount": 14, // 呼入接听数
 * "callInAnswerRate": "100.00%", // 呼入接通率=呼入接听数/呼入技能组总数；根据具体需求定义。
 * "callOutTotalCount": 30, // 外呼总数
 * "callOutAnswerCount": 21, // 外呼接通数
 * "callOutAnswerRate": "70.00%" // 外呼接通率
 */
@Data
public class CallOutData{
    @SerializedName("callInTotalCount")
    private int callInTotalCount;
    @SerializedName("callInQueueCount")
    private String callInQueueCount;
    @SerializedName("callInAnswerCount")
    private String callInAnswerCount;
    @SerializedName("callInAnswerRate")
    private String callInAnswerRate;
    @SerializedName("callOutTotalCount")
    private int  callOutTotalCount;
    @SerializedName("callOutAnswerCount")
    private String callOutAnswerCount;
    @SerializedName("callOutAnswerRate")
    private String callOutAnswerRate;
}
