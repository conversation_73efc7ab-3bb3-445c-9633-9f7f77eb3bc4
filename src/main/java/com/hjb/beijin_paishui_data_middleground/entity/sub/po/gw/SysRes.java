package com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description: 运行班实体类
 * @Author: lvhongen
 * @Date: 2025-04-23 08:49
 * @Version： 1.0
 **/
@Data
@TableName("sys.sys_res")
public class SysRes implements Serializable {

    private static final long serialVersionUID = 1L;

    private String uuid;

    private String fileName;

    private String resType;

    private LocalDateTime createDate;

    private String relId;

}
