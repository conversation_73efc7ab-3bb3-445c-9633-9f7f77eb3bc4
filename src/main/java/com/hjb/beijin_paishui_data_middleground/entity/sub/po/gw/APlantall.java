package com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.locationtech.jts.geom.Geometry;

import java.io.Serializable;

/**
 * @Description: 再生水厂实体类
 * @Author: lvhongen
 * @Date: 2025-04-23 09:12
 * @Version： 1.0
 **/
@Data
@TableName("a_plantall")
public class APlantall implements Serializable {
    private String code;
    private String name;
    private String sgeom;
    private static final long serialVersionUID = 1L;
}
