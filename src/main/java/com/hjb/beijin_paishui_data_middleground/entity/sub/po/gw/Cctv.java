package com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 管网检测现状信息表
 * @Author: lvhongen
 * @Date: 2025-03-19 23:23
 * @Version： 1.0
 **/
@Data
@TableName("yy.cctv")
public class Cctv implements Serializable {
    /**
     * 检测ID
     */

    private String cctvUuid;
    /**
     * 管道Id
     */
    private String cctvGddm;
    /**
     * 检测作业记录的id
     */
    private String cctvWorkId;
    /**
     * 检测类型（功能（gn）,结构（jg）,功能和结构（gn,jg））
     */
    private String cctvJcType;
    /**
     * 结构缺陷信息编码ID(FS腐蚀，PL破裂，BX变形，CK错口，TJ脱节，SL渗漏，QR侵入)
     * 数据库存储格式（编码ID,编码ID）
     */
    private String cctvJgqx;
    /**
     * 检测时间
     **/
    private String cctvJcDate;
    /**
     * 功能等级
     **/
    private String cctvGndj;
    /**
     * 结构等级
     **/
    private String cctvJgdj;
    /**
     * 缺陷数量
     **/
    private String cctvBhCount;
    /**
     * 功能缺陷描述
     **/
    private String cctvGnqxInfo;
    /**
     * 结构缺陷描述
     **/
    private String cctvJgqxInfo;
    /**
     * 充满度
     **/
    private String cctvCmd;
    /**
     * 存泥深度
     **/
    private String cctvCnsd;
    /**
     * 存泥率
     **/
    private String cctvCnl;
    /**
     * 地区重要性
     **/
    private String cctvDqzyx;
    /**
     * 土壤重要性
     **/
    private String cctvTrzyx;
    /**
     * 老化状况
     **/
    private String cctvLhzk;
    /**
     * 负荷状况
     **/
    private String cctvFhzkDesc;
    /**
     * 检测设备
     **/
    private String cctvJcsb;
    private static final long serialVersionUID = 1L;

}
