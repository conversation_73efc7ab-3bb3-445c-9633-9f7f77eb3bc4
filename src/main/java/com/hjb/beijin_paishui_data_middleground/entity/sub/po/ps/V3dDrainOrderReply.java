package com.hjb.beijin_paishui_data_middleground.entity.sub.po.ps;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 处置记录表实体类
 * @Author: lvhongen
 * @Date: 2025-03-23 18:48
 * @Version： 1.0
 **/
@Data
@TableName("v_3d_drain_order_reply")
public class V3dDrainOrderReply implements Serializable {
    /**
     * 事件编号
     */
    private String orderSn;
    /**
     * 处置单位
     */
    private String replyerCompanyName;
    /**
     * 事件所在位置
     */
    private String locationAddress;
    /**
     * 是否权属 0否1是
     */
    private String bolOwnership;
    private static final long serialVersionUID = 1L;
}
