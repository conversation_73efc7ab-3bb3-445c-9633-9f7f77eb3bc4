package com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 截流井台账表实体类
 * @Author: lvhongen
 * @Date: 2025-03-20 11:31
 * @Version： 1.0
 **/
@Data
@TableName("gw.gw_tz_jlj")
public class GwTzJlj implements Serializable {
    private static final long serialVersionUID = 1L;

    private String uuid;
    private String sfqs;
}
