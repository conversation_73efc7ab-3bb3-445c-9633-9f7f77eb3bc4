package com.hjb.beijin_paishui_data_middleground.entity.sub.po.fx;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 下凹桥区实体类
 * @Author: lvhongen
 * @Date: 2025-03-21 16:32
 * @Version： 1.0
 **/
@Data
@TableName("FXYJ.SHARE_BRG_INFO")
public class ShareBrgInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 编码
     */

    private String brCode;
    /**
     * 名字
     */
    private String brName;
    /**
     * 所属单位
     */
    private String comnm;
    /**
     * 政区
     */
    private String adnm;
    /**
     * 经度
     */
    private String lgtd;
    /**
     * 维度
     */
    private String lttd;
}
