package com.hjb.beijin_paishui_data_middleground.entity.sub.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description: 设施消隐Vo
 * @Author: lvhongen
 * @Date: 2025-03-22 14:17
 * @Version： 1.0
 **/
@Data
public class GxGxgzVo {
    /**
     * id
     */
    private String uuid;
    /**
     * 完工时间
     */
    private LocalDateTime wgsj;
    /**
     * 状态 （完工 'wangong'）
     */
    private String status;

    /**
     * 检查井个数
     */
    @ApiModelProperty(name = "wgJcjCount", value = "维修检查井个数")
    private Integer wgJcjCount;
    /**
     * 雨水口（座）个数
     */
    @ApiModelProperty(name = "wgYskCount", value = "维修雨水口个数")
    private Integer wgYskCount;
    /**
     * 雨水口（处）个数
     */
    @ApiModelProperty(name = "wgBzCount", value = "维修雨水口（处）个数")
    private Integer wgBzCount;
    /**
     * 计划类型 （井盖治理为‘jgzl’）
     */

    private String planType;
    /**
     *完工信息
     */
    private CompletedIndoData newGwSumInfoValue;

    @Data
    @ApiModel("完工信息")
    public static class CompletedIndoData {
        @ApiModelProperty(name = "pipeLengthWs", value = "维修长度")
        private Double pipeLengthWs;
    }
}
