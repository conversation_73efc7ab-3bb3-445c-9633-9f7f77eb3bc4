package com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.models.auth.In;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 泵站数据实体类
 * @Author: lvhongen
 * @Date: 2025-03-19 23:05
 * @Version： 1.0
 **/
@TableName("gw.gw_bengzhan")
@Data
public class GwBengzhan implements Serializable {
    /**
     * 泵站编码
     */

    private Long code;
    /**
     * 泵站类型
     */
    private String leixing;
    /**
     * 设计排水能力
     */
    private Double designCapacity;
    /**
     * 设置轴升周期
     */
    private Integer designCxzq;
    /**
     * 水泵数量
     */
    private Integer sbCount;
    private static final long serialVersionUID = 1L;
}
