package com.hjb.beijin_paishui_data_middleground.entity.sub.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class PlanPageResponse {
    private int currentPage;     // 当前页
    private int totalCount;      // 总记录数
    private int pageCount;       // 总页数
    private int pageSize;        // 当前页显示多少条数据
    private List<Plan> records;  // 数据记录列表

    @Data
    public static class Plan {
        private String planId;       // 方案编号
        private String modelId;      // 模型ID (1: 概化模型, 2: 细化模型)
        private String planName;     // 方案名称
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date startTime;      // 计算开始时间 yyyy-MM-dd HH:mm:ss
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date endTime;        // 计算结束时间 yyyy-MM-dd HH:mm:ss
        private int outputStep;      // 输出步长（单位：分钟）
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date gmtCreate;      // 方案创建时间 yyyy-MM-dd HH:mm:ss

    }

}
