package com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 检测作业记录信息表
 * @Author: lvhongen
 * @Date: 2025-03-23 15:51
 * @Version： 1.0
 **/
@Data
@TableName("yy.cctv_work_record")
public class CctvWorkRecord implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 检测作业编码
     */
    @TableId
    private String uuid;

    private String planUuid;
    /**
     * 检测长度
     */
    private Double workLength;
    /**
     * 缺陷长度
     */
    private Double bhLength;
}
