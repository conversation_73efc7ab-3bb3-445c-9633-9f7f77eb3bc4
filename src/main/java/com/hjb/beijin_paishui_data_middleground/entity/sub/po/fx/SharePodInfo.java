package com.hjb.beijin_paishui_data_middleground.entity.sub.po.fx;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description: 积水点视图
 * @Author: lv<PERSON>en
 * @Date: 2025-04-10 17:03
 * @Version： 1.0
 **/
@Data
@TableName("FXYJ.SHARE_POD_INFO")
public class SharePodInfo implements Serializable {
    private String code;
    private String status;
    private LocalDateTime fdtime;
    private static final long serialVersionUID = 1L;
}
