package com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 结构缺陷信息定义表
 * @Author: lvhongen
 * @Date: 2025-03-23 15:45
 * @Version： 1.0
 **/
@Data
@TableName("yy.cctv_jgqx")
public class CctvJgqx implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 缺陷编码
     */

    private String code;
    /**
     * 缺陷名称
     */
    private String name;
}
