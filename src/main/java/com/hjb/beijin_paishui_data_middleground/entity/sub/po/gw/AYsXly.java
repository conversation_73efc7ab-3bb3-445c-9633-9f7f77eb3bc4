package com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 雨水小流域实体类
 * @Author: lvhongen
 * @Date: 2025-04-23 08:56
 * @Version： 1.0
 **/
@Data
@TableName("a_ys_xly")
public class AYsXly implements Serializable {
    private String code;
    private String name;
    private String dept;
    private String banzu;
    private String sgeom;
    private String river;
    private static final long serialVersionUID = 1L;
}
