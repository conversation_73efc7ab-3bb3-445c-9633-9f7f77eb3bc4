package com.hjb.beijin_paishui_data_middleground.entity.sub.po.ps;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description: 排水事件视图
 * @Author: lvhongen
 * @Date: 2025-03-22 17:19
 * @Version： 1.0
 **/
@Data
@TableName("v_3d_drain_order")
public class V3dDrainOrder implements Serializable {
    private static final long serialVersionUID = 1L;

    private String id;
    /**
     * 事件编号
     */
    private String orderSn;
    /**
     * 登记时候
     */
    private LocalDateTime registerTime;
    /**
     * 事件来源 （1-巡查 6-12345 4-排水热线（96159））
     */
    private String channelCode;
    /**
     * 类型1编码
     */
    private String classifyOne;
    /**
     * 类型1名称
     */
    private String classifyOneName;
    /**
     * 类型2编码
     */
    private String classifyTwo;
    /**
     * 类型2名称
     */
    private String classifyTwoName;
    /**
     * 类型3名称
     */
    private String classifyThreeName;
    /**
     * 问题描述
     */
    private String eventDescription;
    /**
     * 是否权属 0否1是
     */
    private Integer bolOwnership;

}
