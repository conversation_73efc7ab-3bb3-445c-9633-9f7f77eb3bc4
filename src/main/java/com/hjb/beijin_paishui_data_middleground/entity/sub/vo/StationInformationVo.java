package com.hjb.beijin_paishui_data_middleground.entity.sub.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 测站详细信息
 * @Author: lvhongen
 * @Date: 2025-04-27 11:07
 * @Version： 1.0
 **/
@Data
public class StationInformationVo {
    @JsonProperty("code")
    private String code;
    @JsonProperty("name")
    private String name;
    @JsonProperty("lng")
    private String lng;
    @JsonProperty("lat")
    private String lat;
    @JsonProperty("onlineStatus")
    private String onlineStatus;
    @JsonProperty("parent_id")
    private String parentId;
}
