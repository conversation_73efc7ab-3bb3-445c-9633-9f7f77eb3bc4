package com.hjb.beijin_paishui_data_middleground.entity.sub.vo;

import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.drain.DrainEmergencyRepairEditDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description: 运行风险dto
 * @Author: lvhongen
 * @Date: 2025-03-21 18:03
 * @Version： 1.0
 **/
@Data
public class GwWbfxVo {
    /**
     * id
     */
    private String uuid;
    /**
     * 编号
     */
    private String bianHao;
    /**
     * 名称
     */
    private String name;
    /**
     * 状态 （已解决yjj,未解决wjj）
     */
    private String status;
    /**
     * 风险类型（例如占压zhanya，圈占quanzhan，穿凿chuanzao，掩埋chuanzao，气体超标gas，缩进sj，高负荷gfh，断头管dtg，涉河道shd
     * 积水点jsd）
     */
    private String fxType;
    /**
     * 风险时间
     */
    private LocalDateTime fxsj;
    /**
     * 对应管道信息
     */
    private GwPipeData oldGwSumInfo;
    @Data
    @ApiModel("对应管道信息")
    public static class GwPipeData {
        private String jdms;
        /**
         * 使用性质(雨水 ys,污水 ws)
         */
        @ApiModelProperty(name = "syxz", value = "使用性质")
        private String syxz;

        @ApiModelProperty(name = "pipeLength", value = "管道长度")
        private Double pipeLength;
    }
}
