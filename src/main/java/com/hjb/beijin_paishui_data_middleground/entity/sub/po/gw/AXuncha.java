package com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 巡查范围实体类
 * @Author: lvhongen
 * @Date: 2025-04-23 09:04
 * @Version： 1.0
 **/
@Data
@TableName("a_xuncha")
public class AXuncha implements Serializable {
    private String code;
    private String name;
    private String dept;
    private String banzu;
    private String sgeom;
    private static final long serialVersionUID = 1L;
}
