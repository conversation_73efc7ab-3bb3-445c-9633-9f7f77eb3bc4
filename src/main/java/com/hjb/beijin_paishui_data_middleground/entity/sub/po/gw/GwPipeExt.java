package com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 管网额外信息表
 * @Author: lvhongen
 * @Date: 2025-05-08 09:56
 * @Version： 1.0
 ***/
@Data
@TableName("yy.gw_pipe_ext")
public class GwPipeExt implements Serializable {
    /**
     * 管道代码
     */
    private String codeExt;
    /**
     * 当前养护计划
     **/
    private String yhPlan;
    /**
     * 当前监测计划
     **/
    private String cctvPlan;
    /**
     * 当前调查计划
     **/
    private String dcPlan;
    /**
     * 当前消隐计划
     **/
    private String gxgzPlan;
    /**
     * 上次养护时间
     **/
    private String lastYhDate;
    /**
     * 下次养护时间(手工赋值)
     **/
    private String nextYhDate;
    /**
     * 下次养护时间(沉泥计算)
     **/
    private String nextYhDateCal;
    private static final long serialVersionUID = 1L;
}
