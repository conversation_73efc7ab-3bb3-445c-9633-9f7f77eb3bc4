package com.hjb.beijin_paishui_data_middleground.entity.sub.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 测站监测数据Vo
 * @Author: lvhongen
 * @Date: 2025-04-27 14:30
 * @Version： 1.0
 **/
@Data
public class StationMonitoringVo {
    @JsonProperty("station_name")
    private String stationName;
    /**
     * 液位
     */
    @JsonProperty("liquidLevel")
    private String liquidLevel;
    /**
     * 流速
     */
    @JsonProperty("flowVel")
    private String flowVel;
    @JsonProperty("station_code")
    private String stationCode;
}
