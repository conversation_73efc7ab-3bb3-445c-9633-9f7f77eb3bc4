package com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 生产作业记录表实体类
 * @Author: lvhongen
 * @Date: 2025-05-09 10:39
 * @Version： 1.0
 **/
@Data
@TableName("yy.sc_work_record")
public class ScWorkRecord implements Serializable {
    @TableId
    private String uuid;
    private String planUuid;
    private static final long serialVersionUID = 1L;
}
