package com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 管井数据表
 */
@Data
@TableName("gw.gw_well")
public class GwWells implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 井代码
     */
    private String code;
    /**
     * 设施台帐序号
     */
    private Long gctz;
    /**
     * 构筑物类型
     */
    private String gzw;
    /**
     * 使用状态
     */
    private String syzt;
    /**
     * 使用性质
     */
    private String syxz;
    /**
     * 井面高程
     */
    private Double jmgc;
    /**
     * 井底高程
     */
    private Double jdgc;
    /**
     * 井盖材质
     */
    private String jgcz;

    /**
     * 旧井代码
     */
    private String oldCode;
    /**
     * DEM地面高
     */
    private Double demDmg;
    /**
     * 篦子数
     */
    private int bzs;
    /**
     * 档案编号
     */
    private String dabh;
    /**
     * 数据来源
     */
    private String sjly;
    /**
     * 竣工时间
     */
    private String jgsj;
    /**
     * 接入时间
     */
    private String jrsj;
    /**
     * 管渠编号
     */
    private String yytz;

}
