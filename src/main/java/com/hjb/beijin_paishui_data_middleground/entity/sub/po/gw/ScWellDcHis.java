package com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description: 管井调查历史表
 * @Author: lvhongen
 * @Date: 2025-05-09 10:16
 * @Version： 1.0
 **/
@Data
@TableName("yy.sc_well_dc_his")
public class ScWellDcHis implements Serializable {
    /**
     * 井代码
     */
    private String dcJdm;
    /**
     * 对应的作业id
     */
    private String dcWorkId;
    /**
     * 调查记录人
     */
    private String dcSaveUser;
    /**
     * 调查时间
     */
    private LocalDate dcDate;
    /**
     * 是否五防
     */
    private String dcType5f;
    /**
     * 井盖防盗
     */
    private String dcFd;
    /**
     * 是否有防坠落子盖
     */
    private String dcZg;
    /**
     * 是否有防坠护网
     */
    private String dcHw;
    /**
     * 户外安装时间
     */
    private LocalDate dcHwazDate;
    /**
     * 井盖权属
     */
    private String dcQs;
    /**
     * 踏步情况
     */
    private String dcTb;
    /**
     * 井壁情况
     */
    private String dcJb;
    /**
     * 管口状况
     */
    private String dcGk;
    /**
     * 流槽状况
     */
    private String dcLc;
    /**
     * 井底状况
     */
    private String dcJd;
    private static final long serialVersionUID = 1L;
}
