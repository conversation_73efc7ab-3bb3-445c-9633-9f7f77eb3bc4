package com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 管线数据相关信息
 * @Author: lvhongen
 * @Date: 2025-05-08 10:06
 * @Version： 1.0
 **/
@Data
@TableName("gw.gw_pipe_info")
public class GwPipeInfo implements Serializable {
    /**
     * 管道代码
     */
    private String theCode;
    /**
     * 水厂流域
     */
    private String scly;
    /**
     * 以json表示的信息
     */
    private String statInfo;
    private static final long serialVersionUID = 1L;
}
