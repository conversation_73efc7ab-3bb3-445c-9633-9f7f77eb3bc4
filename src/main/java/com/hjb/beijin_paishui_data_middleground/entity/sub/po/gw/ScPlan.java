package com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
/**
 * @Description: 生产计划实体类
 * @Author: lvhongen
 * @Date: 2025-04-28 09:59
 * @Version： 1.0
 **/
@Data
@TableName("yy.sc_plan")
public class ScPlan implements Serializable {
    @TableId
    private String uuid;
    private String bianHao;
    private String tzCode;
    private String status;
    private String tzName;
    /**
     * 原计划时间
     */
    private LocalDate originalPlanDate;
    /**
     * 计划完成时间
     */
    private LocalDate planDate;
    private static final long serialVersionUID = 1L;
}
