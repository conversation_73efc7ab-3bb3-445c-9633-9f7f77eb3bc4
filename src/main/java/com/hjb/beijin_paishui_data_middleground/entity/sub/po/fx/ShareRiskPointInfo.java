package com.hjb.beijin_paishui_data_middleground.entity.sub.po.fx;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 【防汛】风险点实体类
 * @Author: lvhongen
 * @Date: 2025-03-21 16:26
 * @Version： 1.0
 **/
@Data
@TableName("FXYJ.SHARE_RISK_POINT_INFO")
public class ShareRiskPointInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * id
     */

    private String code;
    /**
     * 名称
     */
    private String name;
    /**
     * 所属单位
     */
    private String comnm;
    /**
     * 政区
     */
    private String adnm;
    /**
     * 经度
     */
    private String lgtd;
    /**
     * 维度
     */
    private String lttd;
}
