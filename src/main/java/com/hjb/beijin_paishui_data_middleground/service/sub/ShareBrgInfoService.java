package com.hjb.beijin_paishui_data_middleground.service.sub;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.fx.ShareBrgInfo;

import java.util.List;
import java.util.Map;

/**
 * @Description: 【防汛】下凹桥区service
 * @Author: lvhongen
 * @Date: 2025-03-22 16:20
 * @Version： 1.0
 **/
public interface ShareBrgInfoService extends IService<ShareBrgInfo> {
    Map<String, List<String>> selectDistinctByComnmAndAdnm();
}
