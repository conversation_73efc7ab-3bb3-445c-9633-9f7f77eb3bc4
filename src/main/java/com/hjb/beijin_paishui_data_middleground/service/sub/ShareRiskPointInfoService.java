package com.hjb.beijin_paishui_data_middleground.service.sub;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.fx.ShareRiskPointInfo;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * @Description: 【防汛】风险点Service
 * @Author: lvhongen
 * @Date: 2025-03-22 16:42
 * @Version： 1.0
 **/
public interface ShareRiskPointInfoService extends IService<ShareRiskPointInfo> {
    Map<String, List<String>> selectDistinctByComnmAndAdnm();
}
