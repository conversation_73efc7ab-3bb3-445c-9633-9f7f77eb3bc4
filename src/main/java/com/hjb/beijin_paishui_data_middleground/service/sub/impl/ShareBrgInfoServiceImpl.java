package com.hjb.beijin_paishui_data_middleground.service.sub.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.fx.ShareBrgInfo;
import com.hjb.beijin_paishui_data_middleground.mapper.sub.ShareBrgInfoMapper;
import com.hjb.beijin_paishui_data_middleground.service.sub.ShareBrgInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 【防汛】下凹桥区
 * @Author: lvhongen
 * @Date: 2025-03-22 16:20
 * @Version： 1.0
 **/
@Service
public class ShareBrgInfoServiceImpl extends ServiceImpl<ShareBrgInfoMapper,ShareBrgInfo>
        implements ShareBrgInfoService {
    @Resource
    private ShareBrgInfoMapper shareBrgInfoMapper;
    @Override
    public Map<String, List<String>> selectDistinctByComnmAndAdnm () {
        Map<String,List<String>> map = new HashMap<>();
        List<String> adnmList = shareBrgInfoMapper.selectDistinctByAdnm();
        List<String> comnmList = shareBrgInfoMapper.selectDistinctByComnm();
        map.put("adnm",adnmList);
        map.put("comnm",comnmList);
        return map;
    }
}
