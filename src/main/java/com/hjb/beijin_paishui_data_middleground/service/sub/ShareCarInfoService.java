package com.hjb.beijin_paishui_data_middleground.service.sub;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.fx.ShareCarInfo;

import java.util.List;
import java.util.Map;

/**
 * @Description: 车辆视图Service
 * @Author: lvhongen
 * @Date: 2025-04-01 17:45
 * @Version： 1.0
 **/
public interface ShareCarInfoService extends IService<ShareCarInfo> {
    /**
     * 获取抢险单元查询类型列表
     * @return
     */
    Map<String, List<String>> getEmergencyUnitQueryList ();
}
