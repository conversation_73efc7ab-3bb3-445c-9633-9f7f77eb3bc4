package com.hjb.beijin_paishui_data_middleground.service.sub.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.flood.StationTypeData;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.StationTypeDataMapper;
import com.hjb.beijin_paishui_data_middleground.service.sub.StationTypeDataService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Description: 物理网检测站点清单Service
 * @Author: lvhongen
 * @Date: 2025-04-14 18:11
 * @Version： 1.0
 **/
@Service
public class StationTypeDataServiceImpl extends ServiceImpl<StationTypeDataMapper, StationTypeData> implements
        StationTypeDataService {
    @Resource
    private StationTypeDataMapper stationTypeDataMapper;
    @Override
    public void clearTable () {
        // 创建一个空的 QueryWrapper
        QueryWrapper<StationTypeData> queryWrapper = new QueryWrapper<>();
        stationTypeDataMapper.delete(queryWrapper);
    }
}
