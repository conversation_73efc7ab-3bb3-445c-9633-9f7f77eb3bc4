package com.hjb.beijin_paishui_data_middleground.service.sub.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw.GwPipeYhzqHis;
import com.hjb.beijin_paishui_data_middleground.mapper.sub.GwPipeYhzqHisMapper;
import com.hjb.beijin_paishui_data_middleground.service.sub.GwPipeYhzqHisService;
import org.springframework.stereotype.Service;

/**
 * @Description: 养护周期
 * @Author: lvhongen
 * @Date: 2025-03-19 23:17
 * @Version： 1.0
 **/
@Service
public class GwPipeYhzqHisServiceImpl extends ServiceImpl<GwPipeYhzqHisMapper, GwPipeYhzqHis>
        implements GwPipeYhzqHisService {
}
