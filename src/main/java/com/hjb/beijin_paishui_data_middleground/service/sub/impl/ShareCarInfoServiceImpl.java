package com.hjb.beijin_paishui_data_middleground.service.sub.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.fx.ShareCarInfo;
import com.hjb.beijin_paishui_data_middleground.mapper.sub.ShareCarInfoMapper;
import com.hjb.beijin_paishui_data_middleground.service.sub.ShareCarInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 车辆信息service
 * @Author: lvhongen
 * @Date: 2025-04-01 17:47
 * @Version： 1.0
 **/
@Service
public class ShareCarInfoServiceImpl extends ServiceImpl<ShareCarInfoMapper, ShareCarInfo>
    implements ShareCarInfoService {
    @Resource
    private ShareCarInfoMapper shareCarInfoMapper;
    @Override
    public Map<String, List<String>> getEmergencyUnitQueryList () {
        Map<String,List<String>> map = new HashMap();
        List<String> carComnmQueryList = shareCarInfoMapper.getCarComnmQueryList();
        List<String> carTypeQueryList = shareCarInfoMapper.getCarTypeQueryList();
        map.put("comnm",carComnmQueryList);
        map.put("carType",carTypeQueryList);
        List<String> unitTypeList = new ArrayList<>();
        unitTypeList.add("小单元");
        unitTypeList.add("中单元");
        unitTypeList.add("大单元");
        map.put("unitType",unitTypeList);
        return map;
    }
}
