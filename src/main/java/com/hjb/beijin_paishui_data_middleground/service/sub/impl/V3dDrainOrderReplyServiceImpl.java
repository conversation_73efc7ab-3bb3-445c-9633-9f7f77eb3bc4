package com.hjb.beijin_paishui_data_middleground.service.sub.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.ps.V3dDrainOrderReply;
import com.hjb.beijin_paishui_data_middleground.mapper.sub.V3dDrainOrderMapper;
import com.hjb.beijin_paishui_data_middleground.mapper.sub.V3dDrainOrderReplyMapper;
import com.hjb.beijin_paishui_data_middleground.service.sub.V3dDrainOrderReplyService;
import org.springframework.stereotype.Service;

/**
 * @Description: 处置记录srvice
 * @Author: lvhongen
 * @Date: 2025-03-23 18:56
 * @Version： 1.0
 **/
@Service
public class V3dDrainOrderReplyServiceImpl extends ServiceImpl<V3dDrainOrderReplyMapper, V3dDrainOrderReply>
      implements V3dDrainOrderReplyService {
}
