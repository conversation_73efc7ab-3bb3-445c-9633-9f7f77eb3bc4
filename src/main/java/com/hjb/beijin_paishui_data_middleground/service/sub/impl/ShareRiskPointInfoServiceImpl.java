package com.hjb.beijin_paishui_data_middleground.service.sub.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.fx.ShareRiskPointInfo;
import com.hjb.beijin_paishui_data_middleground.mapper.sub.ShareRiskPointInfoMapper;
import com.hjb.beijin_paishui_data_middleground.service.sub.ShareRiskPointInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 【防汛】风险点service
 * @Author: lvhongen
 * @Date: 2025-03-22 16:43
 * @Version： 1.0
 **/
@Service
public class ShareRiskPointInfoServiceImpl extends ServiceImpl<ShareRiskPointInfoMapper, ShareRiskPointInfo>
        implements ShareRiskPointInfoService {
    @Resource
    private ShareRiskPointInfoMapper shareRiskPointInfoMapper;
    @Override
    public  Map<String, List<String>> selectDistinctByComnmAndAdnm () {
        Map<String,List<String>> map = new HashMap();
        List<String> adnmList = shareRiskPointInfoMapper.selectDistinctByAdnm();
        map.put("adnm",adnmList);
        List<String> comnmList = shareRiskPointInfoMapper.selectDistinctByComnm();
        map.put("comnm",comnmList);
        return map;
    }
}
