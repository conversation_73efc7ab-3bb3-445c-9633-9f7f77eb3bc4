package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.flood.FloodRecessedBridgeEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.flood.FloodRecessedBridge;
import com.hjb.beijin_paishui_data_middleground.service.szls.FloodRecessedBridgeService;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.FloodRecessedBridgeMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【flood_recessed_bridge(下凹桥)】的数据库操作Service实现
* @createDate 2025-01-13 17:25:19
*/
@Service
public class FloodRecessedBridgeServiceImpl extends ServiceImpl<FloodRecessedBridgeMapper, FloodRecessedBridge>
    implements FloodRecessedBridgeService{

    @Override
    public FloodRecessedBridge getReportedData(String searchAdministrative, String searchCompany) {
        List<FloodRecessedBridge> ones = this.list(
                new LambdaQueryWrapper<FloodRecessedBridge>()
                        .eq(FloodRecessedBridge::getIsReported, 1)
                        .eq(StringUtils.isNoneBlank(searchAdministrative), FloodRecessedBridge::getSearchAdministrative, searchAdministrative)
                        .eq(StringUtils.isNoneBlank(searchCompany), FloodRecessedBridge::getSearchCompany, searchCompany)
        );
        if (ones.isEmpty()) {
            ones = Collections.emptyList();
        } else {
            for (FloodRecessedBridge one : ones) {
                one.setRecessedBridgeData(JsonUtil.toList(one.getRecessedBridge(), FloodRecessedBridgeEditDto.TableData.class));
            }
        }
        return ones.get(0);
    }
}




