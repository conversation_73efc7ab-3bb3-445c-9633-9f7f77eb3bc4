package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.event.EventBusinessOfficeUndertakeEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.event.EventBusinessOfficeUndertake;
import com.hjb.beijin_paishui_data_middleground.service.szls.EventBusinessOfficeUndertakeService;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.EventBusinessOfficeUndertakeMapper;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【event_business_office_undertake(按办理类型业务部室承办量)】的数据库操作Service实现
* @createDate 2025-01-14 17:28:05
*/
@Service
public class EventBusinessOfficeUndertakeServiceImpl extends ServiceImpl<EventBusinessOfficeUndertakeMapper, EventBusinessOfficeUndertake>
    implements EventBusinessOfficeUndertakeService{

    @Override
    public EventBusinessOfficeUndertake getReportedData() {
        EventBusinessOfficeUndertake one = this.getOne(
                new LambdaQueryWrapper<EventBusinessOfficeUndertake>()
                        .eq(EventBusinessOfficeUndertake::getIsReported, 1)
        );
        if (Objects.isNull(one)) {
            one = new EventBusinessOfficeUndertake();
        } else {
            one.setBusinessOfficeUndertakeValue(
                    JsonUtil.toObject(one.getBusinessOfficeUndertake(),
                            EventBusinessOfficeUndertakeEditDto.EchartsValue.class)
            );
        }
        return one;
    }
}




