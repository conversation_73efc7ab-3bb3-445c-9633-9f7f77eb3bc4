package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.common.advice.CustomException;
import com.hjb.beijin_paishui_data_middleground.constants.SzlsModuleEnum;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.SzlsModule;
import com.hjb.beijin_paishui_data_middleground.entity.szls.vo.SzlsModuleVo;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.SzlsModuleMapper;
import com.hjb.beijin_paishui_data_middleground.service.szls.SzlsModuleService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 针对表【szls_module(模块表)】的数据库操作Service实现
 * @createDate 2025-01-08 17:05:02
 */
@Service
public class SzlsModuleServiceImpl extends ServiceImpl<SzlsModuleMapper, SzlsModule>
        implements SzlsModuleService {

    @Override
    public List<SzlsModuleVo> tree() {
        List<SzlsModule> list = this.list(
                new LambdaQueryWrapper<SzlsModule>()
                        .orderByAsc(SzlsModule::getCreateTime)
        );
        return buildTree(list, 0L);
    }

    @Override
    public Boolean isReported(String moduleName, Long parentId) {
        SzlsModule szlsModule = this.getOne(
                new LambdaQueryWrapper<SzlsModule>()
                        .eq(SzlsModule::getModuleName, moduleName)
                        .eq(SzlsModule::getParentId, parentId)
        );
        if (Objects.isNull(szlsModule)) {
            throw new CustomException("没有 '" + moduleName + "' 模块配置");
        }
        return szlsModule.getIsReported() == 1;
    }

    private List<SzlsModuleVo> buildTree(List<SzlsModule> allModules, Long parentId) {
        List<SzlsModuleVo> result = new ArrayList<>();

        for (SzlsModule module : allModules) {
            if (module.getParentId().equals(parentId)) {
                SzlsModuleVo szlsModuleVo = new SzlsModuleVo();
                BeanUtils.copyProperties(module, szlsModuleVo);
                // 递归找到所有子模块
                szlsModuleVo.setChildren(buildTree(allModules, module.getId()));
                result.add(szlsModuleVo);
            }
        }

        return result;
    }
}




