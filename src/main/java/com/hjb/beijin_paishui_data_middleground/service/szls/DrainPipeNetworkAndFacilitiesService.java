package com.hjb.beijin_paishui_data_middleground.service.szls;

import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainPipeNetworkAndFacilities;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【drain_pipe_network_and_facilities(管网及设施)】的数据库操作Service
* @createDate 2025-01-10 13:19:38
*/
public interface DrainPipeNetworkAndFacilitiesService extends IService<DrainPipeNetworkAndFacilities> {

    DrainPipeNetworkAndFacilities getReportedData();
}
