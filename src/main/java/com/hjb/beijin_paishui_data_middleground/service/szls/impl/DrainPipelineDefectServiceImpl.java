package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.drain.DrainPipelineDefectEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainPipelineDefect;
import com.hjb.beijin_paishui_data_middleground.service.szls.DrainPipelineDefectService;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.DrainPipelineDefectMapper;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【drain_pipeline_defect(管道缺陷)】的数据库操作Service实现
* @createDate 2025-01-13 11:12:16
*/
@Service
public class DrainPipelineDefectServiceImpl extends ServiceImpl<DrainPipelineDefectMapper, DrainPipelineDefect>
    implements DrainPipelineDefectService{

    @Override
    public DrainPipelineDefect getReportedData() {
        DrainPipelineDefect one = this.getOne(
                new LambdaQueryWrapper<DrainPipelineDefect>()
                        .eq(DrainPipelineDefect::getIsReported, 1)
        );
        if (Objects.isNull(one)) {
            one = new DrainPipelineDefect();
        } else {
            one.setPipelineDefectTypeValue(
                    JsonUtil.toList(one.getPipelineDefectType(),
                            DrainPipelineDefectEditDto.EchartsValue.class));
            one.setPipelineDefectDegreeValue(
                    JsonUtil.toList(one.getPipelineDefectDegree(),
                            DrainPipelineDefectEditDto.EchartsValue.class));
        }
        return one;
    }
}




