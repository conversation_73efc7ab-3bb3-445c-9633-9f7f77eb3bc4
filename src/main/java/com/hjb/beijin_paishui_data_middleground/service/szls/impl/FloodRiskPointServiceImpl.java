package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.flood.FloodRiskPointEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.flood.FloodRiskPoint;
import com.hjb.beijin_paishui_data_middleground.service.szls.FloodRiskPointService;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.FloodRiskPointMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【flood_risk_point(风险点)】的数据库操作Service实现
* @createDate 2025-01-13 17:38:01
*/
@Service
public class FloodRiskPointServiceImpl extends ServiceImpl<FloodRiskPointMapper, FloodRiskPoint>
    implements FloodRiskPointService{

    @Override
    public FloodRiskPoint getReportedData(String searchAdministrative, String searchCompany) {
        List<FloodRiskPoint> ones = this.list(
                new LambdaQueryWrapper<FloodRiskPoint>()
                        .eq(FloodRiskPoint::getIsReported, 1)
                        .eq(StringUtils.isNoneBlank(searchAdministrative), FloodRiskPoint::getSearchAdministrative, searchAdministrative)
                        .eq(StringUtils.isNoneBlank(searchCompany), FloodRiskPoint::getSearchCompany, searchCompany)
        );
        if (ones.isEmpty()) {
            ones = Collections.emptyList();
        } else {
            for (FloodRiskPoint one : ones) {
                one.setRiskPointData(JsonUtil.toList(one.getRiskPoint(), FloodRiskPointEditDto.TableData.class));
            }
        }
        return ones.get(0);
    }
}




