package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.event.EventSubCenterUndertakeEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.event.EventSubCenterUndertake;
import com.hjb.beijin_paishui_data_middleground.service.szls.EventSubCenterUndertakeService;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.EventSubCenterUndertakeMapper;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【event_sub_center_undertake(按办理类型各分中心承办量)】的数据库操作Service实现
* @createDate 2025-01-14 17:24:21
*/
@Service
public class EventSubCenterUndertakeServiceImpl extends ServiceImpl<EventSubCenterUndertakeMapper, EventSubCenterUndertake>
    implements EventSubCenterUndertakeService{

    @Override
    public EventSubCenterUndertake getReportedData() {
        EventSubCenterUndertake one = this.getOne(
                new LambdaQueryWrapper<EventSubCenterUndertake>()
                        .eq(EventSubCenterUndertake::getIsReported, 1)
        );
        if (Objects.isNull(one)) {
            one = new EventSubCenterUndertake();
        } else {
            one.setSubCenterUndertakeValue(
                    JsonUtil.toObject(one.getSubCenterUndertake(),
                            EventSubCenterUndertakeEditDto.EchartsValue.class)
            );
        }
        return one;
    }
}




