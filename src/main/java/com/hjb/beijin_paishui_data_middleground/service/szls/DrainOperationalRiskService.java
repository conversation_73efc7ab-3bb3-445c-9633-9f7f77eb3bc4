package com.hjb.beijin_paishui_data_middleground.service.szls;

import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.drain.DrainOperationalRiskEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainOperationalRisk;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【drain_operational_risk(运行风险)】的数据库操作Service
* @createDate 2025-01-10 14:12:49
*/
public interface DrainOperationalRiskService extends IService<DrainOperationalRisk> {

    List<DrainOperationalRiskEditDto> getReportedData();
}
