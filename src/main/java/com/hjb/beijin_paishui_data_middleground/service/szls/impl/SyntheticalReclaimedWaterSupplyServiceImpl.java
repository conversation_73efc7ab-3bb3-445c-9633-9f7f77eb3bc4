package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical.SyntheticalReclaimedWaterSupply;
import com.hjb.beijin_paishui_data_middleground.service.szls.SyntheticalReclaimedWaterSupplyService;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.SyntheticalReclaimedWaterSupplyMapper;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【synthetical_reclaimed_water_supply(再生水供应)】的数据库操作Service实现
* @createDate 2025-01-10 10:21:56
*/
@Service
public class SyntheticalReclaimedWaterSupplyServiceImpl extends ServiceImpl<SyntheticalReclaimedWaterSupplyMapper, SyntheticalReclaimedWaterSupply>
    implements SyntheticalReclaimedWaterSupplyService{

    @Override
    public SyntheticalReclaimedWaterSupply getReportedData() {
        SyntheticalReclaimedWaterSupply one = this.getOne(
                new LambdaQueryWrapper<SyntheticalReclaimedWaterSupply>()
                        .eq(SyntheticalReclaimedWaterSupply::getIsReported, 1)
        );
        if (Objects.isNull(one)) {
            one = new SyntheticalReclaimedWaterSupply();
        }
        return one;
    }
}




