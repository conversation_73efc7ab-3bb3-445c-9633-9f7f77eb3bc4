package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.synthetical.SyntheticalFloodControlEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical.SyntheticalFloodControl;
import com.hjb.beijin_paishui_data_middleground.service.szls.SyntheticalFloodControlService;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.SyntheticalFloodControlMapper;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【synthetical_flood_control(防汛保障)】的数据库操作Service实现
* @createDate 2025-01-10 11:26:15
*/
@Service
public class SyntheticalFloodControlServiceImpl extends ServiceImpl<SyntheticalFloodControlMapper, SyntheticalFloodControl>
    implements SyntheticalFloodControlService{

    @Override
    public SyntheticalFloodControl getReportedData() {
        SyntheticalFloodControl one = this.getOne(
                new LambdaQueryWrapper<SyntheticalFloodControl>()
                        .eq(SyntheticalFloodControl::getIsReported, 1)
        );
        if (Objects.isNull(one)) {
            one = new SyntheticalFloodControl();
        } else {
            one.setCumulativeLiftYoyValue(
                    JsonUtil.toObject(one.getCumulativeLiftYoy(),
                            SyntheticalFloodControlEditDto.EchartsValue.class)
            );
            one.setFloodControlPersonnelYoyValue(
                    JsonUtil.toObject(one.getFloodControlPersonnelYoy(),
                            SyntheticalFloodControlEditDto.EchartsValue.class)
            );
            one.setWaterDisposalEventYoyValue(
                    JsonUtil.toObject(one.getWaterDisposalEventYoy(),
                            SyntheticalFloodControlEditDto.EchartsValue.class)
            );
        }
        return one;
    }
}




