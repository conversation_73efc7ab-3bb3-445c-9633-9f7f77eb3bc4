package com.hjb.beijin_paishui_data_middleground.service.szls;

import com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical.SyntheticalPipelineOperation;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【synthetical_pipeline_operation(管网运营)】的数据库操作Service
* @createDate 2025-01-10 09:22:01
*/
public interface SyntheticalPipelineOperationService extends IService<SyntheticalPipelineOperation> {

    SyntheticalPipelineOperation getReportedData();
}
