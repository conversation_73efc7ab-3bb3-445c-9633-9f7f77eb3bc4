package com.hjb.beijin_paishui_data_middleground.service.szls.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.waterwork.WaterworkFlowMonitoringEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.waterwork.WaterworkDailyInflow;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.WaterworkDailyInflowMapper;
import com.hjb.beijin_paishui_data_middleground.service.szls.WaterworkDailyInflowService;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
public class WaterworkDailyInflowServiceImpl extends ServiceImpl<WaterworkDailyInflowMapper, WaterworkDailyInflow>
    implements WaterworkDailyInflowService {

    @Override
    public WaterworkDailyInflow getReportedData() {
        WaterworkDailyInflow one = this.getOne(
                new LambdaQueryWrapper<WaterworkDailyInflow>()
                    .eq(WaterworkDailyInflow::getIsReported, 1)
        );
        if(Objects.isNull(one)) {
            one = new  WaterworkDailyInflow();
        } else {
            one.setDailyInflowValue(JsonUtil.toObject(one.getDailyInflow(), WaterworkFlowMonitoringEditDto.EchartsValue.class));
        }
        return one;
    }
}
