package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.drain.DrainOperationalRiskEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainOperationalRisk;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.DrainOperationalRiskMapper;
import com.hjb.beijin_paishui_data_middleground.service.szls.DrainOperationalRiskService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【drain_operational_risk(运行风险)】的数据库操作Service实现
 * @createDate 2025-01-10 14:12:49
 */
@Service
public class DrainOperationalRiskServiceImpl extends ServiceImpl<DrainOperationalRiskMapper, DrainOperationalRisk>
        implements DrainOperationalRiskService {

    @Override
    public List<DrainOperationalRiskEditDto> getReportedData() {
        List<DrainOperationalRisk> ones = this.list(
                new LambdaQueryWrapper<DrainOperationalRisk>()
                        .eq(DrainOperationalRisk::getIsReported, 1)
        );
        List<DrainOperationalRiskEditDto> list = new ArrayList<>();
        if (ones.isEmpty()) ;
        else {
            for (DrainOperationalRisk one : ones) {
                DrainOperationalRiskEditDto drainOperationalRiskEditDto = new DrainOperationalRiskEditDto();
                BeanUtils.copyProperties(one,drainOperationalRiskEditDto);
                drainOperationalRiskEditDto.setAmountCondition(
                        JsonUtil.toList(one.getAmountCondition(), DrainOperationalRiskEditDto.EchartsValue.class)
                );
                drainOperationalRiskEditDto.setRainCondition(
                        JsonUtil.toList(one.getRainCondition(), DrainOperationalRiskEditDto.EchartsValue.class)
                );
                drainOperationalRiskEditDto.setSewageCondition(
                        JsonUtil.toList(one.getSewageCondition(), DrainOperationalRiskEditDto.EchartsValue.class)
                );
                list.add(drainOperationalRiskEditDto);
            }
        }
        return list;
    }
}




