package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainPipeNetworkAndFacilities;
import com.hjb.beijin_paishui_data_middleground.service.szls.DrainPipeNetworkAndFacilitiesService;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.DrainPipeNetworkAndFacilitiesMapper;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【drain_pipe_network_and_facilities(管网及设施)】的数据库操作Service实现
* @createDate 2025-01-10 13:19:38
*/
@Service
public class DrainPipeNetworkAndFacilitiesServiceImpl extends ServiceImpl<DrainPipeNetworkAndFacilitiesMapper, DrainPipeNetworkAndFacilities>
    implements DrainPipeNetworkAndFacilitiesService{

    @Override
    public DrainPipeNetworkAndFacilities getReportedData() {
        DrainPipeNetworkAndFacilities one = this.getOne(
                new LambdaQueryWrapper<DrainPipeNetworkAndFacilities>()
                        .eq(DrainPipeNetworkAndFacilities::getIsReported, 1)
        );
        if (Objects.isNull(one)) {
            one = new DrainPipeNetworkAndFacilities();
        }
        return one;
    }
}




