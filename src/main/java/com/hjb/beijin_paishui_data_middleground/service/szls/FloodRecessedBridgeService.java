package com.hjb.beijin_paishui_data_middleground.service.szls;

import com.hjb.beijin_paishui_data_middleground.entity.szls.po.flood.FloodRecessedBridge;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【flood_recessed_bridge(下凹桥)】的数据库操作Service
* @createDate 2025-01-13 17:25:19
*/
public interface FloodRecessedBridgeService extends IService<FloodRecessedBridge> {

    FloodRecessedBridge getReportedData(String searchAdministrative, String searchCompany);
}
