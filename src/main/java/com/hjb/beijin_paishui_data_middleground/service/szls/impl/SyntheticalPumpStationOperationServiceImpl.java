package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical.SyntheticalPumpStationOperation;
import com.hjb.beijin_paishui_data_middleground.service.szls.SyntheticalPumpStationOperationService;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.SyntheticalPumpStationOperationMapper;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【synthetical_pump_station_operation(泵站运营)】的数据库操作Service实现
* @createDate 2025-01-10 09:48:57
*/
@Service
public class SyntheticalPumpStationOperationServiceImpl extends ServiceImpl<SyntheticalPumpStationOperationMapper, SyntheticalPumpStationOperation>
    implements SyntheticalPumpStationOperationService{

    @Override
    public SyntheticalPumpStationOperation getReportedData() {
        SyntheticalPumpStationOperation one = this.getOne(
                new LambdaQueryWrapper<SyntheticalPumpStationOperation>()
                        .eq(SyntheticalPumpStationOperation::getIsReported, 1)
        );
        if (Objects.isNull(one)) {
            one = new SyntheticalPumpStationOperation();
        }
        return one;
    }
}




