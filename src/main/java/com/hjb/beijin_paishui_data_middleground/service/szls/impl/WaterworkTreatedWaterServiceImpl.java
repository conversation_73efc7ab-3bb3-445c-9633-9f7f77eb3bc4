package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.waterwork.WaterworkTreatedWaterEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.waterwork.WaterworkTreatedWater;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.WaterworkTreatedWaterMapper;
import com.hjb.beijin_paishui_data_middleground.service.szls.WaterworkTreatedWaterService;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @createDate 2025/7/30 17:52
 * @description
 */
@Service
public class WaterworkTreatedWaterServiceImpl extends ServiceImpl<WaterworkTreatedWaterMapper, WaterworkTreatedWater>
implements WaterworkTreatedWaterService {

    @Override
    public WaterworkTreatedWater getReportedData() {
        WaterworkTreatedWater one = this.getOne(
                new LambdaQueryWrapper<WaterworkTreatedWater>()
                        .eq(WaterworkTreatedWater::getIsReported, 1)
        );
        if(Objects.isNull(one)) {
            one = new WaterworkTreatedWater();
        } else {
            one.setTreatedWaterValue(JsonUtil.toObject(one.getTreatedWater(), WaterworkTreatedWaterEditDto.EchartsValue.class));
        }
        return one;
    }
}
