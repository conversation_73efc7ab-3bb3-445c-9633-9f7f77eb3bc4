package com.hjb.beijin_paishui_data_middleground.service.szls;

import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainAncillaryFacilities;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【drain_ancillary_facilities(附属设施)】的数据库操作Service
* @createDate 2025-01-10 13:27:08
*/
public interface DrainAncillaryFacilitiesService extends IService<DrainAncillaryFacilities> {

    DrainAncillaryFacilities getReportedData();
}
