package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.flood.FloodWaterPlantEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.flood.FloodWaterPlant;
import com.hjb.beijin_paishui_data_middleground.service.szls.FloodWaterPlantService;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.FloodWaterPlantMapper;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【flood_water_plant(水厂)】的数据库操作Service实现
* @createDate 2025-01-13 15:35:07
*/
@Service
public class FloodWaterPlantServiceImpl extends ServiceImpl<FloodWaterPlantMapper, FloodWaterPlant>
    implements FloodWaterPlantService{

    @Override
    public FloodWaterPlant getReportedData() {
        FloodWaterPlant one = this.getOne(
                new LambdaQueryWrapper<FloodWaterPlant>()
                        .eq(FloodWaterPlant::getIsReported, 1)
        );
        if (Objects.isNull(one)) {
            one = new FloodWaterPlant();
        } else {
            one.setWaterPlantDetailsData(JsonUtil.toList(one.getWaterPlantDetails(), FloodWaterPlantEditDto.DetailData.class));
        }
        return one;
    }
}




