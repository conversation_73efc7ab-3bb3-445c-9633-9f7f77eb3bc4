package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.drain.DrainPipeNetworkTestingEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainPipeNetworkTesting;
import com.hjb.beijin_paishui_data_middleground.service.szls.DrainPipeNetworkTestingService;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.DrainPipeNetworkTestingMapper;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【drain_pipe_network_testing(管网检测)】的数据库操作Service实现
* @createDate 2025-01-13 10:39:13
*/
@Service
public class DrainPipeNetworkTestingServiceImpl extends ServiceImpl<DrainPipeNetworkTestingMapper, DrainPipeNetworkTesting>
    implements DrainPipeNetworkTestingService{

    @Override
    public DrainPipeNetworkTesting getReportedData() {
        DrainPipeNetworkTesting one = this.getOne(
                new LambdaQueryWrapper<DrainPipeNetworkTesting>()
                        .eq(DrainPipeNetworkTesting::getIsReported, 1)
        );
        if (Objects.isNull(one)) {
            one = new DrainPipeNetworkTesting();
        } else {
            one.setPipeNetworkInspectionValue(
                    JsonUtil.toObject(one.getPipeNetworkInspection(),
                            DrainPipeNetworkTestingEditDto.EchartsValue.class));
        }
        return one;
    }
}




