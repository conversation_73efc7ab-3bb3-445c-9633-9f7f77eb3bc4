package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.event.EventFacilityAppealEventEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.event.EventFacilityAppealEvent;
import com.hjb.beijin_paishui_data_middleground.service.szls.EventFacilityAppealEventService;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.EventFacilityAppealEventMapper;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【event_facility_appeal_event(设施诉求类事件)】的数据库操作Service实现
* @createDate 2025-01-14 16:42:19
*/
@Service
public class EventFacilityAppealEventServiceImpl extends ServiceImpl<EventFacilityAppealEventMapper, EventFacilityAppealEvent>
    implements EventFacilityAppealEventService{

    @Override
    public EventFacilityAppealEvent getReportedData() {
        EventFacilityAppealEvent one = this.getOne(
                new LambdaQueryWrapper<EventFacilityAppealEvent>()
                        .eq(EventFacilityAppealEvent::getIsReported, 1)
        );
        if (Objects.isNull(one)) {
            one = new EventFacilityAppealEvent();
        } else {
            one.setEventClassificationData(
                    JsonUtil.toList(one.getEventClassification(), EventFacilityAppealEventEditDto.TableData.class)
            );
        }
        return one;
    }
}




