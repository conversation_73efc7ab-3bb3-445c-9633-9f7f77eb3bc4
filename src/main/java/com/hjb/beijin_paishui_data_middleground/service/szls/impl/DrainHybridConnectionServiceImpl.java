package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.drain.DrainHybridConnectionEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainHybridConnection;
import com.hjb.beijin_paishui_data_middleground.service.szls.DrainHybridConnectionService;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.DrainHybridConnectionMapper;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【drain_hybrid_connection(混接)】的数据库操作Service实现
* @createDate 2025-01-10 13:44:25
*/
@Service
public class DrainHybridConnectionServiceImpl extends ServiceImpl<DrainHybridConnectionMapper, DrainHybridConnection>
    implements DrainHybridConnectionService{

    @Override
    public DrainHybridConnection getReportedData() {
        DrainHybridConnection one = this.getOne(
                new LambdaQueryWrapper<DrainHybridConnection>()
                        .eq(DrainHybridConnection::getIsReported, 1)
        );
        if (Objects.isNull(one)) {
            one = new DrainHybridConnection();
        } else {
            one.setAdministrativeProportionValue(
                    JsonUtil.toList(one.getAdministrativeProportion(),
                            DrainHybridConnectionEditDto.EchartsValue.class));
        }
        return one;
    }
}




