package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.flood.FloodEmergencyUnitEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.flood.FloodRecessedBridgeEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.flood.FloodEmergencyUnit;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.flood.FloodRiskPoint;
import com.hjb.beijin_paishui_data_middleground.service.szls.FloodEmergencyUnitService;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.FloodEmergencyUnitMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【flood_emergency_unit(抢险单元)】的数据库操作Service实现
* @createDate 2025-01-15 09:39:57
*/
@Service
public class FloodEmergencyUnitServiceImpl extends ServiceImpl<FloodEmergencyUnitMapper, FloodEmergencyUnit>
    implements FloodEmergencyUnitService{

    @Override
    public FloodEmergencyUnit getReportedData(String searchType, String searchCompany, String searchUnit) {
        List<FloodEmergencyUnit> ones = this.list(
                new LambdaQueryWrapper<FloodEmergencyUnit>()
                        .eq(FloodEmergencyUnit::getIsReported, 1)
                        .eq(StringUtils.isNotBlank(searchType), FloodEmergencyUnit::getSearchType, searchType)
                        .eq(StringUtils.isNoneBlank(searchCompany), FloodEmergencyUnit::getSearchCompany, searchCompany)
                        .eq(StringUtils.isNoneBlank(searchUnit), FloodEmergencyUnit::getSearchUnit, searchUnit)
        );
        if (ones.isEmpty()) {
            ones = Collections.emptyList();
        } else {
            for (FloodEmergencyUnit one : ones) {
                one.setEmergencyUnitData(JsonUtil.toList(one.getEmergencyUnit(), FloodEmergencyUnitEditDto.TableData.class));
            }
        }
        return ones.get(0);
    }
}




