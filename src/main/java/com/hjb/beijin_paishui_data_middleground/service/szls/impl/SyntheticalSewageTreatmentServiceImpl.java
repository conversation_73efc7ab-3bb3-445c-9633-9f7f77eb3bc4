package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.synthetical.SyntheticalSewageTreatmentEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical.SyntheticalSewageTreatment;
import com.hjb.beijin_paishui_data_middleground.service.szls.SyntheticalSewageTreatmentService;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.SyntheticalSewageTreatmentMapper;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【synthetical_wewage_treatment(污水处理)】的数据库操作Service实现
* @createDate 2025-01-10 10:02:44
*/
@Service
public class SyntheticalSewageTreatmentServiceImpl extends ServiceImpl<SyntheticalSewageTreatmentMapper, SyntheticalSewageTreatment>
    implements SyntheticalSewageTreatmentService {

    @Override
    public SyntheticalSewageTreatment getReportedData() {
        SyntheticalSewageTreatment one = this.getOne(
                new LambdaQueryWrapper<SyntheticalSewageTreatment>()
                        .eq(SyntheticalSewageTreatment::getIsReported, 1)
        );
        if (Objects.isNull(one)) {
            one = new SyntheticalSewageTreatment();
        } else {
            one.setReclaimedWaterDataList(JsonUtil.toList(one.getReclaimedWaterData(), SyntheticalSewageTreatmentEditDto.ReclaimedWater.class));
        }
        return one;
    }
}




