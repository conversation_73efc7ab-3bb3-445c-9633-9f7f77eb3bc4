package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical.SyntheticalPipelineOperation;
import com.hjb.beijin_paishui_data_middleground.service.szls.SyntheticalPipelineOperationService;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.SyntheticalPipelineOperationMapper;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【synthetical_pipeline_operation(管网运营)】的数据库操作Service实现
* @createDate 2025-01-10 09:22:01
*/
@Service
public class SyntheticalPipelineOperationServiceImpl extends ServiceImpl<SyntheticalPipelineOperationMapper, SyntheticalPipelineOperation>
    implements SyntheticalPipelineOperationService{

    @Override
    public SyntheticalPipelineOperation getReportedData() {
        SyntheticalPipelineOperation one = this.getOne(
                new LambdaQueryWrapper<SyntheticalPipelineOperation>()
                        .eq(SyntheticalPipelineOperation::getIsReported, 1)
        );
        if (Objects.isNull(one)) {
            one = new SyntheticalPipelineOperation();
        }
        return one;
    }
}




