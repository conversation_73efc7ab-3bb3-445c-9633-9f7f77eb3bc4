package com.hjb.beijin_paishui_data_middleground.service.szls;

import com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical.SyntheticalSewageTreatment;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【synthetical_wewage_treatment(污水处理)】的数据库操作Service
* @createDate 2025-01-10 10:02:44
*/
public interface SyntheticalSewageTreatmentService extends IService<SyntheticalSewageTreatment> {

    SyntheticalSewageTreatment getReportedData();
}
