package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.drain.DrainMaintenanceProductionEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainMaintenanceProduction;
import com.hjb.beijin_paishui_data_middleground.service.szls.DrainMaintenanceProductionService;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.DrainMaintenanceProductionMapper;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【drain_maintenance_production(养护生产)】的数据库操作Service实现
* @createDate 2025-01-10 13:33:26
*/
@Service
public class DrainMaintenanceProductionServiceImpl extends ServiceImpl<DrainMaintenanceProductionMapper, DrainMaintenanceProduction>
    implements DrainMaintenanceProductionService{

    @Override
    public DrainMaintenanceProduction getReportedData() {
        DrainMaintenanceProduction one = this.getOne(
                new LambdaQueryWrapper<DrainMaintenanceProduction>()
                        .eq(DrainMaintenanceProduction::getIsReported, 1)
        );
        if (Objects.isNull(one)) {
            one = new DrainMaintenanceProduction();
        } else {
            one.setAnnualTrendValue(JsonUtil.toObject(one.getAnnualTrend(), DrainMaintenanceProductionEditDto.EchartsValue.class));
        }
        return one;
    }
}




