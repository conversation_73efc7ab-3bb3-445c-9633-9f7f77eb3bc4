package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainAncillaryFacilities;
import com.hjb.beijin_paishui_data_middleground.service.szls.DrainAncillaryFacilitiesService;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.DrainAncillaryFacilitiesMapper;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【drain_ancillary_facilities(附属设施)】的数据库操作Service实现
* @createDate 2025-01-10 13:27:08
*/
@Service
public class DrainAncillaryFacilitiesServiceImpl extends ServiceImpl<DrainAncillaryFacilitiesMapper, DrainAncillaryFacilities>
    implements DrainAncillaryFacilitiesService{

    @Override
    public DrainAncillaryFacilities getReportedData() {
        DrainAncillaryFacilities one = this.getOne(
                new LambdaQueryWrapper<DrainAncillaryFacilities>()
                        .eq(DrainAncillaryFacilities::getIsReported, 1)
        );
        if (Objects.isNull(one)) {
            one = new DrainAncillaryFacilities();
        }
        return one;
    }
}




