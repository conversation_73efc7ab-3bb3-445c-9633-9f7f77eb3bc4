package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.flood.FloodRealTimeRainfallEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.flood.FloodRealTimeRainfall;
import com.hjb.beijin_paishui_data_middleground.service.szls.FloodRealTimeRainfallService;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.FloodRealTimeRainfallMapper;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【flood_real_time_rainfall(实时雨量)】的数据库操作Service实现
* @createDate 2025-01-13 16:43:32
*/
@Service
public class FloodRealTimeRainfallServiceImpl extends ServiceImpl<FloodRealTimeRainfallMapper, FloodRealTimeRainfall>
    implements FloodRealTimeRainfallService{

    @Override
    public FloodRealTimeRainfall getReportedData() {
        FloodRealTimeRainfall one = this.getOne(
                new LambdaQueryWrapper<FloodRealTimeRainfall>()
                        .eq(FloodRealTimeRainfall::getIsReported, 1)
        );
        if (Objects.isNull(one)) {
            one = new FloodRealTimeRainfall();
        } else {
            one.setRealTimeRainfallValue(JsonUtil.toObject(one.getRealTimeRainfall(), FloodRealTimeRainfallEditDto.EchartsValue.class));
        }
        return one;
    }
}




