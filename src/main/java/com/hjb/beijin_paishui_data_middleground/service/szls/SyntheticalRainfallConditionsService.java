package com.hjb.beijin_paishui_data_middleground.service.szls;

import com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical.SyntheticalRainfallConditions;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hjb.beijin_paishui_data_middleground.entity.szls.vo.SyntheticalRainfallConditionsVo;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【synthetical_rainfall_conditions(降雨情况)】的数据库操作Service
* @createDate 2025-01-10 10:40:16
*/
public interface SyntheticalRainfallConditionsService extends IService<SyntheticalRainfallConditions> {

    List<SyntheticalRainfallConditions> getReportedData(String year);

    SyntheticalRainfallConditionsVo getReportedDataByManage(String year);
}
