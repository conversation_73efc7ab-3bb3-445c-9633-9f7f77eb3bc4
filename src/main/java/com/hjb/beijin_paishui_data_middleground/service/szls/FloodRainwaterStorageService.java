package com.hjb.beijin_paishui_data_middleground.service.szls;

import com.hjb.beijin_paishui_data_middleground.entity.szls.po.flood.FloodRainwaterStorage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【flood_rainwater_storage(雨水蓄排)】的数据库操作Service
* @createDate 2025-01-13 16:18:18
*/
public interface FloodRainwaterStorageService extends IService<FloodRainwaterStorage> {

    List<FloodRainwaterStorage> getReportedData(String company);
    FloodRainwaterStorage getReportedDataOne(String company);
}
