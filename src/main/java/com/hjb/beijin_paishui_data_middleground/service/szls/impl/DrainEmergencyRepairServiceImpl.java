package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.drain.DrainEmergencyRepairEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainEmergencyRepair;
import com.hjb.beijin_paishui_data_middleground.service.szls.DrainEmergencyRepairService;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.DrainEmergencyRepairMapper;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【drain_emergency_repair(应急抢修)】的数据库操作Service实现
* @createDate 2025-01-13 11:38:35
*/
@Service
public class DrainEmergencyRepairServiceImpl extends ServiceImpl<DrainEmergencyRepairMapper, DrainEmergencyRepair>
    implements DrainEmergencyRepairService{

    @Override
    public DrainEmergencyRepair getReportedData() {
        DrainEmergencyRepair one = this.getOne(
                new LambdaQueryWrapper<DrainEmergencyRepair>()
                        .eq(DrainEmergencyRepair::getIsReported, 1)
        );
        if (Objects.isNull(one)) {
            one = new DrainEmergencyRepair();
        } else {
            one.setEmergencyRepairAllValue(
                    JsonUtil.toObject(one.getEmergencyRepairAll(), DrainEmergencyRepairEditDto.BaseData.class));
            one.setEmergencyRescueValue(
                    JsonUtil.toObject(one.getEmergencyRescue(), DrainEmergencyRepairEditDto.BaseData.class)
            );
            one.setRushToRepairValue(
                    JsonUtil.toObject(one.getRushToRepair(), DrainEmergencyRepairEditDto.BaseData.class)
            );
            one.setFloodPreventionValue(
                    JsonUtil.toObject(one.getFloodPrevention(), DrainEmergencyRepairEditDto.BaseData.class)
            );
        }
        return one;
    }
}




