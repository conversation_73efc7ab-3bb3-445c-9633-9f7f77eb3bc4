package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.common.advice.CustomException;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseCode;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.SzlsPlan;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.SzlsPlanMapper;
import com.hjb.beijin_paishui_data_middleground.service.reception.PlanService;
import com.hjb.beijin_paishui_data_middleground.service.szls.SzlsPlanService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileWriter;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【szls_plan(淹没方案表)】的数据库操作Service实现
 * @createDate 2025-07-31 15:40:58
 */
@Service
@Slf4j
public class SzlsPlanServiceImpl extends ServiceImpl<SzlsPlanMapper, SzlsPlan>
        implements SzlsPlanService {

    @Resource
    private PlanService planService;

    @Value("${app.plan_save_path}")
    private String planSavePath;

    @Async("planTaskExecutor")
    public void processWaterDepthTask(String planId, LocalDateTime startTime, LocalDateTime endTime, int stepMinutes) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime current = startTime;
        int callIndex = 1;

        // 构建目录路径
        String dirPath = planSavePath + File.separator + planId;
        File dir = new File(dirPath);

        // 如果目录存在，先清空
        if (dir.exists() && dir.isDirectory()) {
            File[] files = dir.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isFile()) {
                        boolean deleted = file.delete();
                        if (!deleted) {
                            System.err.println("无法删除旧文件：" + file.getAbsolutePath());
                        }
                    }
                }
            }
        } else {
            dir.mkdirs(); // 若目录不存在则创建
        }

        while (!current.isAfter(endTime)) {
            String timeStr = current.format(formatter);
            try {
                List<Integer> result = planService.waterDepthByPlanId(planId, timeStr);
                log.info("调用成功：{}，结果：{}", timeStr, result);

                List<Integer> gridIdArray = new ArrayList<>();
                List<Integer> valueArray = new ArrayList<>();

                for (int i = 0; i < result.size(); i++) {
                    gridIdArray.add(i);
                    valueArray.add(result.get(i));
                }

                JSONObject gridIdJson = new JSONObject();
                gridIdJson.put("GridIDArray", gridIdArray);

                JSONObject valueJson = new JSONObject();
                valueJson.put("ValueArray", valueArray);

                try (FileWriter gridWriter = new FileWriter(new File(dir, "GridIDArray" + callIndex + ".json"));
                     FileWriter valueWriter = new FileWriter(new File(dir, "ValueArray" + callIndex + ".json"))) {

                    gridWriter.write(JSON.toJSONString(gridIdJson, JSONWriter.Feature.PrettyFormat));
                    valueWriter.write(JSON.toJSONString(valueJson, JSONWriter.Feature.PrettyFormat));
                }

                log.info("写入完成：第 {} 次数据", callIndex);

            } catch (Exception e) {
                log.error("调用或写文件失败：{} 错误：{}", timeStr, e.getMessage());
                throw new CustomException(ResponseCode.FAILURE, "写入文件失败");
            }

            current = current.plusMinutes(stepMinutes);
            callIndex++;
        }

        SzlsPlan szlsPlan = getOne(new LambdaQueryWrapper<SzlsPlan>()
                .eq(SzlsPlan::getPlanId, planId));
        if (szlsPlan != null) {
            szlsPlan.setDataState(1);
            updateById(szlsPlan);
        }

        log.info("planId={} 的所有水深任务执行完毕", planId);
    }


}




