package com.hjb.beijin_paishui_data_middleground.service.szls;

import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainMaintenanceProduction;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【drain_maintenance_production(养护生产)】的数据库操作Service
* @createDate 2025-01-10 13:33:26
*/
public interface DrainMaintenanceProductionService extends IService<DrainMaintenanceProduction> {

    DrainMaintenanceProduction getReportedData();
}
