package com.hjb.beijin_paishui_data_middleground.service.szls;

import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainPipelineDefect;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【drain_pipeline_defect(管道缺陷)】的数据库操作Service
* @createDate 2025-01-13 11:12:17
*/
public interface DrainPipelineDefectService extends IService<DrainPipelineDefect> {

    DrainPipelineDefect getReportedData();
}
