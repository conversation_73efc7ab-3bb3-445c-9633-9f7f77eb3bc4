package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainSpecialFacility;
import com.hjb.beijin_paishui_data_middleground.service.szls.DrainSpecialFacilityService;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.DrainSpecialFacilityMapper;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【drain_special_facility(特殊设施)】的数据库操作Service实现
* @createDate 2025-01-10 13:31:54
*/
@Service
public class DrainSpecialFacilityServiceImpl extends ServiceImpl<DrainSpecialFacilityMapper, DrainSpecialFacility>
    implements DrainSpecialFacilityService{

    @Override
    public DrainSpecialFacility getReportedData() {
        DrainSpecialFacility one = this.getOne(
                new LambdaQueryWrapper<DrainSpecialFacility>()
                        .eq(DrainSpecialFacility::getIsReported, 1)
        );
        if (Objects.isNull(one)) {
            one = new DrainSpecialFacility();
        }
        return one;
    }
}




