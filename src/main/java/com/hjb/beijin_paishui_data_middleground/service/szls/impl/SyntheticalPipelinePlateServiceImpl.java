package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical.SyntheticalPipelinePlate;
import com.hjb.beijin_paishui_data_middleground.service.szls.SyntheticalPipelinePlateService;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.SyntheticalPipelinePlateMapper;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【synthetical_pipeline_plate(管网板块)】的数据库操作Service实现
* @createDate 2025-01-10 11:08:26
*/
@Service
public class SyntheticalPipelinePlateServiceImpl extends ServiceImpl<SyntheticalPipelinePlateMapper, SyntheticalPipelinePlate>
    implements SyntheticalPipelinePlateService{

    @Override
    public SyntheticalPipelinePlate getReportedData() {
        SyntheticalPipelinePlate one = this.getOne(
                new LambdaQueryWrapper<SyntheticalPipelinePlate>()
                        .eq(SyntheticalPipelinePlate::getIsReported, 1)
        );
        if (Objects.isNull(one)) {
            one = new SyntheticalPipelinePlate();
        }
        return one;
    }
}




