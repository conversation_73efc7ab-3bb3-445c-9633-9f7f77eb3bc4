package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.flood.FloodRainwaterStorage;
import com.hjb.beijin_paishui_data_middleground.service.szls.FloodRainwaterStorageService;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.FloodRainwaterStorageMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【flood_rainwater_storage(雨水蓄排)】的数据库操作Service实现
* @createDate 2025-01-13 16:18:18
*/
@Service
public class FloodRainwaterStorageServiceImpl extends ServiceImpl<FloodRainwaterStorageMapper, FloodRainwaterStorage>
    implements FloodRainwaterStorageService{
   @Override
    public List<FloodRainwaterStorage> getReportedData(String company) {
        List<FloodRainwaterStorage> ones = this.list(
                new LambdaQueryWrapper<FloodRainwaterStorage>()
                        .eq(FloodRainwaterStorage::getIsReported, 1)
                        .eq(StringUtils.isNoneBlank(company), FloodRainwaterStorage::getSearchCompany, company)
        );
        if (ones.isEmpty()) {
            ones = Collections.emptyList();
        }
        return ones;
    }

    @Override
    public FloodRainwaterStorage getReportedDataOne (String company) {
        List<FloodRainwaterStorage> ones = this.list(
                new LambdaQueryWrapper<FloodRainwaterStorage>()
                        .eq(FloodRainwaterStorage::getIsReported, 1)
                        .eq(StringUtils.isNoneBlank(company), FloodRainwaterStorage::getSearchCompany, company)
        );
        FloodRainwaterStorage floodRainwaterStorage = new FloodRainwaterStorage();
        if (ones.size() > 0)
            BeanUtils.copyProperties(ones.get(0),floodRainwaterStorage);
        return floodRainwaterStorage;
    }
}




