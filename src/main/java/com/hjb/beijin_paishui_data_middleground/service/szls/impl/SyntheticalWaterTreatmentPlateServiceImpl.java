package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical.SyntheticalWaterTreatmentPlate;
import com.hjb.beijin_paishui_data_middleground.service.szls.SyntheticalWaterTreatmentPlateService;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.SyntheticalWaterTreatmentPlateMapper;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【synthetical_water_treatment_plate(水处理板块)】的数据库操作Service实现
* @createDate 2025-01-10 11:18:58
*/
@Service
public class SyntheticalWaterTreatmentPlateServiceImpl extends ServiceImpl<SyntheticalWaterTreatmentPlateMapper, SyntheticalWaterTreatmentPlate>
    implements SyntheticalWaterTreatmentPlateService{

    @Override
    public SyntheticalWaterTreatmentPlate getReportedData() {
        SyntheticalWaterTreatmentPlate one = this.getOne(
                new LambdaQueryWrapper<SyntheticalWaterTreatmentPlate>()
                        .eq(SyntheticalWaterTreatmentPlate::getIsReported, 1)
        );
        if (Objects.isNull(one)) {
            one = new SyntheticalWaterTreatmentPlate();
        }
        return one;
    }
}




