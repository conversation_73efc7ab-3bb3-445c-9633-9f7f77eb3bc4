package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.flood.FloodPipeNetworkMonitorEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.flood.FloodPipeNetworkMonitor;
import com.hjb.beijin_paishui_data_middleground.service.szls.FloodPipeNetworkMonitorService;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.FloodPipeNetworkMonitorMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【flood_pipe_network_monitor(管网监测)】的数据库操作Service实现
* @createDate 2025-01-13 16:47:44
*/
@Service
public class FloodPipeNetworkMonitorServiceImpl extends ServiceImpl<FloodPipeNetworkMonitorMapper, FloodPipeNetworkMonitor>
    implements FloodPipeNetworkMonitorService{

    @Override
    public List<FloodPipeNetworkMonitorEditDto> getReportedData(String type) {
        List<FloodPipeNetworkMonitorEditDto> list= new ArrayList<>();
        List<FloodPipeNetworkMonitor> ones = this.list(
                new LambdaQueryWrapper<FloodPipeNetworkMonitor>()
                        .eq(FloodPipeNetworkMonitor::getIsReported, 1)
                        .eq(StringUtils.isNoneBlank(type), FloodPipeNetworkMonitor::getSearchType, type)
        );
        if (ones.isEmpty()) {
            ones = Collections.emptyList();
        } else {
            for (FloodPipeNetworkMonitor floodPipeNetworkMonitor : ones) {
                FloodPipeNetworkMonitorEditDto floodPipeNetworkMonitorEditDto = new FloodPipeNetworkMonitorEditDto();
                BeanUtils.copyProperties(floodPipeNetworkMonitor,floodPipeNetworkMonitorEditDto);
                floodPipeNetworkMonitorEditDto.setStationDetailsData(
                        JsonUtil.toList(floodPipeNetworkMonitor.getStationDetails(), FloodPipeNetworkMonitorEditDto.TableData.class)
                );
                list.add(floodPipeNetworkMonitorEditDto);
            }
        }
        return list;
    }
}




