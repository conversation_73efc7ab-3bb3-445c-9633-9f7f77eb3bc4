package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.waterwork.WaterworkLevelMonitoringEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.waterwork.WaterworkLevelMonitoring;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.WaterworkLevelMonitoringMapper;
import com.hjb.beijin_paishui_data_middleground.service.szls.WaterworkLevelMonitoringService;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @createDate 2025/7/30 16:41
 * @description
 */
@Service
public class WaterworkLevelMonitoringServiceImpl extends ServiceImpl<WaterworkLevelMonitoringMapper, WaterworkLevelMonitoring>
    implements WaterworkLevelMonitoringService {

    @Override
    public WaterworkLevelMonitoring getReportedData() {
        WaterworkLevelMonitoring one = this.getOne(
          new LambdaQueryWrapper<WaterworkLevelMonitoring>()
                .eq(WaterworkLevelMonitoring::getIsReported, 1)
        );
        if(Objects.isNull(one)) {
            one = new WaterworkLevelMonitoring();
        } else {
            one.setLevelMonitoringValue(JsonUtil.toObject(one.getLevelMonitoring(), WaterworkLevelMonitoringEditDto.EchartsValue.class));
        }
        return one;
    }
}
