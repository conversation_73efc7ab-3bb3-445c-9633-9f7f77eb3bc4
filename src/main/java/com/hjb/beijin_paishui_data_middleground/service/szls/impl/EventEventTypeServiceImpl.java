package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.event.EventEventTypeEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.event.EventEventType;
import com.hjb.beijin_paishui_data_middleground.service.szls.EventEventTypeService;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.EventEventTypeMapper;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【event_event_type(事件类型)】的数据库操作Service实现
* @createDate 2025-01-14 16:37:56
*/
@Service
public class EventEventTypeServiceImpl extends ServiceImpl<EventEventTypeMapper, EventEventType>
    implements EventEventTypeService{

    @Override
    public EventEventType getReportedData() {
        EventEventType one = this.getOne(
                new LambdaQueryWrapper<EventEventType>()
                        .eq(EventEventType::getIsReported, 1)
        );
        if (Objects.isNull(one)) {
            one = new EventEventType();
        } else {
            one.setEventTypeValue(JsonUtil.toList(one.getEventType(), EventEventTypeEditDto.EchartsValue.class));
        }
        return one;
    }
}




