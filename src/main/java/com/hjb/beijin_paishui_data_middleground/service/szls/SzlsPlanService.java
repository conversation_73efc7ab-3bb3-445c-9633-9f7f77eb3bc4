package com.hjb.beijin_paishui_data_middleground.service.szls;

import com.hjb.beijin_paishui_data_middleground.entity.szls.po.SzlsPlan;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;

/**
* <AUTHOR>
* @description 针对表【szls_plan(淹没方案表)】的数据库操作Service
* @createDate 2025-07-31 15:40:58
*/
public interface SzlsPlanService extends IService<SzlsPlan> {

    void processWaterDepthTask(String planId, LocalDateTime startTime, LocalDateTime endTime, int stepMinutes);
}
