package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical.SyntheticalComplaintResolution;
import com.hjb.beijin_paishui_data_middleground.service.szls.SyntheticalComplaintResolutionService;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.SyntheticalComplaintResolutionMapper;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【synthetical_complaint_resolution(接诉即办)】的数据库操作Service实现
* @createDate 2025-01-10 11:53:07
*/
@Service
public class SyntheticalComplaintResolutionServiceImpl extends ServiceImpl<SyntheticalComplaintResolutionMapper, SyntheticalComplaintResolution>
    implements SyntheticalComplaintResolutionService{

    @Override
    public SyntheticalComplaintResolution getReportedData() {
        SyntheticalComplaintResolution one = this.getOne(
                new LambdaQueryWrapper<SyntheticalComplaintResolution>()
                        .eq(SyntheticalComplaintResolution::getIsReported, 1)
        );
        if (Objects.isNull(one)) {
            one = new SyntheticalComplaintResolution();
        }
        return one;
    }
}




