package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.event.EventEventSource;
import com.hjb.beijin_paishui_data_middleground.service.szls.EventEventSourceService;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.EventEventSourceMapper;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【event_event_source(事件来源)】的数据库操作Service实现
* @createDate 2025-01-14 16:31:36
*/
@Service
public class EventEventSourceServiceImpl extends ServiceImpl<EventEventSourceMapper, EventEventSource>
    implements EventEventSourceService{

    @Override
    public EventEventSource getReportedData() {
        EventEventSource one = this.getOne(
                new LambdaQueryWrapper<EventEventSource>()
                        .eq(EventEventSource::getIsReported, 1)
        );
        if (Objects.isNull(one)) {
            one = new EventEventSource();
        }
        return one;
    }
}




