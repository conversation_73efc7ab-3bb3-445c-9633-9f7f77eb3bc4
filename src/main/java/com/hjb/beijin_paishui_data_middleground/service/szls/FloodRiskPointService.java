package com.hjb.beijin_paishui_data_middleground.service.szls;

import com.hjb.beijin_paishui_data_middleground.entity.szls.po.flood.FloodRiskPoint;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【flood_risk_point(风险点)】的数据库操作Service
* @createDate 2025-01-13 17:38:01
*/
public interface FloodRiskPointService extends IService<FloodRiskPoint> {

    FloodRiskPoint getReportedData(String searchAdministrative, String searchCompany);
}
