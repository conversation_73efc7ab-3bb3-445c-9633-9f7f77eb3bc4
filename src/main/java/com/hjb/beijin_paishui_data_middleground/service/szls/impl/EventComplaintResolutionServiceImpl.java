package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.event.EventComplaintResolution;
import com.hjb.beijin_paishui_data_middleground.service.szls.EventComplaintResolutionService;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.EventComplaintResolutionMapper;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【event_complaint_resolution(接诉即办)】的数据库操作Service实现
* @createDate 2025-01-14 16:36:38
*/
@Service
public class EventComplaintResolutionServiceImpl extends ServiceImpl<EventComplaintResolutionMapper, EventComplaintResolution>
    implements EventComplaintResolutionService{

    @Override
    public EventComplaintResolution getReportedData() {
        EventComplaintResolution one = this.getOne(
                new LambdaQueryWrapper<EventComplaintResolution>()
                        .eq(EventComplaintResolution::getIsReported, 1)
        );
        if (Objects.isNull(one)) {
            one = new EventComplaintResolution();
        }
        return one;
    }
}




