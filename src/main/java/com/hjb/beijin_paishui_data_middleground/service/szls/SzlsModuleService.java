package com.hjb.beijin_paishui_data_middleground.service.szls;

import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.SzlsModule;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hjb.beijin_paishui_data_middleground.entity.szls.vo.SzlsModuleVo;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【szls_module(模块表)】的数据库操作Service
* @createDate 2025-01-08 17:05:02
*/
public interface SzlsModuleService extends IService<SzlsModule> {

    List<SzlsModuleVo> tree();

    Boolean isReported(String moduleName, Long parentId);
}
