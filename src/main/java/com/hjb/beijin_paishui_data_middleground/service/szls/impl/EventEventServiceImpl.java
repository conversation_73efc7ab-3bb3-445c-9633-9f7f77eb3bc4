package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.event.EventEvent;
import com.hjb.beijin_paishui_data_middleground.service.szls.EventEventService;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.EventEventMapper;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【event_event(事件)】的数据库操作Service实现
* @createDate 2025-01-14 16:29:07
*/
@Service
public class EventEventServiceImpl extends ServiceImpl<EventEventMapper, EventEvent>
    implements EventEventService{

    @Override
    public EventEvent getReportedData() {
        EventEvent one = this.getOne(
                new LambdaQueryWrapper<EventEvent>()
                        .eq(EventEvent::getIsReported, 1)
        );
        if (Objects.isNull(one)) {
            one = new EventEvent();
        }
        return one;
    }
}




