package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.synthetical.SyntheticalRainfallConditionsEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical.SyntheticalRainfallConditions;
import com.hjb.beijin_paishui_data_middleground.entity.szls.vo.SyntheticalRainfallConditionsVo;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.SyntheticalRainfallConditionsMapper;
import com.hjb.beijin_paishui_data_middleground.service.szls.SyntheticalRainfallConditionsService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【synthetical_rainfall_conditions(降雨情况)】的数据库操作Service实现
 * @createDate 2025-01-10 10:40:16
 */
@Service
public class SyntheticalRainfallConditionsServiceImpl extends ServiceImpl<SyntheticalRainfallConditionsMapper, SyntheticalRainfallConditions>
        implements SyntheticalRainfallConditionsService {

    @Override
    public List<SyntheticalRainfallConditions> getReportedData(String year) {
        List<SyntheticalRainfallConditions> ones = this.list(
                new LambdaQueryWrapper<SyntheticalRainfallConditions>()
                        .eq(SyntheticalRainfallConditions::getIsReported, 1)
                        .eq(StringUtils.isNoneBlank(year), SyntheticalRainfallConditions::getSearchYear, year)
        );
        if (ones.isEmpty()) {
            ones = Collections.emptyList();
        } else {
            for (SyntheticalRainfallConditions one : ones) {
                one.setTop10RainfallStationsValue(
                        JsonUtil.toObject(one.getTop10RainfallStations(),
                                SyntheticalRainfallConditionsEditDto.EchartsValue.class)
                );
                one.setAdministrativeCumulativeRainfallValue(
                        JsonUtil.toObject(one.getAdministrativeCumulativeRainfall(),
                                SyntheticalRainfallConditionsEditDto.EchartsValue.class)
                );
                one.setOfficeRainfallAdministrativeValue(
                        JsonUtil.toObject(one.getOfficeRainfallAdministrative(),
                                SyntheticalRainfallConditionsEditDto.EchartsValue.class)
                );
                one.setRiverRainfallAdministrativeValue(
                        JsonUtil.toObject(one.getRiverRainfallAdministrative(),
                                SyntheticalRainfallConditionsEditDto.EchartsValue.class)
                );
            }
        }
        return ones;
    }

    @Override
    public SyntheticalRainfallConditionsVo getReportedDataByManage(String year) {
        SyntheticalRainfallConditionsVo vos = new SyntheticalRainfallConditionsVo();
        List<SyntheticalRainfallConditions> ones = this.list(
                new LambdaQueryWrapper<SyntheticalRainfallConditions>()
                        .eq(SyntheticalRainfallConditions::getIsReported, 1)
                        .eq(StringUtils.isNoneBlank(year), SyntheticalRainfallConditions::getSearchYear, year)
        );
        if (ones.isEmpty()) {
            return vos;
        }
        List<SyntheticalRainfallConditionsVo.BasicData> basicDataList = new ArrayList<>();
        List<SyntheticalRainfallConditionsVo.EchartsData> echartsDataList = new ArrayList<>();
        for (SyntheticalRainfallConditions one : ones) {
            SyntheticalRainfallConditionsVo.EchartsData echartsData = new SyntheticalRainfallConditionsVo.EchartsData();

            if (!StringUtils.isAllBlank(
                    one.getTop10RainfallStations(),
                    one.getAdministrativeCumulativeRainfall(),
                    one.getOfficeRainfallAdministrative(),
                    one.getAdministrativeCumulativeRainfall()
            )) {
                echartsData.setTop10RainfallStationsValue(
                        JsonUtil.toObject(one.getTop10RainfallStations(),
                                SyntheticalRainfallConditionsEditDto.EchartsValue.class)
                );
                echartsData.setAdministrativeCumulativeRainfallValue(
                        JsonUtil.toObject(one.getAdministrativeCumulativeRainfall(),
                                SyntheticalRainfallConditionsEditDto.EchartsValue.class)
                );
                echartsData.setOfficeRainfallAdministrativeValue(
                        JsonUtil.toObject(one.getOfficeRainfallAdministrative(),
                                SyntheticalRainfallConditionsEditDto.EchartsValue.class)
                );
                echartsData.setRiverRainfallAdministrativeValue(
                        JsonUtil.toObject(one.getRiverRainfallAdministrative(),
                                SyntheticalRainfallConditionsEditDto.EchartsValue.class)
                );
                echartsData.setSearchYear(one.getSearchYear());
                echartsDataList.add(echartsData);
            }
            if (!StringUtils.isAllBlank(
                    one.getRainfallAmount(),
                    one.getRainfallAmountYoy(),
                    one.getCumulativeAverageRainfall(),
                    one.getCumulativeAverageRainfallYoy())) {
                SyntheticalRainfallConditionsVo.BasicData basicData = new SyntheticalRainfallConditionsVo.BasicData();
                BeanUtils.copyProperties(one, basicData);
                basicDataList.add(basicData);
            }
        }
        vos.setBasicDataList(basicDataList);
        vos.setEchartsDataList(echartsDataList);
        return vos;
    }
}




