package com.hjb.beijin_paishui_data_middleground.service.szls;

import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.flood.FloodPipeNetworkMonitorEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.flood.FloodPipeNetworkMonitor;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【flood_pipe_network_monitor(管网监测)】的数据库操作Service
* @createDate 2025-01-13 16:47:44
*/
public interface FloodPipeNetworkMonitorService extends IService<FloodPipeNetworkMonitor> {

    List<FloodPipeNetworkMonitorEditDto> getReportedData(String type);
}
