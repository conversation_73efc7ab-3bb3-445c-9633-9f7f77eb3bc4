package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.drain.DrainFacilityBlankingEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainFacilityBlanking;
import com.hjb.beijin_paishui_data_middleground.service.szls.DrainFacilityBlankingService;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.DrainFacilityBlankingMapper;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【drain_facility_blanking(设施消隐)】的数据库操作Service实现
* @createDate 2025-01-13 11:17:55
*/
@Service
public class DrainFacilityBlankingServiceImpl extends ServiceImpl<DrainFacilityBlankingMapper, DrainFacilityBlanking>
    implements DrainFacilityBlankingService{

    @Override
    public DrainFacilityBlanking getReportedData() {
        DrainFacilityBlanking one = this.getOne(
                new LambdaQueryWrapper<DrainFacilityBlanking>()
                        .eq(DrainFacilityBlanking::getIsReported, 1)
        );
        if (Objects.isNull(one)) {
            one = new DrainFacilityBlanking();
        } else {
            one.setAnnualTrendValue(
                    JsonUtil.toObject(one.getAnnualTrend(), DrainFacilityBlankingEditDto.EchartsValue.class));
            one.setSolutionInformationValue(
                    JsonUtil.toObject(one.getSolutionInformation(), DrainFacilityBlankingEditDto.EchartsValueSolution.class)
            );
        }
        return one;
    }
}




