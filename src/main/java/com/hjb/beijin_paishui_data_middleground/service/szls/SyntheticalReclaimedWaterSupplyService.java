package com.hjb.beijin_paishui_data_middleground.service.szls;

import com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical.SyntheticalReclaimedWaterSupply;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【synthetical_reclaimed_water_supply(再生水供应)】的数据库操作Service
* @createDate 2025-01-10 10:21:56
*/
public interface SyntheticalReclaimedWaterSupplyService extends IService<SyntheticalReclaimedWaterSupply> {

    SyntheticalReclaimedWaterSupply getReportedData();
}
