package com.hjb.beijin_paishui_data_middleground.service.szls;

import com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical.SyntheticalWaterTreatmentPlate;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【synthetical_water_treatment_plate(水处理板块)】的数据库操作Service
* @createDate 2025-01-10 11:18:58
*/
public interface SyntheticalWaterTreatmentPlateService extends IService<SyntheticalWaterTreatmentPlate> {

    SyntheticalWaterTreatmentPlate getReportedData();
}
