package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.drain.DrainPeripheralConstructionEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainPeripheralConstruction;
import com.hjb.beijin_paishui_data_middleground.service.szls.DrainPeripheralConstructionService;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.DrainPeripheralConstructionMapper;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【drain_peripheral_construction(周边施工)】的数据库操作Service实现
* @createDate 2025-01-10 13:56:36
*/
@Service
public class DrainPeripheralConstructionServiceImpl extends ServiceImpl<DrainPeripheralConstructionMapper, DrainPeripheralConstruction>
    implements DrainPeripheralConstructionService{

    @Override
    public DrainPeripheralConstruction getReportedData() {
        DrainPeripheralConstruction one = this.getOne(
                new LambdaQueryWrapper<DrainPeripheralConstruction>()
                        .eq(DrainPeripheralConstruction::getIsReported, 1)
        );
        if (Objects.isNull(one)) {
            one = new DrainPeripheralConstruction();
        } else {
            one.setItemTypeProportionValue(
                    JsonUtil.toList(one.getItemTypeProportion(),
                            DrainPeripheralConstructionEditDto.EchartsValue.class));
            one.setEngineeringProgressProportionValue(
                    JsonUtil.toList(one.getEngineeringProgressProportion(),
                            DrainPeripheralConstructionEditDto.EchartsValue.class));
        }
        return one;
    }
}




