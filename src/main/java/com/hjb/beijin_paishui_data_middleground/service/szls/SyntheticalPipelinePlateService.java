package com.hjb.beijin_paishui_data_middleground.service.szls;

import com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical.SyntheticalPipelinePlate;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【synthetical_pipeline_plate(管网板块)】的数据库操作Service
* @createDate 2025-01-10 11:08:26
*/
public interface SyntheticalPipelinePlateService extends IService<SyntheticalPipelinePlate> {

    SyntheticalPipelinePlate getReportedData();
}
