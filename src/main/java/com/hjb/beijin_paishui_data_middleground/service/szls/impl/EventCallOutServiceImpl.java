package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.event.EventCallOut;
import com.hjb.beijin_paishui_data_middleground.service.szls.EventCallOutService;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.EventCallOutMapper;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【event_call_out(呼叫)】的数据库操作Service实现
* @createDate 2025-01-14 16:34:52
*/
@Service
public class EventCallOutServiceImpl extends ServiceImpl<EventCallOutMapper, EventCallOut>
    implements EventCallOutService{

    @Override
    public EventCallOut getReportedData() {
        EventCallOut one = this.getOne(
                new LambdaQueryWrapper<EventCallOut>()
                        .eq(EventCallOut::getIsReported, 1)
        );
        if (Objects.isNull(one)) {
            one = new EventCallOut();
        }
        return one;
    }
}




