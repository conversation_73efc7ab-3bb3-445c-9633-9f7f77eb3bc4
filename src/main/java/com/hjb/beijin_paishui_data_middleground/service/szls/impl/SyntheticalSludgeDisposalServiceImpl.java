package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.synthetical.SyntheticalSewageTreatmentEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical.SyntheticalSewageTreatment;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical.SyntheticalSludgeDisposal;
import com.hjb.beijin_paishui_data_middleground.service.szls.SyntheticalSludgeDisposalService;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.SyntheticalSludgeDisposalMapper;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【synthetical_sludge_disposal(污泥处置)】的数据库操作Service实现
* @createDate 2025-01-10 10:36:45
*/
@Service
public class SyntheticalSludgeDisposalServiceImpl extends ServiceImpl<SyntheticalSludgeDisposalMapper, SyntheticalSludgeDisposal>
    implements SyntheticalSludgeDisposalService{

    @Override
    public SyntheticalSludgeDisposal getReportedData() {
        SyntheticalSludgeDisposal one = this.getOne(
                new LambdaQueryWrapper<SyntheticalSludgeDisposal>()
                        .eq(SyntheticalSludgeDisposal::getIsReported, 1)
        );
        if (Objects.isNull(one)) {
            one = new SyntheticalSludgeDisposal();
        }
        return one;
    }
}




