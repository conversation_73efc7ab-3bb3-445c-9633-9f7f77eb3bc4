package com.hjb.beijin_paishui_data_middleground.service.szls;

import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainHybridConnection;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【drain_hybrid_connection(混接)】的数据库操作Service
* @createDate 2025-01-10 13:44:25
*/
public interface DrainHybridConnectionService extends IService<DrainHybridConnection> {

    DrainHybridConnection getReportedData();

}
