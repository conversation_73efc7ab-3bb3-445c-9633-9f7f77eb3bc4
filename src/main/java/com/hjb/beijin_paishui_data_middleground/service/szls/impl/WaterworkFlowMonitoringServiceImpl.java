package com.hjb.beijin_paishui_data_middleground.service.szls.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.waterwork.WaterworkFlowMonitoringEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.waterwork.WaterworkFlowMonitoring;
import com.hjb.beijin_paishui_data_middleground.mapper.szls.WaterworkFlorMonitoringMapper;
import com.hjb.beijin_paishui_data_middleground.service.szls.WaterworkFlowMonitoringService;
import org.springframework.stereotype.Service;

import java.util.Objects;


@Service
public class WaterworkFlowMonitoringServiceImpl extends ServiceImpl<WaterworkFlorMonitoringMapper, WaterworkFlowMonitoring>
    implements WaterworkFlowMonitoringService {

    @Override
    public WaterworkFlowMonitoring getReportedData() {
        WaterworkFlowMonitoring one = this.getOne(
                new LambdaQueryWrapper<WaterworkFlowMonitoring>()
                    .eq(WaterworkFlowMonitoring::getIsReported, 1)
        );
        if(Objects.isNull(one)) {
            one = new WaterworkFlowMonitoring();
        } else {
            one.setFlowMonitoringValue(JsonUtil.toObject(one.getFlowMonitoring(), WaterworkFlowMonitoringEditDto.EchartsValue.class));
        }
        return one;
    }
}
