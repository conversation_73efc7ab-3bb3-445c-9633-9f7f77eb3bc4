package com.hjb.beijin_paishui_data_middleground.service.szls;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.waterwork.WaterworkTreatedWater;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【waterwork_treated_water(处理水量)】的数据库操作Service
 * @createDate 2025/7/30 17:49
 */
public interface WaterworkTreatedWaterService extends IService<WaterworkTreatedWater> {

    WaterworkTreatedWater getReportedData();
}
