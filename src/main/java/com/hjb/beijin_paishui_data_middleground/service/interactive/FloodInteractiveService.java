package com.hjb.beijin_paishui_data_middleground.service.interactive;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.CarAndUnitVo;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.CarPointVo;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.PopUpNotificationVo;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.PumpStationInteractiveVo;
import com.hjb.beijin_paishui_data_middleground.entity.sub.vo.RainGaugeInformationVo;
import com.hjb.beijin_paishui_data_middleground.entity.sub.vo.StationMonitoringVo;

import java.util.List;

/**
 * @Description: 交互防汛调度Service
 * @Author: lvhongen
 * @Date: 2025-05-12 10:20
 * @Version： 1.0
 **/
public interface FloodInteractiveService {
    /**
     * 获取实时雨量
     * @return
     */
    ResponseData<List<RainGaugeInformationVo>> getRealTimeRainfall ();

    /**
     * 获取车辆实时位置信息
     * @return
     */
    ResponseData<List<CarPointVo>> getCarInfo ();

    /**
     * 通过车牌号获取车辆详细信息
     * @return
     */
    ResponseData<PopUpNotificationVo> getCarInfoByCarNo (String carNo);

    /**
     * 获得雨水管道液位流量计流量
     * @return
     */
    ResponseData<List<StationMonitoringVo>> getPipelineLiquidLevel (String type);

    /**
     * 获取水厂监测数据（排河口流量）
     * @return
     */
    ResponseData getWaterPlant ();

    /**
     * 获取雨水泵站POI信息
     * @return
     */
    ResponseData<List<PumpStationInteractiveVo>> getRainwaterPumpingStation (String type);
}
