package com.hjb.beijin_paishui_data_middleground.service.interactive.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONNull;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hjb.beijin_paishui_data_middleground.controller.interactive.WaterWorksInteractiveController;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.PopUpNotificationVo;
import com.hjb.beijin_paishui_data_middleground.service.interactive.WaterWorksInteractiveService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class WaterWorksInteractiveServiceImpl implements WaterWorksInteractiveService {

    public static final String WATER_URL = "http://10.2.110.37:18086/api/query_rt";

    @Override
    public PopUpNotificationVo buildIndicatorSection(String title, List<WaterWorksInteractiveController.IndicatorInfo> indicatorList) {
        // 请求接口
        String bodyJson = JSONUtil.toJsonStr(indicatorList.stream()
                .map(WaterWorksInteractiveController.IndicatorInfo::getId)
                .collect(Collectors.toList()));

        HttpResponse response = HttpRequest.post(WATER_URL)
                .body(bodyJson)
                .execute();

        // 打印响应体
        String responseBody = response.body();
        System.out.println("接口响应内容：" + responseBody);

        JSONObject respJson = JSONUtil.parseObj(responseBody);
        JSONObject data = respJson.getJSONObject("data");

        // 构造 KeyValue 列表
        List<PopUpNotificationVo.KeyValue> kvList = indicatorList.stream()
                .map(info -> {
                    Object val = data.get(info.getId());
                    String valueStr;
                    if (val == null || val.equals("null") || val instanceof JSONNull) {
                        valueStr = null;
                    } else if (val instanceof Number || val instanceof String) {
                        valueStr = val + info.getUnit();
                    } else if (val instanceof JSONObject) {
                        // 取对象中的 "run" 字段的值
                        JSONObject valObj = (JSONObject) val;
                        Object runVal = valObj.get("run");
                        valueStr = (runVal == null) ? null : runVal.toString();
                    } else {
                        valueStr = JSONUtil.toJsonStr(val);
                    }

                    return new PopUpNotificationVo.KeyValue(info.getLabel(), valueStr);
                })
                .collect(Collectors.toList());


        PopUpNotificationVo.Section section = new PopUpNotificationVo.Section("基本信息", kvList);
        PopUpNotificationVo result = new PopUpNotificationVo();
        result.setTitle(title);
        result.setData(Collections.singletonList(section));
        return result;
    }



    @Override
    public WaterWorksInteractiveController.PopUpNotificationVo2 getPreDehydrationArea(String title) {
        List<WaterWorksInteractiveController.IndicatorInfo> list = Arrays.asList(
                new WaterWorksInteractiveController.IndicatorInfo("HF3270320230720", "1#浓缩机运行"),
                new WaterWorksInteractiveController.IndicatorInfo("HF3269120230720", "1#进泥流量", "m³/h"),
                new WaterWorksInteractiveController.IndicatorInfo("HF3272920230720", "2#浓缩机运行"),
                new WaterWorksInteractiveController.IndicatorInfo("HF3271720230720", "2#进泥流量", "m³/h"),
                new WaterWorksInteractiveController.IndicatorInfo("HF3275520230720", "3#浓缩机运行"),
                new WaterWorksInteractiveController.IndicatorInfo("HF3274320230720", "3#进泥流量", "m³/h"),
                new WaterWorksInteractiveController.IndicatorInfo("HF3278120230720", "4#浓缩机运行"),
                new WaterWorksInteractiveController.IndicatorInfo("HF3276920230720", "4#进泥流量", "m³/h"),
                new WaterWorksInteractiveController.IndicatorInfo("HF3102020230720", "1#脱水机运行"),
                new WaterWorksInteractiveController.IndicatorInfo("HF3098920230720", "1#进泥流量", "m³/h"),
                new WaterWorksInteractiveController.IndicatorInfo("HF3108320230720", "2#脱水机运行"),
                new WaterWorksInteractiveController.IndicatorInfo("HF3105220230720", "2#进泥流量", "m³/h"),
                new WaterWorksInteractiveController.IndicatorInfo("HF3114620230720", "3#脱水机运行"),
                new WaterWorksInteractiveController.IndicatorInfo("HF3111520230720", "3#进泥流量", "m³/h"),
                new WaterWorksInteractiveController.IndicatorInfo("HF3120920230720", "4#脱水机运行"),
                new WaterWorksInteractiveController.IndicatorInfo("HF3117820230720", "4#进泥流量", "m³/h")
        );

        // 构建请求参数
        String bodyJson = JSONUtil.toJsonStr(
                list.stream().map(WaterWorksInteractiveController.IndicatorInfo::getId).collect(Collectors.toList())
        );

        // 请求接口
        // 请求接口
        HttpResponse response = HttpRequest.post(WATER_URL)
                .body(bodyJson)
                .execute();

        // 打印响应体
        String responseBody = response.body();
        System.out.println("接口响应内容：" + responseBody);
        JSONObject respJson = JSONUtil.parseObj(response.body());
        JSONObject data = respJson.getJSONObject("data");

        List<WaterWorksInteractiveController.PopUpNotificationVo2.KeyValue> keyValueList = new ArrayList<>();

        for (int i = 0; i < list.size(); i += 2) {
            WaterWorksInteractiveController.IndicatorInfo runInfo = list.get(i);
            WaterWorksInteractiveController.IndicatorInfo flowInfo = list.get(i + 1);

            // 默认 title = label + 状态（运行/停止）
            String stateTitle = runInfo.getLabel();  // 基础标题
            Object runObj = data.get(runInfo.getId());
            if (runObj instanceof JSONObject) {
                Integer run = ((JSONObject) runObj).getInt("run");
                if (run != null && run == 1) {
                    stateTitle += "运行";
                } else {
                    stateTitle += "停止";
                }
            } else {
                stateTitle += "状态未知";
            }

            // 流量值处理
            String flowValue = "--";
            Object flowObj = data.get(flowInfo.getId());
            if (flowObj instanceof Number) {
                flowValue = String.format("%.2f%s", ((Number) flowObj).doubleValue(), flowInfo.getUnit());
            }

            keyValueList.add(new WaterWorksInteractiveController.PopUpNotificationVo2.KeyValue(
                    stateTitle, flowInfo.getLabel(), flowValue
            ));
        }


        WaterWorksInteractiveController.PopUpNotificationVo2.Section section =
                new WaterWorksInteractiveController.PopUpNotificationVo2.Section(title, keyValueList);

        WaterWorksInteractiveController.PopUpNotificationVo2 vo = new WaterWorksInteractiveController.PopUpNotificationVo2();
        vo.setTitle(title);
        vo.setData(Collections.singletonList(section));
        return vo;
    }
}
