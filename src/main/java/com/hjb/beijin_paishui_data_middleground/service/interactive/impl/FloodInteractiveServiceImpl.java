package com.hjb.beijin_paishui_data_middleground.service.interactive.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.*;
import com.hjb.beijin_paishui_data_middleground.entity.json.*;
import com.hjb.beijin_paishui_data_middleground.entity.sub.vo.PumpStationVo;
import com.hjb.beijin_paishui_data_middleground.entity.sub.vo.RainGaugeInformationVo;
import com.hjb.beijin_paishui_data_middleground.entity.sub.vo.StationInformationVo;
import com.hjb.beijin_paishui_data_middleground.entity.sub.vo.StationMonitoringVo;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.flood.StationTypeData;
import com.hjb.beijin_paishui_data_middleground.entity.szls.vo.WaterPlantResponseData;
import com.hjb.beijin_paishui_data_middleground.mapper.sub.ShareRceCarInfoMapper;
import com.hjb.beijin_paishui_data_middleground.service.interactive.FloodInteractiveService;
import com.hjb.beijin_paishui_data_middleground.service.sub.StationTypeDataService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Type;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 交互防汛调度Service
 * @Author: lvhongen
 * @Date: 2025-05-12 10:20
 * @Version： 1.0
 **/
@Slf4j
@Service
public class FloodInteractiveServiceImpl implements FloodInteractiveService {
    @Resource
    private ShareRceCarInfoMapper shareRceCarInfoMapper;
    @Resource
    private StationTypeDataService stationTypeDataService;

    @Override
    public ResponseData<List<RainGaugeInformationVo>> getRealTimeRainfall () {
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> entity = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.getForEntity(
                "http://*********:23486/deviot-open-api/api/external/" +
                        "findNewsStationListByStationType?token=75693db267804dbdb7127e06064821ce&stationType=9",
                String.class, entity);
        if (response.getStatusCode().is2xxSuccessful()) {
            String jsonResponse = response.getBody();
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                // 这里假设 JsonStationMonitoringVoResponse 是正确的响应类，根据实际情况修改
                JsonRainGaugeInformationResponse jsonStationMonitoringVoResponse =
                        objectMapper.readValue(jsonResponse, JsonRainGaugeInformationResponse.class);
                // 这里假设 StationMonitoringVo 是正确的实体类，根据实际情况修改
                List<RainGaugeInformationVo> detail = jsonStationMonitoringVoResponse.getDetail();
                // 获取当前日期
                LocalDate currentDate = LocalDate.now();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                // 筛选当前月日的数据
                List<RainGaugeInformationVo> filteredList = new ArrayList<>();
                if (detail.size() > 0) {
                    for (RainGaugeInformationVo vo : detail) {
                        LocalDate voDate = LocalDate.parse(vo.getCollectTime().substring(0, 10), formatter);
                        if (voDate.getMonth() == currentDate.getMonth() && voDate.getDayOfMonth() == currentDate.getDayOfMonth()) {
                            filteredList.add(vo);
                        }
                    }
                }
                log.info(detail.toString());
                return ResponseData.ok(filteredList);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    @Override
    public ResponseData<List<CarPointVo>> getCarInfo () {
        List<CarPointVo> carPointVoList = new ArrayList<>();
        List<CarAndUnitVo> vehicleCallSignInformation = shareRceCarInfoMapper.getVehicleCallSignInformation(null);
        if (vehicleCallSignInformation.size() == 0)
            return ResponseData.ok(carPointVoList);
        StringJoiner joiner = new StringJoiner(",");
        for (CarAndUnitVo vo : vehicleCallSignInformation) {
            String callSign = vo.getCallNo();
            if (callSign.length() == 4) {
                char secondChar = callSign.charAt(1);
                if ('1' == secondChar || '2' == secondChar || '3' == secondChar) {
                    String carNo = vo.getCarNo();
                    if (carNo != null) {
                        joiner.add(carNo);
                    }
                }
            }
        }
        String carNoList = joiner.toString();
        log.info(joiner.toString());
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> entity = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.getForEntity(
                "http://*********/StandardApiAction_vehicleStatus.action?" +
                        "jsession=d4e4be777deb470286efaf7bf5ee3333&vehiIdno=+" + carNoList + "&toMap=2&geoaddress=0",
                String.class, entity);
        if (response.getStatusCode().is2xxSuccessful()) {
            String jsonResponse = response.getBody();
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                // 这里假设 JsonStationMonitoringVoResponse 是正确的响应类，根据实际情况修改
                JsonStandardApiActionResponse jsonStandardApiActionResponse =
                        objectMapper.readValue(jsonResponse, JsonStandardApiActionResponse.class);
                // 这里假设 StationMonitoringVo 是正确的实体类，根据实际情况修改
                List<CarPointVo> detail = jsonStandardApiActionResponse.getInfos();
                if (detail.size() > 0) {
                    for (CarPointVo carPointVo : detail) {
                        for (CarAndUnitVo vo : vehicleCallSignInformation) {
                            if (vo.getCarNo() != null && vo.getCarNo().equals(carPointVo.getCarNo())) {
                                String callSign = vo.getCallNo();
                                if (callSign.length() == 4) {
                                    char secondChar = callSign.charAt(1);
                                    if ('1' == secondChar)
                                        carPointVo.setTypeName("大型单元");
                                    if ('2' == secondChar)
                                        carPointVo.setTypeName("中型单元");
                                    if ('3' == secondChar)
                                        carPointVo.setTypeName("小型单元");
                                }
                                break;
                            }
                        }
                    }
                }
                log.info(ResponseData.ok(detail).toString());
                Gson gson = new Gson();
                try (BufferedWriter writer = new BufferedWriter(new FileWriter("1.txt"))) {
                    writer.write(gson.toJson(ResponseData.ok(detail)));
                    log.info("内容已成功写入文件：" + "1.txt");
                } catch (IOException e) {
                    log.error("写入文件时出错：" + "1.txt", e);
                }
                return ResponseData.ok(detail);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    @Override
    public ResponseData<PopUpNotificationVo> getCarInfoByCarNo (String carNo) {
        PopUpNotificationVo popUpNotificationVo = new PopUpNotificationVo();
        log.info("车牌号为" + carNo);
        List<CarAndUnitVo> vehicleCallSignInformation = shareRceCarInfoMapper.getVehicleCallSignInformation(carNo);
        if (vehicleCallSignInformation.size() == 0)
            return ResponseData.ok(null);
        CarAndUnitVo carAndUnitVo = vehicleCallSignInformation.get(0);
        String callSign = carAndUnitVo.getCallNo();
        if (callSign.length() == 4) {
            char secondChar = callSign.charAt(1);
            if ('1' == secondChar)
                carAndUnitVo.setTypeName("大型单元");
            if ('2' == secondChar)
                carAndUnitVo.setTypeName("中型单元");
            if ('3' == secondChar)
                carAndUnitVo.setCarNo("小型单元");
        }
        List<PopUpNotificationVo.Section> sectionList = new ArrayList<>();
        PopUpNotificationVo.Section section = new PopUpNotificationVo.Section();
        section.setTitle("基本信息");
        List<PopUpNotificationVo.KeyValue> keyValueList = new ArrayList<>();
        keyValueList.add(new PopUpNotificationVo.KeyValue("车牌号",carAndUnitVo.getCarNo()));
        keyValueList.add(new PopUpNotificationVo.KeyValue("首要联系人",null));
        keyValueList.add(new PopUpNotificationVo.KeyValue("车型",carAndUnitVo.getCarType()));
        keyValueList.add(new PopUpNotificationVo.KeyValue("能力(m/h)",null));
        keyValueList.add(new PopUpNotificationVo.KeyValue("首要联系人电话",carAndUnitVo.getCarNo()));
        keyValueList.add(new PopUpNotificationVo.KeyValue("抢险单元类型",carAndUnitVo.getTypeName()));
        keyValueList.add(new PopUpNotificationVo.KeyValue("所属分公司",carAndUnitVo.getComnm()));
        section.setData(keyValueList);
        sectionList.add(section);
        popUpNotificationVo.setData(sectionList);
        log.info(vehicleCallSignInformation.toString());
        return ResponseData.ok(popUpNotificationVo);
    }

    private final RestTemplate restTemplate = new RestTemplate();

    @Override
    public ResponseData<List<StationMonitoringVo>> getPipelineLiquidLevel (String type) {
        List<StationTypeData> stationTypeDataList = stationTypeDataService.list();
        List<StationMonitoringVo> newStationList = new ArrayList<>();
        Map<String, List<Integer>> typeMap = new HashMap<>();
        List<Integer> wsTypeList = new ArrayList<>();
        wsTypeList.add(18);
        wsTypeList.add(21);
        typeMap.put("wsgd", wsTypeList);
        List<Integer> ysTypeList = new ArrayList<>();
        ysTypeList.add(19);
        ysTypeList.add(82);
        ysTypeList.add(87);
        typeMap.put("ysgd", ysTypeList);
        List<Integer> agywTypeList = new ArrayList<>();
        agywTypeList.add(87);
        typeMap.put("agyw", agywTypeList);
        List<Integer> jsdjcList = new ArrayList<>();
        jsdjcList.add(93);
        typeMap.put("jsdjc", jsdjcList);
        List<Integer> integerList = typeMap.get(type);
        if (integerList != null && integerList.size() > 0) {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> entity = new HttpEntity<>(headers);
            // 批量处理网络请求
            List<ResponseEntity<String>> responses = integerList.stream()
                    .map(stationType -> restTemplate.getForEntity(
                            "http://*********:23486/deviot-open-api/api/external/" +
                                    "findNewsStationListByStationType?token=75693db267804dbdb7127e06064821ce&stationType=" + stationType,
                            String.class, entity))
                    .collect(Collectors.toList());
            for (ResponseEntity<String> response : responses) {
                if (response.getStatusCode().is2xxSuccessful()) {
                    String jsonResponse = response.getBody();
                    try {
                        ObjectMapper objectMapper = new ObjectMapper();
                        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                        JsonStationMonitoringVoResponse jsonStationMonitoringVoResponse =
                                objectMapper.readValue(jsonResponse, JsonStationMonitoringVoResponse.class);
                        List<StationMonitoringVo> stationInfoList = jsonStationMonitoringVoResponse.getDetail();
                        Map<String, StationTypeData> stationTypeDataMap = stationTypeDataList.stream()
                                .collect(Collectors.toMap(StationTypeData::getCode, s -> s));
                        for (StationMonitoringVo stationInformationVo : stationInfoList) {
                            if (stationInformationVo.getStationName().contains("(废)"))
                                continue;
                            boolean is = "cqyl".equals(type) ^ stationTypeDataMap.containsKey(stationInformationVo.getStationCode());
                            if (!is) {
                                newStationList.add(stationInformationVo);
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                } else {
                    System.out.println("请求失败，状态码: " + response.getStatusCode());
                }
            }
        }
        return ResponseData.ok(newStationList);
    }

    @Override
    public ResponseData getWaterPlant () {
        Map<String, String> map = new HashMap<>();
        map.put("CZWS0622", "清河40");
        String qh40 = "QH0000120230525,QH0000220230525,QH0000320230525,QH0000420230525," +
                "QH0000520230525,QH0000620230525";
        map.put("CZZS0621", "清河15");
        String qh15 = "QH0000820230525,QH0000920230525,QH0001020230525,QH0001120230525,QH0001220230525";
        map.put("CZZS0625", "清河二");
        String qh2 = "QHE0000120230601,QHE0000220230601,QHE0000320230601,QHE0000420230601,QHE0000520230601," +
                "QHE0000620230601,QHE0000720230601,QHE0000820230601";
        map.put("CZZS0632", "北小河");
        String bxh = "BXH0000120230530,BXH0000220230530,BXH0000320230530,BXH0000420230530," +
                "BXH0000520230530,BXH0000620230530";
        map.put("CZWS0642", "酒仙桥");
        String jxq = "JXQ0000120230530,JXQ0000220230530,JXQ0000320230530,JXQ0000420230530," +
                "JXQ0000520230530,JXQ0000620230530";
        map.put("CZWS4106", "高安屯");
        String gat = "GAT0000120230530,GAT0000220230530,GAT0000320230530,GAT0000420230530," +
                "GAT0000520230530,GAT0000620230530";
        map.put("CZWS0801", "高碑店");
        String gbd = "GBD0000120230522,GBD0000220230522,GBD0000320230522,GBD0000420230522,GBD0000520230522," +
                "GBD0000620230522";
        map.put("CZWS4105", "定福庄");
        String dfz = "DFZ0000120230525,DFZ0000220230525,DFZ0000320230525," +
                "DFZ0000420230525,DFZ0000520230525,DFZ0000620230525,DFZ0000720230525,DFZ0000820230525";
        map.put("CZWS1025", "小红门");
        String xhm = "XHM0000120230601,XHM0000220230601,XHM0000320230601,XHM0000420230601";
        map.put("CZWS4107", "槐房");
        String gf = "HF0000120230525,HF0000220230525,HF0000320230525,HF0000420230525," +
                "HF0000520230525,HF0000620230525,HF0000720230525,HE0000820230525,HF0000920230525,HF0001020230525";
        map.put("CZWS1042", "吴家村");
        String wjc = "WJC0000120230525,WJC0000220230525,WJC0000320230525,WJC0000420230525,WJC0000520230525";
        map.put("CZWS3800", "卢沟桥");
        String lgq = "LGQ0002920221020,LGQ0003020221020,LGQ0003120221020,LGQ0003220221020";
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new org.springframework.http.HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        // 假设这些是请求体中的数据，构建成List
        List<String> requestBodyList = new ArrayList<>(Arrays.asList(
                "QH0829220220919", "QH0827220220919", "QH0000120221101",
                "QH0001020220919", "QH0002420220919", "QH0020120220919",
                "QHE0000520220823", "QHE0008020220823", "QHE0000920230601",
                "BXH0000120220606", "BXH0002220220606", "JXQ0007620220515",
                "BXH0000120220606", "BXH0002220220606", "JXQ0007620220515",
                "JXQ0007420220515", "TXQ0009120220515", "GAT0000320220517",
                "GAT0006120220517", "GAT0000720230530", "GBD0116620220506",
                "GBD1918220220608", "GBD0001120230531", "DFZ0046420220525",
                "DFZ0000220220728", "XH1503620230313", "XH1507520230313",
                "XHM0000520230601", "HF0008820230313", "H0013420230313",
                "WJC0002320221013", "WJC0158520221013", "LGQ0054020221020",
                "LGQ0002420221020"
        ));
        List<String> combinedList = Arrays.asList(
                        qh40.split(","),
                        qh15.split(","),
                        qh2.split(","),
                        bxh.split(","),
                        jxq.split(","),
                        gat.split(","),
                        gbd.split(","),
                        dfz.split(","),
                        xhm.split(","),
                        gf.split(","),
                        wjc.split(","),
                        lgq.split(",")
                ).stream()
                .flatMap(Arrays::stream)
                .collect(Collectors.toList());
        requestBodyList.addAll(combinedList);
        log.info(requestBodyList.toString());
        ObjectMapper objectMapper = new ObjectMapper();
        String requestBodyJson = "";
        try {
            requestBodyJson = objectMapper.writeValueAsString(requestBodyList);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        HttpEntity<String> entity = new HttpEntity<>(requestBodyJson, headers);
        String url = "http://10.2.110.37:18086/api/query_rt";
        String jsonResponse = restTemplate.postForObject(url, entity, String.class);
        log.info("请求成功，返回水厂数据为" + jsonResponse);
        Gson gson = new Gson();
        Type type = new TypeToken<WaterPlantResponseData>() {
        }.getType();
        WaterPlantResponseData response = gson.fromJson(jsonResponse, type);
        log.info(response.getData().toString());
        List<FloodWaterPlantDetailDataVo> list = new ArrayList<>();
        // 定义水厂信息映射表，根据你的数据结构填充
        Map<String, Map<String, Object>> waterPlantMap = new HashMap<>();
        Map<String, Object> map1 = new HashMap<>();
        map1.put("FrontLevel", "QH0827220220919");
        map1.put("InstantaneousLift", "QH0829220220919");
        map1.put("WhetherToCross", "QH0000120221101");
        map1.put("OverflowLevel", "5.80");
        map1.put("status", Arrays.asList(qh40.split(",")));
        waterPlantMap.put("清河40", map1);
        // 将后面的都改为相同的map类型
        Map<String, Object> map2 = new HashMap<>();
        map2.put("FrontLevel", "QH0001020220919");
        map2.put("InstantaneousLift", "QH0002420220919");
        map2.put("WhetherToCross", "QH0020120220919");
        map2.put("OverflowLevel", "5.80");
        map2.put("status", Arrays.asList(qh15.split(",")));
        waterPlantMap.put("清河15", map2);
        Map<String, Object> map3 = new HashMap<>();
        map3.put("FrontLevel", "QHE0000520220823");
        map3.put("InstantaneousLift", "QHE0008020220823");
        map3.put("WhetherToCross", "QHE0000920230601");
        map3.put("OverflowLevel", "8.00");
        map3.put("status", Arrays.asList(qh2.split(",")));
        waterPlantMap.put("清河二", map3);
        Map<String, Object> map4 = new HashMap<>();
        map4.put("FrontLevel", "BXH0000120220606");
        map4.put("InstantaneousLift", "BXH0002220220606");
        map4.put("WhetherToCross", "无跨越");
        map4.put("OverflowLevel", "3.70");
        map4.put("status", Arrays.asList(bxh.split(",")));
        waterPlantMap.put("北小河", map4);
        Map<String, Object> map5 = new HashMap<>();
        map5.put("FrontLevel", "JXQ0007620220515");
        map5.put("InstantaneousLift", "JXQ0007420220515");
        map5.put("WhetherToCross", "TXQ0009120220515");
        map5.put("OverflowLevel", "5.30");
        map5.put("status", Arrays.asList(jxq.split(",")));
        waterPlantMap.put("酒仙桥", map5);
        Map<String, Object> map6 = new HashMap<>();
        map6.put("FrontLevel", "GAT0000320220517");
        map6.put("InstantaneousLift", "GAT0006120220517");
        map6.put("WhetherToCross", "GAT0000720230530");
        map6.put("OverflowLevel", "9.40");
        map6.put("status", Arrays.asList(gat.split(",")));
        waterPlantMap.put("高安屯", map6);
        Map<String, Object> map7 = new HashMap<>();
        map7.put("FrontLevel", "GBD0116620220506");
        map7.put("InstantaneousLift", "GBD1918220220608");
        map7.put("WhetherToCross", "GBD0001120230531");
        map7.put("OverflowLevel", "3.20");
        map7.put("status", Arrays.asList(gbd.split(",")));
        waterPlantMap.put("高碑店", map7);
        Map<String, Object> map8 = new HashMap<>();
        map8.put("FrontLevel", "DFZ0046420220525");
        map8.put("InstantaneousLift", "DFZ0000220220728");
        map8.put("WhetherToCross", "无跨越");
        map8.put("OverflowLevel", "7.10");
        map8.put("status", Arrays.asList(dfz.split(",")));
        waterPlantMap.put("定福庄", map8);
        Map<String, Object> map9 = new HashMap<>();
        map9.put("FrontLevel", "XH1503620230313");
        map9.put("InstantaneousLift", "XH1507520230313");
        map9.put("WhetherToCross", "XHM0000520230601");
        map9.put("OverflowLevel", "5.10");
        map9.put("status", Arrays.asList(xhm.split(",")));
        waterPlantMap.put("小红门", map9);
        Map<String, Object> map10 = new HashMap<>();
        map10.put("FrontLevel", "HF0008820230313");
        map10.put("InstantaneousLift", "H0013420230313");
        map10.put("WhetherToCross", "无跨越");
        map10.put("OverflowLevel", "10.60");
        map10.put("status", Arrays.asList(gf.split(",")));
        waterPlantMap.put("槐房", map10);
        Map<String, Object> map11 = new HashMap<>();
        map11.put("FrontLevel", "WJC00023202211013");
        map11.put("InstantaneousLift", "WJC01585202211013");
        map11.put("WhetherToCross", "无跨越");
        map11.put("OverflowLevel", "5.62");
        map11.put("status", Arrays.asList(wjc.split(",")));
        waterPlantMap.put("吴家村", map11);
        Map<String, Object> map12 = new HashMap<>();
        map12.put("FrontLevel", "LGQ00540202211020");
        map12.put("InstantaneousLift", "LGQ00024202211020");
        map12.put("WhetherToCross", "无监测");
        map12.put("OverflowLevel", "11.00");
        map12.put("status", Arrays.asList(lgq.split(",")));
        // 遍历响应数据，填充到对应的水厂信息中
        for (Map.Entry<String, Map<String, Object>> stringMapEntry : waterPlantMap.entrySet()) {
            FloodWaterPlantDetailDataVo detailData = new FloodWaterPlantDetailDataVo();
            detailData.setWaterPlantName(stringMapEntry.getKey());
            Map<String, Object> stringMapEntryValue = stringMapEntry.getValue();
            if (Objects.nonNull(stringMapEntryValue)) {
                detailData.setOverflowLevel((String) stringMapEntryValue.get("OverflowLevel"));
                log.info(stringMapEntryValue.toString());
                if (response != null && response.getData() != null) {
                    for (Map.Entry<String, Object> entry : response.getData().entrySet()) {
                        String key = entry.getKey();
                        Object value = entry.getValue();
                        if (Objects.isNull(value)) {
                            continue;
                        }
                        String valueStr = null;
                        if (value instanceof Double) {
                            valueStr = String.valueOf((Double) value);
                            if (stringMapEntryValue.get("FrontLevel").equals(key))
                                detailData.setFrontLevel(valueStr);
                            if (stringMapEntryValue.get("InstantaneousLift").equals(key))
                                detailData.setInstantaneousLift(valueStr);
                            if (stringMapEntryValue.get("WhetherToCross").equals(key))
                                detailData.setWhetherToCross(valueStr);
                            else
                                detailData.setWhetherToCross((String) stringMapEntryValue.get("WhetherToCross"));
                        } else if (value instanceof String) {
                            valueStr = (String) value;
                            log.info("值是字符串，转换后: " + valueStr);
                            if (stringMapEntryValue.get("FrontLevel").equals(key))
                                detailData.setFrontLevel(valueStr);
                            if (stringMapEntryValue.get("InstantaneousLift").equals(key))
                                detailData.setInstantaneousLift(valueStr);
                            if (stringMapEntryValue.get("WhetherToCross").equals(key))
                                detailData.setWhetherToCross(valueStr);
                            else
                                detailData.setWhetherToCross((String) stringMapEntryValue.get("WhetherToCross"));
                        } else if (value instanceof Map) {
                            log.info("进行状态判断开始-------------------");
                            Map<String, Double> valueMap = (Map<String, Double>) value;
                            Double run = valueMap.get("run");
                            System.out.println(run);
                            log.info("状态接收数据状态为=========" + run);
                            List<String> stringList = (List<String>) stringMapEntryValue.get("status");
                            log.info("状态编号为=========" + stringList.toString());
                            for (String status : stringList) {
                                System.out.println("我的staus为" + status + "---------当前获取参数值为" + key);
                                if (status.equals(key)) {
                                    log.info("进来了");
                                    if (run == 0) {
                                        if (detailData.getStatus() == null) {
                                            detailData.setStatus("0");
                                        } else {
                                            // 去掉前面的逗号
                                            String statusStr = detailData.getStatus().startsWith(",") ? detailData.getStatus().substring(1) : detailData.getStatus();
                                            detailData.setStatus(statusStr + ",0");
                                        }
                                    }
                                    if (run == 1) {
                                        if (detailData.getStatus() == null) {
                                            detailData.setStatus("1");
                                        } else {
                                            // 去掉前面的逗号
                                            String statusStr = detailData.getStatus().startsWith(",") ? detailData.getStatus().substring(1) : detailData.getStatus();
                                            detailData.setStatus(statusStr + ",1");
                                        }
                                    }
                                }
                            }
                        } else {
                            log.info("值为空");
                        }
                        log.info("键: {}, 值的类型: {}, 值: {}", key, value.getClass().getName(), value);
                    }
                } else {
                    log.error("响应数据或数据部分为 null");
                }
            }
            log.info(detailData.toString());
            list.add(detailData);
        }
        log.info("最终得到的list为" + list.toString());
        return ResponseData.ok(list);
    }

    @Override
    public ResponseData<List<PumpStationInteractiveVo>> getRainwaterPumpingStation (String type) {
        List<PumpStationInteractiveVo> pumpStationInteractiveVoList = new ArrayList<>();
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> entity = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.getForEntity(
                "http://*********:23486/deviot-open-api/api/external/" +
                        "findStationListByStationType?token=75693db267804dbdb7127e06064821ce&stationType=2",
                String.class, entity);
        if (response.getStatusCode().is2xxSuccessful()) {
            String jsonResponse = response.getBody();
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                JsonStationInformationResponse jsonStationInformationResponse =
                        objectMapper.readValue(jsonResponse, JsonStationInformationResponse.class);
                List<StationInformationVo> stationInfoList = jsonStationInformationResponse.getDetail();
                log.info("得到的泵站信息为" + stationInfoList);
                Map<String,StationInformationVo> map = new HashMap<>();
                String codes = "";
                if (stationInfoList != null && !stationInfoList.isEmpty()) {
                    codes = stationInfoList.stream()
                            .filter(vo -> vo.getName() != null && vo.getName().contains(type))
                            .map(StationInformationVo::getCode)
                            .collect(Collectors.joining(","));
                    log.info(codes);
                    for (StationInformationVo stationInformationVo : stationInfoList) {
                        map.put(stationInformationVo.getCode(),stationInformationVo);
                    }
                }
                ResponseEntity<String> pumpResponse = restTemplate.getForEntity(
                        "http://*********:23486/deviot-open-api/api/external/" +
                                "findSameStationTypeNewsData?token=75693db267804dbdb7127e06064821ce&stationType=2&stationCodes=" + codes,
                        String.class, entity);
                if (pumpResponse.getStatusCode().is2xxSuccessful()) {
                    String jsonPumpResponse = pumpResponse.getBody();
                    try {
                        JsonPupmVoResponse jsonFzsSlhVoResponse =
                                objectMapper.readValue(jsonPumpResponse, JsonPupmVoResponse.class);
                        List<PumpStationVo> pumpStationInteractiveList = jsonFzsSlhVoResponse.getDetail();
                        if (pumpStationInteractiveList.size() > 0) {
                            for (PumpStationVo pumpStationVo : pumpStationInteractiveList) {
                                PumpStationInteractiveVo pumpStationInteractiveVo = new PumpStationInteractiveVo();
                                StationInformationVo stationInformationVo = map.get(pumpStationVo.getStationCode());
                                if(Objects.nonNull(stationInformationVo))
                                    BeanUtils.copyProperties(stationInformationVo,pumpStationInteractiveVo);
                                BeanUtils.copyProperties(pumpStationVo, pumpStationInteractiveVo);
                                int nullCount = 0;
                                int notNullCount = 0;
                                List<String> statusList = new ArrayList<>();
                                String[] propertyNames = {
                                        "kp1K", "kp2K", "kp3K", "kp4K", "kp5K", "kp6K", "kp7K", "kp8K",
                                        "kp9K", "kp10K", "kp11K", "kp12K", "kp13K", "kp14K"
                                };
                                for (String propertyName : propertyNames) {
                                    try {
                                        Field field = PumpStationVo.class.getDeclaredField(propertyName);
                                        field.setAccessible(true);
                                        Object value = field.get(pumpStationVo);
                                        if (value == null || "null".equals(value)) {
                                            nullCount++;
                                        } else {
                                            notNullCount++;
                                            statusList.add(String.valueOf(value));
                                        }
                                    } catch (NoSuchFieldException | IllegalAccessException e) {
                                        e.printStackTrace();
                                    }
                                }
                                String status = statusList.stream()
                                        .collect(Collectors.joining(","));
                                pumpStationInteractiveVo.setPumpStatus(status);
                                pumpStationInteractiveVo.setPumpNumber(String.valueOf(notNullCount));
                                pumpStationInteractiveVoList.add(pumpStationInteractiveVo);
                            }

                        }
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            System.out.println("请求失败，状态码: " + response.getStatusCode());
        }
        return ResponseData.ok(pumpStationInteractiveVoList);
    }
}





