package com.hjb.beijin_paishui_data_middleground.service.interactive;

import com.hjb.beijin_paishui_data_middleground.controller.interactive.WaterWorksInteractiveController;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.PopUpNotificationVo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface WaterWorksInteractiveService {

    /**
     * 通用请求方法(弹窗)
     */
    PopUpNotificationVo buildIndicatorSection(String title, List<WaterWorksInteractiveController.IndicatorInfo> indicatorList);

    /**
     * 通用请求方法(弹窗)
     */
    WaterWorksInteractiveController.PopUpNotificationVo2 getPreDehydrationArea(String title);

}
