package com.hjb.beijin_paishui_data_middleground.service.interactive.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.PopUpNotificationVo;
import com.hjb.beijin_paishui_data_middleground.entity.json.JsonFzsSlhVoResponse;
import com.hjb.beijin_paishui_data_middleground.entity.json.JsonRainGaugeInformationResponse;
import com.hjb.beijin_paishui_data_middleground.entity.json.JsonStationInformationResponse;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw.ADept;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw.GwPipe;
import com.hjb.beijin_paishui_data_middleground.entity.sub.vo.RainGaugeInformationVo;
import com.hjb.beijin_paishui_data_middleground.entity.sub.vo.StationInformationVo;
import com.hjb.beijin_paishui_data_middleground.entity.sub.vo.StationMonitoringVo;
import com.hjb.beijin_paishui_data_middleground.service.interactive.FloodInteractiveService;
import com.hjb.beijin_paishui_data_middleground.service.interactive.TheBridgeInteractiveService;
import com.hjb.beijin_paishui_data_middleground.service.sub.ADeptService;
import com.hjb.beijin_paishui_data_middleground.service.sub.GwPipeService;
import com.hjb.beijin_paishui_data_middleground.utils.interactive.StationMonitoringVoToNewFormatConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Description: 桥区交互接口Impl
 * @Author: lvhongen
 * @Date: 2025-05-14 17:08
 * @Version： 1.0
 **/
@Service
@Slf4j
public class TheBridgeInteractiveServiceImpl implements TheBridgeInteractiveService {
    @Resource
    private FloodInteractiveService floodInteractiveService;
    @Resource
    private GwPipeService gwPipeService;
    @Resource
    private ADeptService aDeptService;

    @Override
    public ResponseData<StationMonitoringVoToNewFormatConverter.NewFormat> getMonitoringOfAccumulatedWaterInfo (String stationCode) {
        ResponseData<List<StationMonitoringVo>> jsdjc = floodInteractiveService.getPipelineLiquidLevel("jsdjc");
        List<StationMonitoringVo> stationMonitoringVoList = jsdjc.getData();
        if (!stationMonitoringVoList.isEmpty()) {
            for (StationMonitoringVo stationMonitoringVo : stationMonitoringVoList) {
                if (stationCode.equals(stationMonitoringVo.getStationCode()))
                    return ResponseData.ok(StationMonitoringVoToNewFormatConverter.convert(stationMonitoringVo));
            }
        }
        return ResponseData.ok(new StationMonitoringVoToNewFormatConverter.NewFormat());
    }

    @Override
    public ResponseData<List<StationInformationVo>> getFzsSlhPoint () {
        List<StationInformationVo> stationInformationVoList = new ArrayList<>();
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> entity = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.getForEntity(
                "http://10.2.0.75:23486/deviot-open-api/api/external/" +
                        "findStationListByStationType?token=75693db267804dbdb7127e06064821ce&stationType= 95",
                String.class, entity);

        if (response.getStatusCode().is2xxSuccessful()) {
            String jsonResponse = response.getBody();
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                JsonStationInformationResponse jsonStationInformationResponse =
                        objectMapper.readValue(jsonResponse, JsonStationInformationResponse.class);
                List<StationInformationVo> stationInfoList = jsonStationInformationResponse.getDetail();
                for (StationInformationVo stationInformationVo : stationInfoList) {
                    if (!stationInformationVo.getName().contains("(废)") && stationInformationVo.getName().contains("分钟寺"))
                        stationInformationVoList.add(stationInformationVo);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            System.out.println("请求失败，状态码: " + response.getStatusCode());
        }
        return ResponseData.ok(stationInformationVoList);
    }

    @Override
    public ResponseData<PopUpNotificationVo> getFzsSlhLiquidLevelByCode (String code) {
        PopUpNotificationVo popUpNotificationVo = new PopUpNotificationVo();
        popUpNotificationVo.setTitle("雨水管道液位");
        List<PopUpNotificationVo.Section> sectionList = new ArrayList<>();
        List<StationInformationVo> stationInformationVoList = new ArrayList<>();
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> entity = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.getForEntity(
                "http://10.2.0.75:23486/deviot-open-api/api/external/" +
                        "findSameStationTypeNewsData?token=75693db267804dbdb7127e06064821ce&stationType=95&stationCodes=" + code,
                String.class, entity);

        if (response.getStatusCode().is2xxSuccessful()) {
            String jsonResponse = response.getBody();
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                JsonFzsSlhVoResponse jsonFzsSlhVoResponse =
                        objectMapper.readValue(jsonResponse, JsonFzsSlhVoResponse.class);
                List<StationMonitoringVo> stationInfoList = jsonFzsSlhVoResponse.getDetail();
                if (stationInfoList.size() > 0) {
                    StationMonitoringVo stationMonitoringVo = stationInfoList.get(0);
                    double gj = 0;
                    LambdaQueryWrapper<GwPipe> gwPipeLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    gwPipeLambdaQueryWrapper.eq(GwPipe::getSyjdm, stationMonitoringVo.getStationCode());
                    List<GwPipe> gwPipeList = gwPipeService.list(gwPipeLambdaQueryWrapper);
                    GwPipe gwPipe = gwPipeList.get(0);
                    ADept aDept = aDeptService.getById(gwPipe.getDept());
                    gj = Double.parseDouble(gwPipe.getGk());
                    log.info("测站编码为" + stationMonitoringVo.getStationCode() + "--------管径为" + gj);
                    log.info("监测信息为" + stationMonitoringVo + "--------管径为" + gj);
                    double v = 0;
                    if (Objects.nonNull(stationMonitoringVo.getLiquidLevel())) {
                        v = Double.parseDouble(stationMonitoringVo.getLiquidLevel()) * 1000.00 / gj * 100;
                    }
                    log.info("测站编码为" + stationMonitoringVo.getStationCode() + "--------充满度为" + v);
                    List<PopUpNotificationVo.KeyValue> keyValueList = new ArrayList<>();
                    keyValueList.add(new PopUpNotificationVo.KeyValue("测站名称", stationMonitoringVo.getStationName()));
                    keyValueList.add(new PopUpNotificationVo.KeyValue("充满度(%)", String.format("%.2f",v)));
                    keyValueList.add(new PopUpNotificationVo.KeyValue("所属单位",aDept.getName()));
                    keyValueList.add(new PopUpNotificationVo.KeyValue("所属行政区","朝阳区"));
                    sectionList.add(new PopUpNotificationVo.Section("基本信息",keyValueList));
                    popUpNotificationVo.setData(sectionList);
                }
            } catch (JsonMappingException e) {
                throw new RuntimeException(e);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }
        return ResponseData.ok(popUpNotificationVo);
    }

    @Override
    public ResponseData<PopUpNotificationVo> getFzsSlhRainCondition () {
        PopUpNotificationVo popUpNotificationVo = new PopUpNotificationVo();
        popUpNotificationVo.setTitle("雨水管道液位");
        List<PopUpNotificationVo.Section> sectionList = new ArrayList<>();
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> entity = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.getForEntity(
                "http://10.2.0.75:23486/deviot-open-api/api/external/" +
                        "findSameStationTypeNewsData?token=75693db267804dbdb7127e06064821ce&stationType=9&stationCodes=Y198",
                String.class, entity);

        if (response.getStatusCode().is2xxSuccessful()) {
            String jsonResponse = response.getBody();
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                JsonRainGaugeInformationResponse jsonFzsSlhVoResponse =
                        objectMapper.readValue(jsonResponse, JsonRainGaugeInformationResponse.class);
                List<RainGaugeInformationVo> rainGaugeInformationVos = jsonFzsSlhVoResponse.getDetail();
                if (rainGaugeInformationVos.size() > 0) {
                    RainGaugeInformationVo rainGaugeInformationVo = rainGaugeInformationVos.get(0);
                    List<PopUpNotificationVo.KeyValue> keyValueList = new ArrayList<>();
                    keyValueList.add(new PopUpNotificationVo.KeyValue("编码", rainGaugeInformationVo.getStationCode()));
                    keyValueList.add(new PopUpNotificationVo.KeyValue("名称", rainGaugeInformationVo.getStationName()));
                    keyValueList.add(new PopUpNotificationVo.KeyValue("雨量",rainGaugeInformationVo.getRain()));
                    keyValueList.add(new PopUpNotificationVo.KeyValue("雨强(5)",rainGaugeInformationVo.getRaininess5M()));
                    sectionList.add(new PopUpNotificationVo.Section("基本信息",keyValueList));
                    popUpNotificationVo.setData(sectionList);
                }
            } catch (JsonMappingException e) {
                throw new RuntimeException(e);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }
        return ResponseData.ok(popUpNotificationVo);
    }
}




