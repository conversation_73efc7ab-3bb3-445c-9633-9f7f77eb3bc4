package com.hjb.beijin_paishui_data_middleground.service.interactive;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.dto.GwPipeSeachDto;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.dto.GwWellSeachDto;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.*;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.fx.ShareBrgInfo;
import com.hjb.beijin_paishui_data_middleground.entity.sub.vo.ShareBrgInfoVo;
import com.hjb.beijin_paishui_data_middleground.utils.interactive.GwPipeVoToNewFormatConverter;
import com.hjb.beijin_paishui_data_middleground.utils.interactive.GwWellVoToNewFormatConverter;

import java.util.List;

/**
 * @Description: 综合态势service
 * @Author: lvhongen
 * @Date: 2025-03-19 22:26
 * @Version： 1.0
 **/
public interface SyntheticaInteractiveService {
    /**
     * 获取综合态势查询列表
     * @return
     */
    ResponseData<SyntheticaSelectData> getSyntheticaSelectData ();

    /**
     * 获取所有的行政区点位信息
     * @return
     */
    ResponseData<List<PointVo>> getAllAdministrativeRegion ();

    /**
     * 通过行政区code获取行政区的详细详细
     * @param code
     * @return
     */

    ResponseData<PartialInteractionVo> getAdministrativeRegionByCode (String code);

    /**
     * 获取所有的再生水厂
     * @return
     */
    ResponseData<List<PointVo>> getAllWaterworks ();

    /**
     * 通过再生水厂code获取详细信息
     * @return
     */
    ResponseData<PartialInteractionVo> getWaterworksByCode (String code);

    /**
     * 获取所有的污水主干
     * @return
     */
    ResponseData<List<PointVo>> getAllSewageBackbone ();

    /**
     * 通过污水主干code获取详细详细
     * @param code
     * @return
     */
    ResponseData<PartialInteractionVo> getSewageBackboneByCode (String code);

    /**
     * 获取所有污水主干信息
     * @return
     */
    ResponseData<List<PointVo>> getAllRainwaterBackbone ();

    /**
     * 通过雨水主干code获取详细详细
     * @param code
     * @return
     */
    ResponseData<PartialInteractionVo> getRainwaterBackboneByCode (String code);

    /**
     * 获取所有运营单位信息
     * @return
     */
    ResponseData<List<PointVo>> getAllOperationUnit ();

    /**
     * 通过运营单位code获取详细详细
     * @param code
     * @return
     */
    ResponseData<PartialInteractionVo> getOperationUnitByCode (String code);

    /**
     * 获取所有运行班信息
     * @return
     */
    ResponseData<List<PointVo>> getAllRunningClass ();

    /**
     * 通过运行班code获取详细详细
     * @param code
     * @return
     */
    ResponseData<ABanzuVo> getRunningClassByCode (String code);

    /**
     *
     * @return
     */
    ResponseData<List<PointVo>> getAllSewageSmallWatershed ();

    /**
     * 通过污水小流域code获取详细详细
     * @param code
     * @return
     */
    ResponseData<List<AWsXlyVo>> getSewageSmallWatershedByCode (String code);

    /**
     * 获取所有雨水小流域信息
     * @return
     */
    ResponseData<List<PointVo>> getAllRainwaterSmallWatershed ();

    /**
     * 通过雨水小流域code获取详细详细
     * @param code
     * @return
     */
    ResponseData<List<AYsXlyVo>> getRainwaterSmallWatershedByCode (String code);

    /**
     * 获取所有污水流域信息
     * @return
     */
    ResponseData<List<PointVo>> getAllSewageBasin ();

    /**
     * 通过污水流域code获取详细详细
     * @param code
     * @return
     */
    ResponseData<PartialInteractionVo> getSewageBasinByCode (String code);

    /**
     * 获取所有雨水流域信息
     * @return
     */
    ResponseData<List<PointVo>> getAllRainwaterBasin ();

    /**
     * 通过雨水流域code获取详细详细
     * @param code
     * @return
     */
    ResponseData<PartialInteractionVo> getRainwaterBasinByCode (String code);

    /**
     * 获取所有巡查范围信息
     * @return
     */
    ResponseData<List<PointVo>> getAllInspectionScope ();

    /**
     * 通过雨水流域code获取详细详细
     * @param code
     * @return
     */
    ResponseData<AXunchaVo> getInspectionScopeByCode (String code);

    /**
     * 获取所有下凹桥区信息
     * @return
     */
    ResponseData<List<ShareBrgInfoVo>> getAlloverpass ();

    /**
     * 通过立交桥code获取详细详细
     * @param code
     * @return
     */
    ResponseData<ShareBrgInfo> getoverpassByCode (String code);

    /**
     * 获取所有雨水管线信息
     * @return
     */
    ResponseData<List<PointVo>> getAllRainwaterPipeline ();

    /**
     * 通过管线code获取管线详细信息
     * @param code
     * @return
     */
    ResponseData<GwPipeVoToNewFormatConverter.NewFormat> getPipeInfo (String code);

    /**
     * 获取所有污水管线信息
     * @return
     */
    ResponseData<List<PointVo>> getAllSewagePipeline ();

    /**
     * 通过污水管线code获取详细信息
     * @return
     */
    ResponseData<List<PointVo>> getAllConfluentPipeline ();

    /**
     * 通过类型获取所有管井信息
     * @return
     */
    ResponseData<List<PointVo>> getAllWellByGzw(String gzw);

    /**
     * 通过管井code获取详细信息
     * @param code
     * @return
     */
    ResponseData<GwWellVoToNewFormatConverter.NewFormat> getWellByCode (String code);

    ResponseData<IPage<GwPipeVo>>  seachGwPipePoint (GwPipeSeachDto gwPipeSeachDto);

    /**
     * 查询管井台账点位列表
     * @param gwWellSeachDto
     * @return
     */
    ResponseData<IPage<GwWellVo>> seachGwWellPoint (GwWellSeachDto gwWellSeachDto);
}
