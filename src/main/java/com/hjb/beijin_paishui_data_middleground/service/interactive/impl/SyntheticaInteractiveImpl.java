package com.hjb.beijin_paishui_data_middleground.service.interactive.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.gson.Gson;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.dto.GwPipeSeachDto;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.dto.GwWellSeachDto;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.*;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.fx.ShareBrgInfo;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw.*;
import com.hjb.beijin_paishui_data_middleground.entity.sub.vo.ShareBrgInfoVo;
import com.hjb.beijin_paishui_data_middleground.mapper.sub.ADeptMapper;
import com.hjb.beijin_paishui_data_middleground.mapper.sub.GwPipeMapper;
import com.hjb.beijin_paishui_data_middleground.mapper.sub.GwWellsMapper;
import com.hjb.beijin_paishui_data_middleground.mapper.sub.ShareBrgInfoMapper;
import com.hjb.beijin_paishui_data_middleground.mapper.sub.interactive.*;
import com.hjb.beijin_paishui_data_middleground.service.interactive.SyntheticaInteractiveService;
import com.hjb.beijin_paishui_data_middleground.service.sub.*;
import com.hjb.beijin_paishui_data_middleground.utils.interactive.GwPipeVoToNewFormatConverter;
import com.hjb.beijin_paishui_data_middleground.utils.interactive.GwWellVoToNewFormatConverter;
import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.io.WKTReader;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
 * @Description: 综合态势
 * @Author: lvhongen
 * @Date: 2025-03-19 22:30
 * @Version： 1.0
 **/
@Service
@Slf4j
public class SyntheticaInteractiveImpl implements SyntheticaInteractiveService {
    @Resource
    private ADeptMapper aDeptMapper;
    @Resource
    private GwPipeMapper gwPipeMapper;
    @Resource
    private AXzjMapper aXzjMapper;
    @Resource
    private APlantallMapper aPlantallMapper;
    @Resource
    private AWsMainMapper aWsMainMapper;
    @Resource
    private AYsMainMapper aYsMainMapper;
    @Resource
    private ABanzuMapper aBanzuMapper;
    @Resource
    private AWsXlyMapper aWsXlyMapper;
    @Resource
    private AYsXlyMapper aYsXlyMapper;
    @Resource
    private AWslyMapper aWslyMapper;
    @Resource
    private AYslyMapper aYslyMapper;
    @Resource
    private AXunchaMapper aXunchaMapper;
    @Resource
    private GwWellsMapper gwWellsMapper;
    @Resource
    private ShareBrgInfoMapper shareBrgInfoMapper;
    @Resource
    private GwPipeYhzqHisService gwPipeYhzqHisService;
    @Resource
    private CctvService cctvService;
    @Resource
    private CctvWorkRecordService cctvWorkRecordService;
    @Resource
    private ScPlanService scPlanService;
    @Resource
    private ScWorkRecordService scWorkRecordService;
    @Resource
    private GwPipeExtService gwPipeExtService;
    @Resource
    private GwWellExtService gwWellExtService;
    @Resource
    private GwGxgzService gwGxgzService;
    @Resource
    private GwPipeInfoService gwPipeInfoService;
    @Resource
    private ScPipeDcHisService scPipeDcHisService;
    @Resource
    private GwWellInfoService gwWellInfoService;
    @Resource
    private ScWellDcHisService scWellDcHisService;

    @Override
    public ResponseData<SyntheticaSelectData> getSyntheticaSelectData() {
        SyntheticaSelectData syntheticaSelectData = new SyntheticaSelectData();
        // 设施台账
        List<SyntheticaSelectData.Select> facilityLedger = new ArrayList<>();
        List<SyntheticaSelectData.SelectData> selectDeptDataList = aDeptMapper.getSyntheticaInteractiveSelect();
        SyntheticaSelectData.Select selectDept = new SyntheticaSelectData.Select();
        selectDept.setName("dept");
        selectDept.setValue(selectDeptDataList);
        List<SyntheticaSelectData.SelectData> selectAxzjDataList = aXzjMapper.getSyntheticaInteractiveSelect();
        SyntheticaSelectData.Select selectAxzj = new SyntheticaSelectData.Select();
        selectAxzj.setName("xzq");
        selectAxzj.setValue(selectAxzjDataList);
        List<SyntheticaSelectData.SelectData> selectsyxzDataList = new ArrayList<>();
        selectsyxzDataList.add(new SyntheticaSelectData.SelectData("污水", "ws"));
        selectsyxzDataList.add(new SyntheticaSelectData.SelectData("雨水", "ys"));
        selectsyxzDataList.add(new SyntheticaSelectData.SelectData("合流", "hl"));
        SyntheticaSelectData.Select selectsyxz = new SyntheticaSelectData.Select();
        selectsyxz.setName("syxz");
        selectsyxz.setValue(selectsyxzDataList);
        List<SyntheticaSelectData.SelectData> selectsfqsDataList = new ArrayList<>();
        selectsfqsDataList.add(new SyntheticaSelectData.SelectData("是", "y"));
        selectsfqsDataList.add(new SyntheticaSelectData.SelectData("否", "n"));
        SyntheticaSelectData.Select selectsfqs = new SyntheticaSelectData.Select();
        selectsfqs.setName("sfqs");
        selectsfqs.setValue(selectsfqsDataList);
        facilityLedger.add(selectDept);
        facilityLedger.add(selectAxzj);
        facilityLedger.add(selectsyxz);
        facilityLedger.add(selectsfqs);
        syntheticaSelectData.setFacilityLedger(facilityLedger);
        // 管道台账
        List<SyntheticaSelectData.Select> pipelineLedger = new ArrayList<>();
        pipelineLedger.add(selectsyxz);
        List<SyntheticaSelectData.SelectData> selectTsgdDataList = new ArrayList<>();
        selectTsgdDataList.add(new SyntheticaSelectData.SelectData("非特殊", "fts"));
        selectTsgdDataList.add(new SyntheticaSelectData.SelectData("边沟", "bg"));
        selectTsgdDataList.add(new SyntheticaSelectData.SelectData("倒虹吸", "dhx"));
        selectTsgdDataList.add(new SyntheticaSelectData.SelectData("截流井", "jlj"));
        selectTsgdDataList.add(new SyntheticaSelectData.SelectData("双排管", "pg2"));
        selectTsgdDataList.add(new SyntheticaSelectData.SelectData("三排管", "pg3"));
        selectTsgdDataList.add(new SyntheticaSelectData.SelectData("四排管", "pg4"));
        selectTsgdDataList.add(new SyntheticaSelectData.SelectData("U型槽", "uxc"));
        selectTsgdDataList.add(new SyntheticaSelectData.SelectData("压力管", "ylg"));
        selectTsgdDataList.add(new SyntheticaSelectData.SelectData("雨水口支管", "yskzg"));
        selectTsgdDataList.add(new SyntheticaSelectData.SelectData("无下游", "wxy"));
        SyntheticaSelectData.Select selectTsgd = new SyntheticaSelectData.Select();
        selectTsgd.setName("tsgd");
        selectTsgd.setValue(selectTsgdDataList);
        pipelineLedger.add(selectTsgd);
        List<SyntheticaSelectData.SelectData> selectSyztDataList = new ArrayList<>();
        selectSyztDataList.add(new SyntheticaSelectData.SelectData("使用", "using"));
        selectSyztDataList.add(new SyntheticaSelectData.SelectData("拟废", "toaban"));
        selectSyztDataList.add(new SyntheticaSelectData.SelectData("报废", "abandon"));
        selectTsgdDataList.add(new SyntheticaSelectData.SelectData("现场无", "xcw"));
        SyntheticaSelectData.Select selectSyzt = new SyntheticaSelectData.Select();
        selectSyzt.setName("syzt");
        selectSyzt.setValue(selectSyztDataList);
        pipelineLedger.add(selectSyzt);
        List<SyntheticaSelectData.SelectData> selectGxlxDataList = new ArrayList<>();
        selectGxlxDataList.add(new SyntheticaSelectData.SelectData("主干线", "main"));
        selectGxlxDataList.add(new SyntheticaSelectData.SelectData("雨水口支管", "zg"));
        selectGxlxDataList.add(new SyntheticaSelectData.SelectData("次干线", "branch"));
        selectGxlxDataList.add(new SyntheticaSelectData.SelectData("支线", "zx"));
        selectGxlxDataList.add(new SyntheticaSelectData.SelectData("户线", "hx"));
        SyntheticaSelectData.Select selectSyxz = new SyntheticaSelectData.Select();
        selectSyxz.setName("gxlx");
        selectSyxz.setValue(selectGxlxDataList);
        pipelineLedger.add(selectSyxz);
        syntheticaSelectData.setPipelineLedger(pipelineLedger);
        // 管井台账
        List<SyntheticaSelectData.Select> pipeWellLedger = new ArrayList<>();
        pipeWellLedger.add(selectsyxz);
        pipeWellLedger.add(selectsfqs);
        pipeWellLedger.add(selectSyzt);
        List<SyntheticaSelectData.SelectData> selectGzwDataList = new ArrayList<>();
        selectGzwDataList.add(new SyntheticaSelectData.SelectData("检查井", "jcj"));
        selectGzwDataList.add(new SyntheticaSelectData.SelectData("雨水口", "ysk"));
        selectGzwDataList.add(new SyntheticaSelectData.SelectData("排河口", "phk"));
        selectGzwDataList.add(new SyntheticaSelectData.SelectData("倒虹吸", "dhx"));
        selectGzwDataList.add(new SyntheticaSelectData.SelectData("截流井", "jlj"));
        selectGzwDataList.add(new SyntheticaSelectData.SelectData("闸门井", "zj"));
        selectGzwDataList.add(new SyntheticaSelectData.SelectData("调水闸井", "dszj"));
        selectGzwDataList.add(new SyntheticaSelectData.SelectData("跌水井", "dsj"));
        selectGzwDataList.add(new SyntheticaSelectData.SelectData("溢流井", "ylj"));
        selectGzwDataList.add(new SyntheticaSelectData.SelectData("进水口", "jsk"));
        selectGzwDataList.add(new SyntheticaSelectData.SelectData("出水口", "csk"));
        selectGzwDataList.add(new SyntheticaSelectData.SelectData("雨篦", "yb"));
        selectGzwDataList.add(new SyntheticaSelectData.SelectData("暗井", "anjing"));
        selectGzwDataList.add(new SyntheticaSelectData.SelectData("独立篦子", "dlbz"));
        selectGzwDataList.add(new SyntheticaSelectData.SelectData("化粪池", "hfc"));
        selectGzwDataList.add(new SyntheticaSelectData.SelectData("管点", "gd"));
        selectGzwDataList.add(new SyntheticaSelectData.SelectData("其他", "other"));
        SyntheticaSelectData.Select selectGzw = new SyntheticaSelectData.Select();
        selectGzw.setName("gzw");
        selectGzw.setValue(selectGzwDataList);
        pipeWellLedger.add(selectGzw);
        syntheticaSelectData.setPipeWellLedger(pipeWellLedger);
        return ResponseData.ok(syntheticaSelectData);
    }

    @Override
    public ResponseData<IPage<GwPipeVo>> seachGwPipePoint(GwPipeSeachDto gwPipeSeachDto) {
        Page<GwPipeAndGzTzAndGwYyTzInfo> page = new Page<>(gwPipeSeachDto.getCurrentPage(), gwPipeSeachDto.getPageSize());
        IPage<GwPipeAndGzTzAndGwYyTzInfo> gwPipePointPage = gwPipeMapper.seachGwPipePoint(page, gwPipeSeachDto);

        List<GwPipeVo> gwPipeVoList = new ArrayList<>();
        for (GwPipeAndGzTzAndGwYyTzInfo item : gwPipePointPage.getRecords()) {
            if (Objects.nonNull(item)) {
                GwPipeVo vo = new GwPipeVo();
                GwPipeVo.BasicInformation basicInformation = new GwPipeVo.BasicInformation();
                GwPipeVo.OperationsInformation operationsInformation = new GwPipeVo.OperationsInformation();
                GwPipeVo.MaintenanceCycleInformation maintenanceCycleInformation = new GwPipeVo.MaintenanceCycleInformation();
                GwPipeVo.CctvDetectionInformation cctvDetectionInformation = new GwPipeVo.CctvDetectionInformation();
                GwPipeVo.ManagementInformation managementInformation = new GwPipeVo.ManagementInformation();
                GwPipeVo.EngineeringInformation engineeringInformation = new GwPipeVo.EngineeringInformation();
                GwPipeVo.HiddenInformation hiddenInformation = new GwPipeVo.HiddenInformation();
                GwPipeVo.InvestigationInformation investigationInformation = new GwPipeVo.InvestigationInformation();

                BeanUtils.copyProperties(item, basicInformation);

                GwPipe gwPipe = gwPipeMapper.selectOne(new LambdaQueryWrapper<GwPipe>().eq(GwPipe::getCode, item.getCode()));
                if (Objects.nonNull(gwPipe)) {
                    BeanUtils.copyProperties(gwPipe, engineeringInformation);
                    engineeringInformation.setXmbh(item.getXmbh());
                    engineeringInformation.setYjly(item.getYjly());
                    engineeringInformation.setGqName(item.getGqName());
                    engineeringInformation.setTzName(item.getTzName());
                }

                GwPipeYhzqHis yhzqHis = gwPipeYhzqHisService.getOne(new LambdaQueryWrapper<GwPipeYhzqHis>()
                        .eq(GwPipeYhzqHis::getYhzqGddm, item.getCode())
                        .orderByDesc(GwPipeYhzqHis::getYhzqDate).last("LIMIT 1"));
                if (Objects.nonNull(yhzqHis)) BeanUtils.copyProperties(yhzqHis, maintenanceCycleInformation);

                GwPipeInfo pipeInfo = gwPipeInfoService.getOne(new LambdaQueryWrapper<GwPipeInfo>().eq(GwPipeInfo::getTheCode, item.getCode()));
                if (pipeInfo != null && pipeInfo.getStatInfo() != null) {
                    GwPipeVo.ManagementInformation info = new Gson().fromJson(pipeInfo.getStatInfo(), GwPipeVo.ManagementInformation.class);
                    if (info != null) {
                        BeanUtils.copyProperties(info, managementInformation);
                        BeanUtils.copyProperties(item, managementInformation);
                        BeanUtils.copyProperties(gwPipe, managementInformation);
                        if (managementInformation.getDeptName() != null)
                            managementInformation.setDeptName(managementInformation.getDeptName() + "公司");
                        if (managementInformation.getSyzt() != null)
                            managementInformation.setSyzt(getActiveStateName(managementInformation.getSyzt()));
                        if (managementInformation.getSfqs() != null)
                            managementInformation.setSfqs(getSyqsName(managementInformation.getSfqs()));
                    }
                }

                Cctv cctv = cctvService.getOne(new LambdaQueryWrapper<Cctv>().eq(Cctv::getCctvGddm, item.getCode()));
                if (cctv != null) {
                    CctvWorkRecord workRecord = cctvWorkRecordService.getById(cctv.getCctvWorkId());
                    if (workRecord != null) {
                        ScPlan plan = scPlanService.getById(workRecord.getPlanUuid());
                        if (plan != null) BeanUtils.copyProperties(plan, cctvDetectionInformation);
                        BeanUtils.copyProperties(cctv, cctvDetectionInformation);
                    }
                }

                ScPipeDcHis dcHis = scPipeDcHisService.getOne(new LambdaQueryWrapper<ScPipeDcHis>()
                        .eq(ScPipeDcHis::getDcGddm, item.getCode()).orderByDesc(ScPipeDcHis::getDcDate).last("LIMIT 1"));
                if (dcHis != null) {
                    BeanUtils.copyProperties(dcHis, investigationInformation);
                    investigationInformation.setDcYxzk(getYxzkName(investigationInformation.getDcYxzk()));
                    if (dcHis.getDcDate() != null)
                        investigationInformation.setDcDate(dcHis.getDcDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                    ScWorkRecord workRecord = scWorkRecordService.getById(dcHis.getDcWorkId());
                    if (workRecord != null && workRecord.getPlanUuid() != null) {
                        ScPlan plan = scPlanService.getById(workRecord.getPlanUuid());
                        investigationInformation.setBianHao(plan.getBianHao());
                        investigationInformation.setTzName(plan.getTzName());
                    }
                }

                GwPipeExt ext = gwPipeExtService.getOne(new LambdaQueryWrapper<GwPipeExt>().eq(GwPipeExt::getCodeExt, item.getCode()));
                if (ext != null) {
                    BeanUtils.copyProperties(ext, operationsInformation);
                    GwGxgz gxgz = gwGxgzService.getOne(new LambdaQueryWrapper<GwGxgz>().eq(GwGxgz::getUuid, ext.getGxgzPlan()));
                    if (gxgz != null) BeanUtils.copyProperties(gxgz, hiddenInformation);
                }

                basicInformation.setGjType(pipeDiameterType(basicInformation.getGjType()));
                basicInformation.setSyxz(getNatureOfUseName(basicInformation.getSyxz()));
                basicInformation.setJgxs(structuralStyleName(basicInformation.getJgxs()));
                basicInformation.setTsgd(getSpecialFacilitiesName(basicInformation.getTsgd()));
                basicInformation.setGxlx(getPipelineType(basicInformation.getGxlx()));
                basicInformation.setGdcz(pipelineMaterial(basicInformation.getGdcz()));

                vo.setBasicInformation(basicInformation);
                vo.setMaintenanceCycleInformation(maintenanceCycleInformation);
                vo.setCctvDetectionInformation(cctvDetectionInformation);
                vo.setHiddenInformation(hiddenInformation);
                vo.setEngineeringInformation(engineeringInformation);
                vo.setManagementInformation(managementInformation);
                vo.setOperationsInformation(operationsInformation);
                vo.setInvestigationInformation(investigationInformation);
                gwPipeVoList.add(vo);
            }
        }

        IPage<GwPipeVo> resultPage = new Page<>(page.getCurrent(), page.getSize(), gwPipePointPage.getTotal());
        resultPage.setRecords(gwPipeVoList);
        return ResponseData.ok(resultPage);
    }


    @Override
    public ResponseData<IPage<GwWellVo>> seachGwWellPoint(GwWellSeachDto gwWellSeachDto) {
        Page<GwWellAndGzTzAndGwYyTzInfo> page = new Page<>(gwWellSeachDto.getCurrentPage(), gwWellSeachDto.getPageSize());

        IPage<GwWellAndGzTzAndGwYyTzInfo> gwPipePage = gwWellsMapper.seachGwWellPoint(page, gwWellSeachDto);

        List<GwWellVo> gwWellVoList = new ArrayList<>();
        for (GwWellAndGzTzAndGwYyTzInfo gwWellAndGzTzAndGwYyTzInfo : gwPipePage.getRecords()) {
            if (gwWellAndGzTzAndGwYyTzInfo == null) continue;

            GwWellVo gwWellVo = new GwWellVo();
            GwWellVo.OperationsInformation operationsInformation = new GwWellVo.OperationsInformation();
            GwWellVo.BasicInformation basicInformation = new GwWellVo.BasicInformation();
            GwWellVo.InvestigationInformation investigationInformation = new GwWellVo.InvestigationInformation();
            GwWellVo.ManagementInformation managementInformation = new GwWellVo.ManagementInformation();
            GwWellVo.EngineeringInformation engineeringInformation = new GwWellVo.EngineeringInformation();
            GwWellVo.HiddenInformation hiddenInformation = new GwWellVo.HiddenInformation();

            BeanUtils.copyProperties(gwWellAndGzTzAndGwYyTzInfo, basicInformation);

            GwWells gwWells = gwWellsMapper.selectOne(new LambdaQueryWrapper<GwWells>()
                    .eq(GwWells::getCode, gwWellAndGzTzAndGwYyTzInfo.getCode()));
            if (gwWells != null) {
                BeanUtils.copyProperties(gwWells, engineeringInformation);
                engineeringInformation.setXmbh(gwWellAndGzTzAndGwYyTzInfo.getXmbh());
                engineeringInformation.setYjly(gwWellAndGzTzAndGwYyTzInfo.getYjly());
                engineeringInformation.setGqName(gwWellAndGzTzAndGwYyTzInfo.getGqName());
                engineeringInformation.setTzName(gwWellAndGzTzAndGwYyTzInfo.getTzName());
            }

            GwWellInfo gwWellInfo = gwWellInfoService.getOne(new LambdaQueryWrapper<GwWellInfo>()
                    .eq(GwWellInfo::getTheCode, gwWellAndGzTzAndGwYyTzInfo.getCode()));
            if (gwWellInfo != null && gwWellInfo.getStatInfo() != null) {
                GwWellVo.ManagementInformation mi = new Gson().fromJson(gwWellInfo.getStatInfo(), GwWellVo.ManagementInformation.class);
                if (mi != null) {
                    BeanUtils.copyProperties(mi, managementInformation);
                    BeanUtils.copyProperties(gwWellAndGzTzAndGwYyTzInfo, managementInformation);
                    BeanUtils.copyProperties(gwWells, managementInformation);
                    if (managementInformation.getDeptName() != null) {
                        managementInformation.setDeptName(managementInformation.getDeptName() + "公司");
                    }
                    if (managementInformation.getSyzt() != null) {
                        managementInformation.setSyzt(getActiveStateName(managementInformation.getSyzt()));
                    }
                    if (managementInformation.getSfqs() != null) {
                        managementInformation.setSfqs(getSyqsName(managementInformation.getSfqs()));
                    }
                }
            }

            ScWellDcHis scWellDcHis = scWellDcHisService.getOne(new LambdaQueryWrapper<ScWellDcHis>()
                    .eq(ScWellDcHis::getDcJdm, gwWellAndGzTzAndGwYyTzInfo.getCode())
                    .orderByDesc(ScWellDcHis::getDcDate).last("LIMIT 1"));
            if (scWellDcHis != null) {
                BeanUtils.copyProperties(scWellDcHis, investigationInformation);
                investigationInformation.setDcHw(getSyqsName(investigationInformation.getDcHw()));
                investigationInformation.setDcQs(getSyqsName(investigationInformation.getDcQs()));
                investigationInformation.setDcFd(getDcFdName(investigationInformation.getDcFd()));
                investigationInformation.setDcZg(getSyqsName(investigationInformation.getDcZg()));
                investigationInformation.setDcTb(getSbZkName(investigationInformation.getDcTb()));
                investigationInformation.setDcGk(getSbZkName(investigationInformation.getDcGk()));
                investigationInformation.setDcJb(getSbZkName(investigationInformation.getDcJb()));
                investigationInformation.setDcLc(getSbZkName(investigationInformation.getDcLc()));
                investigationInformation.setDcJd(getDcJdName(investigationInformation.getDcJd()));
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                if (scWellDcHis.getDcDate() != null)
                    investigationInformation.setDcDate(scWellDcHis.getDcDate().format(formatter));
                if (scWellDcHis.getDcHwazDate() != null)
                    investigationInformation.setDcHwazDate(scWellDcHis.getDcHwazDate().format(formatter));

                ScWorkRecord scWorkRecord = scWorkRecordService.getById(scWellDcHis.getDcWorkId());
                if (scWorkRecord != null && scWorkRecord.getPlanUuid() != null) {
                    ScPlan scPlan = scPlanService.getById(scWorkRecord.getPlanUuid());
                    if (scPlan != null) {
                        investigationInformation.setBianHao(scPlan.getBianHao());
                        investigationInformation.setTzName(scPlan.getTzName());
                    }
                }
            }

            GwWellExt gwWellExt = gwWellExtService.getOne(new LambdaQueryWrapper<GwWellExt>()
                    .eq(GwWellExt::getCodeExt, gwWellAndGzTzAndGwYyTzInfo.getCode()));
            if (gwWellExt != null) {
                BeanUtils.copyProperties(gwWellExt, operationsInformation);
                GwGxgz gwGxgz = gwGxgzService.getOne(new LambdaQueryWrapper<GwGxgz>().eq(GwGxgz::getUuid, gwWellExt.getGxgzPlan()));
                if (gwGxgz != null) {
                    BeanUtils.copyProperties(gwGxgz, hiddenInformation);
                }
            }

            basicInformation.setSyxz(getNatureOfUseName(basicInformation.getSyxz()));
            basicInformation.setGzw(getGzwName(basicInformation.getGzw()));

            gwWellVo.setBasicInformation(basicInformation);
            gwWellVo.setEngineeringInformation(engineeringInformation);
            gwWellVo.setInvestigationInformation(investigationInformation);
            gwWellVo.setManagementInformation(managementInformation);
            gwWellVo.setOperationsInformation(operationsInformation);
            gwWellVo.setHiddenInformation(hiddenInformation);

            gwWellVoList.add(gwWellVo);
        }

        IPage<GwWellVo> resultPage = new Page<>();
        resultPage.setRecords(gwWellVoList);
        resultPage.setTotal(gwPipePage.getTotal());
        resultPage.setSize(gwPipePage.getSize());
        resultPage.setCurrent(gwPipePage.getCurrent());

        return ResponseData.ok(resultPage);
    }


    @Override
    public ResponseData<List<PointVo>> getAllAdministrativeRegion() {
        List<PointVo> list = aXzjMapper.getAllList();
        return ResponseData.ok(list);
    }

    @Override
    public ResponseData<PartialInteractionVo> getAdministrativeRegionByCode(String code) {
        AXzj aXzj = aXzjMapper.getByCode(code);
        PartialInteractionVo partialInteractionVo = new PartialInteractionVo();
        BeanUtils.copyProperties(aXzj, partialInteractionVo);
        try {
            // 解析 WKT 字符串
            Geometry geometry = new WKTReader().read(aXzj.getSgeom());
            // 计算面积和周长
            double area = geometry.getArea();      // 面积（平方米）
            double perimeter = geometry.getLength(); // 周长（米）
            partialInteractionVo.setA_length(String.format("%.2f", perimeter));
            partialInteractionVo.setA_area(String.format("%.2f", area));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ResponseData.ok(partialInteractionVo);
    }

    @Override
    public ResponseData<List<PointVo>> getAllWaterworks() {
        List<PointVo> list = aPlantallMapper.getAllList();
        log.info(list.toString());
        return ResponseData.ok(list);
    }

    @Override
    public ResponseData<PartialInteractionVo> getWaterworksByCode(String code) {
        APlantall aPlantall = aPlantallMapper.getByCode(code);
        PartialInteractionVo partialInteractionVo = new PartialInteractionVo();
        BeanUtils.copyProperties(aPlantall, partialInteractionVo);
        try {
            // 解析 WKT 字符串
            Geometry geometry = new WKTReader().read(aPlantall.getSgeom());
            // 计算面积和周长
            double area = geometry.getArea();      // 面积（平方米）
            double perimeter = geometry.getLength(); // 周长（米）
            partialInteractionVo.setA_length(String.format("%.2f", perimeter));
            partialInteractionVo.setA_area(String.format("%.2f", area));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ResponseData.ok(partialInteractionVo);
    }

    @Override
    public ResponseData<List<PointVo>> getAllSewageBackbone() {
        List<PointVo> aWsMainMapperAllList = aWsMainMapper.getAllList();
        return ResponseData.ok(aWsMainMapperAllList);
    }

    @Override
    public ResponseData<PartialInteractionVo> getSewageBackboneByCode(String code) {
        AWsMain aWsMain = aWsMainMapper.getByCode(code);
        PartialInteractionVo partialInteractionVo = new PartialInteractionVo();
        BeanUtils.copyProperties(aWsMain, partialInteractionVo);
        try {
            // 解析 WKT 字符串
            Geometry geometry = new WKTReader().read(aWsMain.getSgeom());
            // 计算面积和周长
            double area = geometry.getArea();      // 面积（平方米）
            double perimeter = geometry.getLength(); // 周长（米）
            partialInteractionVo.setA_length(String.format("%.2f", perimeter));
            partialInteractionVo.setA_area(String.format("%.2f", area));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ResponseData.ok(partialInteractionVo);
    }

    @Override
    public ResponseData<List<PointVo>> getAllRainwaterBackbone() {
        List<PointVo> aYsMainMapperAllList = aYsMainMapper.getAllList();
        return ResponseData.ok(aYsMainMapperAllList);
    }

    @Override
    public ResponseData<PartialInteractionVo> getRainwaterBackboneByCode(String code) {
        AYsMain aYsMain = aYsMainMapper.getByCode(code);
        PartialInteractionVo partialInteractionVo = new PartialInteractionVo();
        BeanUtils.copyProperties(aYsMain, partialInteractionVo);
        try {
            // 解析 WKT 字符串
            Geometry geometry = new WKTReader().read(aYsMain.getSgeom());
            // 计算面积和周长
            double area = geometry.getArea();      // 面积（平方米）
            double perimeter = geometry.getLength(); // 周长（米）
            partialInteractionVo.setA_length(String.format("%.2f", perimeter));
            partialInteractionVo.setA_area(String.format("%.2f", area));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ResponseData.ok(partialInteractionVo);
    }

    @Override
    public ResponseData<List<PointVo>> getAllOperationUnit() {
        List<PointVo> aDeptMapperAllList = aDeptMapper.getAllList();
        return ResponseData.ok(aDeptMapperAllList);
    }

    @Override
    public ResponseData<PartialInteractionVo> getOperationUnitByCode(String code) {
        ADept aDept = aDeptMapper.getByCode(code);
        PartialInteractionVo partialInteractionVo = new PartialInteractionVo();
        BeanUtils.copyProperties(aDept, partialInteractionVo);
        try {
            // 解析 WKT 字符串
            Geometry geometry = new WKTReader().read(aDept.getSgeom());
            // 计算面积和周长
            double area = geometry.getArea();      // 面积（平方米）
            double perimeter = geometry.getLength(); // 周长（米）
            partialInteractionVo.setA_length(String.format("%.2f", perimeter));
            partialInteractionVo.setA_area(String.format("%.2f", area));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ResponseData.ok(partialInteractionVo);
    }

    @Override
    public ResponseData<List<PointVo>> getAllRunningClass() {
        List<PointVo> aBanzuMapperAllList = aBanzuMapper.getAllList();
        return ResponseData.ok(aBanzuMapperAllList);
    }

    @Override
    public ResponseData<ABanzuVo> getRunningClassByCode(String code) {
        ABanzuVo aBanzuVo = aBanzuMapper.getByCode(code);
        aBanzuVo.setBanzuDesc(aBanzuVo.getName());
        try {
            // 解析 WKT 字符串
            Geometry geometry = new WKTReader().read(aBanzuVo.getSgeom());
            // 计算面积和周长
            double area = geometry.getArea();      // 面积（平方米）
            double perimeter = geometry.getLength(); // 周长（米）
            aBanzuVo.setA_length(String.format("%.2f", perimeter));
            aBanzuVo.setA_area(String.format("%.2f", area));
            aBanzuVo.setSgeom(null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ResponseData.ok(aBanzuVo);
    }

    @Override
    public ResponseData<List<PointVo>> getAllSewageSmallWatershed() {
        List<PointVo> aWsXlyMapperAllList = aWsXlyMapper.getAllList();
        return ResponseData.ok(aWsXlyMapperAllList);
    }

    @Override
    public ResponseData<List<AWsXlyVo>> getSewageSmallWatershedByCode(String code) {
        List<AWsXlyVo> aWsXlyVoList = aWsXlyMapper.getByCode(code);
        return ResponseData.ok(aWsXlyVoList);
    }

    @Override
    public ResponseData<List<PointVo>> getAllRainwaterSmallWatershed() {
        List<PointVo> aYsXlyMapperAllList = aYsXlyMapper.getAllList();
        return ResponseData.ok(aYsXlyMapperAllList);
    }

    @Override
    public ResponseData<List<AYsXlyVo>> getRainwaterSmallWatershedByCode(String code) {
        List<AYsXlyVo> aYsXlyVoList = aYsXlyMapper.getByCode(code);
        return ResponseData.ok(aYsXlyVoList);
    }

    @Override
    public ResponseData<List<PointVo>> getAllSewageBasin() {
        List<PointVo> aWslyMapperAllList = aWslyMapper.getAllList();
        return ResponseData.ok(aWslyMapperAllList);
    }

    @Override
    public ResponseData<PartialInteractionVo> getSewageBasinByCode(String code) {
        AWsly aWsly = aWslyMapper.getByCode(code);
        PartialInteractionVo partialInteractionVo = new PartialInteractionVo();
        BeanUtils.copyProperties(aWsly, partialInteractionVo);
        try {
            // 解析 WKT 字符串
            Geometry geometry = new WKTReader().read(aWsly.getSgeom());
            // 计算面积和周长
            double area = geometry.getArea();      // 面积（平方米）
            double perimeter = geometry.getLength(); // 周长（米）
            partialInteractionVo.setA_length(String.format("%.2f", perimeter));
            partialInteractionVo.setA_area(String.format("%.2f", area));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ResponseData.ok(partialInteractionVo);
    }

    @Override
    public ResponseData<List<PointVo>> getAllRainwaterBasin() {
        List<PointVo> aYslyMapperAllList = aYslyMapper.getAllList();
        return ResponseData.ok(aYslyMapperAllList);
    }

    @Override
    public ResponseData<PartialInteractionVo> getRainwaterBasinByCode(String code) {
        AYsMain aYsMain = aYsMainMapper.getByCode(code);
        PartialInteractionVo partialInteractionVo = new PartialInteractionVo();
        BeanUtils.copyProperties(aYsMain, partialInteractionVo);
        try {
            // 解析 WKT 字符串
            Geometry geometry = new WKTReader().read(aYsMain.getSgeom());
            // 计算面积和周长
            double area = geometry.getArea();      // 面积（平方米）
            double perimeter = geometry.getLength(); // 周长（米）
            partialInteractionVo.setA_length(String.format("%.2f", perimeter));
            partialInteractionVo.setA_area(String.format("%.2f", area));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ResponseData.ok(partialInteractionVo);
    }

    @Override
    public ResponseData<List<PointVo>> getAllInspectionScope() {
        List<PointVo> aXunchaMapperAllList = aXunchaMapper.getAllList();
        return ResponseData.ok(aXunchaMapperAllList);
    }

    @Override
    public ResponseData<AXunchaVo> getInspectionScopeByCode(String code) {
        AXunchaVo xunchaVo = aXunchaMapper.getByCode(code);
        try {
            // 解析 WKT 字符串
            Geometry geometry = new WKTReader().read(xunchaVo.getSgeom());
            // 计算面积和周长
            double area = geometry.getArea();      // 面积（平方米）
            double perimeter = geometry.getLength(); // 周长（米）
            xunchaVo.setA_length(String.format("%.2f", perimeter));
            xunchaVo.setA_area(String.format("%.2f", area));
            xunchaVo.setSgeom(null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ResponseData.ok(xunchaVo);
    }

    @Override
    public ResponseData<List<ShareBrgInfoVo>> getAlloverpass() {
        List<ShareBrgInfoVo> list = shareBrgInfoMapper.getAllList();
        return ResponseData.ok(list);
    }

    @Override
    public ResponseData<ShareBrgInfo> getoverpassByCode(String code) {
        ShareBrgInfo shareBrgInfo = shareBrgInfoMapper.selectOne(new LambdaQueryWrapper<ShareBrgInfo>().eq(
                ShareBrgInfo::getBrCode, code
        ));
        return ResponseData.ok(shareBrgInfo);
    }

    @Override
    public ResponseData<List<PointVo>> getAllRainwaterPipeline() {
        List<PointVo> ysPointVoList = gwPipeMapper.getAllList("ys");
        return ResponseData.ok(ysPointVoList);
    }

    @Override
    public ResponseData<GwPipeVoToNewFormatConverter.NewFormat> getPipeInfo(String code) {
        GwPipeVoToNewFormatConverter.NewFormat convert = new GwPipeVoToNewFormatConverter.NewFormat();
        GwPipeVo gwPipeVo = new GwPipeVo();
        GwPipeAndGzTzAndGwYyTzInfo gwPipeAndGzTzAndGwYyTzInfo = gwPipeMapper.getByCode(code);
        GwPipeVo.OperationsInformation operationsInformation = new GwPipeVo.OperationsInformation();
        GwPipeVo.BasicInformation basicInformation = new GwPipeVo.BasicInformation();
        GwPipeVo.MaintenanceCycleInformation maintenanceCycleInformation = new GwPipeVo.MaintenanceCycleInformation();
        GwPipeVo.CctvDetectionInformation cctvDetectionInformation = new GwPipeVo.CctvDetectionInformation();
        GwPipeVo.ManagementInformation managementInformation = new GwPipeVo.ManagementInformation();
        GwPipeVo.EngineeringInformation engineeringInformation = new GwPipeVo.EngineeringInformation();
        GwPipeVo.InvestigationInformation investigationInformation = new GwPipeVo.InvestigationInformation();
        GwPipeVo.HiddenInformation hiddenInformation = new GwPipeVo.HiddenInformation();
        if (Objects.nonNull(gwPipeAndGzTzAndGwYyTzInfo)) {
            BeanUtils.copyProperties(gwPipeAndGzTzAndGwYyTzInfo, basicInformation);
            LambdaQueryWrapper<GwPipe> gwPipeLambdaQueryWrapper = new LambdaQueryWrapper<>();
            gwPipeLambdaQueryWrapper.eq(GwPipe::getCode, gwPipeAndGzTzAndGwYyTzInfo.getCode());
            GwPipe gwPipe = gwPipeMapper.selectOne(gwPipeLambdaQueryWrapper);
            if (Objects.nonNull(gwPipe)) {
                BeanUtils.copyProperties(gwPipe, engineeringInformation);
                engineeringInformation.setXmbh(gwPipeAndGzTzAndGwYyTzInfo.getXmbh());
                engineeringInformation.setYjly(gwPipeAndGzTzAndGwYyTzInfo.getYjly());
                engineeringInformation.setGqName(gwPipeAndGzTzAndGwYyTzInfo.getGqName());
                engineeringInformation.setTzName(gwPipeAndGzTzAndGwYyTzInfo.getTzName());
            }
            LambdaQueryWrapper<GwPipeYhzqHis> gwPipeYhzqHisLambdaQueryWrapper = new LambdaQueryWrapper<>();
            gwPipeYhzqHisLambdaQueryWrapper.eq(GwPipeYhzqHis::getYhzqGddm, gwPipeAndGzTzAndGwYyTzInfo.getCode())
                    .orderByDesc(GwPipeYhzqHis::getYhzqDate)  // 按 yhzqDate 降序排序
                    .last("LIMIT 1");  // 只取第一条数据
            GwPipeYhzqHis gwPipeYhzqHis = gwPipeYhzqHisService.getOne(gwPipeYhzqHisLambdaQueryWrapper);
            if (Objects.nonNull(gwPipeYhzqHis))
                BeanUtils.copyProperties(gwPipeYhzqHis, maintenanceCycleInformation);
            LambdaQueryWrapper<GwPipeInfo> gwPipeInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
            gwPipeInfoLambdaQueryWrapper.eq(GwPipeInfo::getTheCode, gwPipeAndGzTzAndGwYyTzInfo.getCode());
            GwPipeInfo gwPipeInfo = gwPipeInfoService.getOne(gwPipeInfoLambdaQueryWrapper);
            log.info("管道数据相关信息为:======" + gwPipeInfo);
            if (Objects.nonNull(gwPipeInfo) && Objects.nonNull(gwPipeInfo.getStatInfo())) {
                Gson gson = new Gson();
                GwPipeVo.ManagementInformation managementInformation1 = gson.fromJson(gwPipeInfo.getStatInfo(), GwPipeVo.ManagementInformation.class);
                if (Objects.nonNull(managementInformation1)) {
                    BeanUtils.copyProperties(managementInformation1, managementInformation);
                    BeanUtils.copyProperties(gwPipeAndGzTzAndGwYyTzInfo, managementInformation);
                    BeanUtils.copyProperties(gwPipe, managementInformation);
                    if (Objects.nonNull(managementInformation.getDeptName())) {
                        managementInformation.setDeptName(managementInformation.getDeptName() + "公司");
                    }
                    if (Objects.nonNull(managementInformation.getSyzt())) {
                        managementInformation.setSyzt(getActiveStateName(managementInformation.getSyzt()));
                    }
                    if (Objects.nonNull(managementInformation.getSfqs())) {
                        managementInformation.setSfqs(getSyqsName(managementInformation.getSfqs()));
                    }
                }
            }
            LambdaQueryWrapper<Cctv> cctvLambdaQueryWrapper = new LambdaQueryWrapper<>();
            cctvLambdaQueryWrapper.eq(Cctv::getCctvGddm, gwPipeAndGzTzAndGwYyTzInfo.getCode());
            Cctv cctv = cctvService.getOne(cctvLambdaQueryWrapper);
            if (Objects.nonNull(cctv)) {
                LambdaQueryWrapper<CctvWorkRecord> cctvWorkRecordLambdaQueryWrapper = new LambdaQueryWrapper<>();
                cctvWorkRecordLambdaQueryWrapper.eq(CctvWorkRecord::getUuid, cctv.getCctvWorkId());
                CctvWorkRecord cctvWorkRecord = cctvWorkRecordService.getOne(cctvWorkRecordLambdaQueryWrapper);
                if (Objects.nonNull(cctvWorkRecord)) {
                    LambdaQueryWrapper<ScPlan> scPlanLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    scPlanLambdaQueryWrapper.eq(ScPlan::getUuid, cctvWorkRecord.getPlanUuid());
                    ScPlan scPlan = scPlanService.getOne(scPlanLambdaQueryWrapper);
                    log.info("养护计划为：==============" + scPlan);
                    if (Objects.nonNull(scPlan))
                        BeanUtils.copyProperties(scPlan, cctvDetectionInformation);
                    BeanUtils.copyProperties(cctv, cctvDetectionInformation);
                }
            }
            LambdaQueryWrapper<ScPipeDcHis> scWellDcHisLambdaQueryWrapper = new LambdaQueryWrapper<>();
            scWellDcHisLambdaQueryWrapper.eq(ScPipeDcHis::getDcGddm, gwPipeAndGzTzAndGwYyTzInfo.getCode())
                    .orderByDesc(ScPipeDcHis::getDcDate)  // 按 yhzqDate 降序排序
                    .last("LIMIT 1");  // 只取第一条数据
            ScPipeDcHis scPipeDcHis = scPipeDcHisService.getOne(scWellDcHisLambdaQueryWrapper);
            if (Objects.nonNull(scPipeDcHis)) {
                BeanUtils.copyProperties(scPipeDcHis, investigationInformation);
                investigationInformation.setDcYxzk(getYxzkName(investigationInformation.getDcYxzk()));
                // 定义日期格式
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                if (Objects.nonNull(scPipeDcHis.getDcDate()))
                    investigationInformation.setDcDate(scPipeDcHis.getDcDate().format(formatter));
                ScWorkRecord scWorkRecord = scWorkRecordService.getById(scPipeDcHis.getDcWorkId());
                if (Objects.nonNull(scWorkRecord) && Objects.nonNull(scWorkRecord.getPlanUuid())) {
                    ScPlan scPlan = scPlanService.getById(scWorkRecord.getPlanUuid());
                    investigationInformation.setBianHao(scPlan.getBianHao());
                    investigationInformation.setTzName(scPlan.getTzName());
                }
            }
            LambdaQueryWrapper<GwPipeExt> gwPipeExtLambdaQueryWrapper = new LambdaQueryWrapper<>();
            gwPipeExtLambdaQueryWrapper.eq(GwPipeExt::getCodeExt, gwPipeAndGzTzAndGwYyTzInfo.getCode());
            GwPipeExt gwPipeExt = gwPipeExtService.getOne(gwPipeExtLambdaQueryWrapper);
            if (Objects.nonNull(gwPipeExt)) {
                BeanUtils.copyProperties(gwPipeExt, operationsInformation);
                LambdaQueryWrapper<GwGxgz> gwGxgzLambdaQueryWrapper = new LambdaQueryWrapper<>();
                gwGxgzLambdaQueryWrapper.eq(GwGxgz::getUuid, gwPipeExt.getGxgzPlan());
                GwGxgz gwGxgz = gwGxgzService.getOne(gwGxgzLambdaQueryWrapper);
                if (Objects.nonNull(gwGxgz))
                    BeanUtils.copyProperties(gwGxgz, hiddenInformation);
            }
            basicInformation.setGjType(pipeDiameterType(basicInformation.getGjType()));
            basicInformation.setSyxz(getNatureOfUseName(basicInformation.getSyxz()));
            basicInformation.setJgxs(structuralStyleName(basicInformation.getJgxs()));
            basicInformation.setTsgd(getSpecialFacilitiesName(basicInformation.getTsgd()));
            basicInformation.setGxlx(getPipelineType(basicInformation.getGxlx()));
            basicInformation.setGdcz(pipelineMaterial(basicInformation.getGdcz()));
            gwPipeVo.setBasicInformation(basicInformation);
            gwPipeVo.setMaintenanceCycleInformation(maintenanceCycleInformation);
            gwPipeVo.setCctvDetectionInformation(cctvDetectionInformation);
            gwPipeVo.setHiddenInformation(hiddenInformation);
            gwPipeVo.setEngineeringInformation(engineeringInformation);
            gwPipeVo.setManagementInformation(managementInformation);
            gwPipeVo.setOperationsInformation(operationsInformation);
            gwPipeVo.setInvestigationInformation(investigationInformation);
            convert = GwPipeVoToNewFormatConverter.convert(gwPipeVo);
        }
        return ResponseData.ok(convert);
    }

    @Override
    public ResponseData<List<PointVo>> getAllSewagePipeline() {
        List<PointVo> ysPointVoList = gwPipeMapper.getAllList("ws");
        return ResponseData.ok(ysPointVoList);
    }


    @Override
    public ResponseData<List<PointVo>> getAllConfluentPipeline() {
        List<PointVo> ysPointVoList = gwPipeMapper.getAllList("hl");
        return ResponseData.ok(ysPointVoList);
    }


    @Override
    public ResponseData<List<PointVo>> getAllWellByGzw(String gzw) {
        List<PointVo> jcj = gwWellsMapper.getAllList(gzw);
        return ResponseData.ok(jcj);
    }

    @Override
    public ResponseData<GwWellVoToNewFormatConverter.NewFormat> getWellByCode(String code) {
        GwWellVoToNewFormatConverter.NewFormat newFormat = new GwWellVoToNewFormatConverter.NewFormat();
        GwWellVo gwWellVo = new GwWellVo();
        GwWellAndGzTzAndGwYyTzInfo gwWellAndGzTzAndGwYyTzInfo = gwWellsMapper.getByCode(code);
        GwWellVo.OperationsInformation operationsInformation = new GwWellVo.OperationsInformation();
        GwWellVo.BasicInformation basicInformation = new GwWellVo.BasicInformation();
        GwWellVo.ManagementInformation managementInformation = new GwWellVo.ManagementInformation();
        GwWellVo.EngineeringInformation engineeringInformation = new GwWellVo.EngineeringInformation();
        GwWellVo.InvestigationInformation investigationInformation = new GwWellVo.InvestigationInformation();
        GwWellVo.HiddenInformation hiddenInformation = new GwWellVo.HiddenInformation();
        if (Objects.nonNull(gwWellAndGzTzAndGwYyTzInfo)) {
            BeanUtils.copyProperties(gwWellAndGzTzAndGwYyTzInfo, basicInformation);
            LambdaQueryWrapper<GwWells> gwWellsLambdaQueryWrapper = new LambdaQueryWrapper<>();
            gwWellsLambdaQueryWrapper.eq(GwWells::getCode, gwWellAndGzTzAndGwYyTzInfo.getCode());
            GwWells gwWells = gwWellsMapper.selectOne(gwWellsLambdaQueryWrapper);
            if (Objects.nonNull(gwWells)) {
                BeanUtils.copyProperties(gwWells, engineeringInformation);
                engineeringInformation.setXmbh(gwWellAndGzTzAndGwYyTzInfo.getXmbh());
                engineeringInformation.setYjly(gwWellAndGzTzAndGwYyTzInfo.getYjly());
                engineeringInformation.setGqName(gwWellAndGzTzAndGwYyTzInfo.getGqName());
                engineeringInformation.setTzName(gwWellAndGzTzAndGwYyTzInfo.getTzName());
            }
            LambdaQueryWrapper<GwWellInfo> gwPipeInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
            gwPipeInfoLambdaQueryWrapper.eq(GwWellInfo::getTheCode, gwWellAndGzTzAndGwYyTzInfo.getCode());
            GwWellInfo gwWellInfo = gwWellInfoService.getOne(gwPipeInfoLambdaQueryWrapper);
            log.info("管井数据相关信息为:======" + gwWellInfo);
            if (Objects.nonNull(gwWellInfo) && Objects.nonNull(gwWellInfo.getStatInfo())) {
                Gson gson = new Gson();
                GwWellVo.ManagementInformation managementInformation1 = gson.fromJson(gwWellInfo.getStatInfo(), GwWellVo.ManagementInformation.class);
                if (Objects.nonNull(managementInformation1)) {
                    BeanUtils.copyProperties(managementInformation1, managementInformation);
                    BeanUtils.copyProperties(gwWellAndGzTzAndGwYyTzInfo, managementInformation);
                    BeanUtils.copyProperties(gwWells, managementInformation);
                    if (Objects.nonNull(managementInformation.getDeptName())) {
                        managementInformation.setDeptName(managementInformation.getDeptName() + "公司");
                    }
                    if (Objects.nonNull(managementInformation.getSyzt())) {
                        managementInformation.setSyzt(getActiveStateName(managementInformation.getSyzt()));
                    }
                    if (Objects.nonNull(managementInformation.getSfqs())) {
                        managementInformation.setSfqs(getSyqsName(managementInformation.getSfqs()));
                    }
                }
            }
            LambdaQueryWrapper<ScWellDcHis> scWellDcHisLambdaQueryWrapper = new LambdaQueryWrapper<>();
            scWellDcHisLambdaQueryWrapper.eq(ScWellDcHis::getDcJdm, gwWellAndGzTzAndGwYyTzInfo.getCode())
                    .orderByDesc(ScWellDcHis::getDcDate)  // 按 yhzqDate 降序排序
                    .last("LIMIT 1");  // 只取第一条数据
            ScWellDcHis scWellDcHis = scWellDcHisService.getOne(scWellDcHisLambdaQueryWrapper);
            if (Objects.nonNull(scWellDcHis)) {
                BeanUtils.copyProperties(scWellDcHis, investigationInformation);
                investigationInformation.setDcHw(getSyqsName(investigationInformation.getDcHw()));
                investigationInformation.setDcQs(getSyqsName(investigationInformation.getDcQs()));
                investigationInformation.setDcFd(getDcFdName(investigationInformation.getDcFd()));
                investigationInformation.setDcZg(getSyqsName(investigationInformation.getDcZg()));
                investigationInformation.setDcTb(getSbZkName(investigationInformation.getDcTb()));
                investigationInformation.setDcGk(getSbZkName(investigationInformation.getDcGk()));
                investigationInformation.setDcJb(getSbZkName(investigationInformation.getDcJb()));
                investigationInformation.setDcLc(getSbZkName(investigationInformation.getDcLc()));
                investigationInformation.setDcJd(getDcJdName(investigationInformation.getDcJd()));
                // 定义日期格式
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                if (Objects.nonNull(scWellDcHis.getDcDate()))
                    investigationInformation.setDcDate(scWellDcHis.getDcDate().format(formatter));
                if (Objects.nonNull(scWellDcHis.getDcHwazDate()))
                    investigationInformation.setDcHwazDate(scWellDcHis.getDcHwazDate().format(formatter));
                ScWorkRecord scWorkRecord = scWorkRecordService.getById(scWellDcHis.getDcWorkId());
                if (Objects.nonNull(scWorkRecord) && Objects.nonNull(scWorkRecord.getPlanUuid())) {
                    ScPlan scPlan = scPlanService.getById(scWorkRecord.getPlanUuid());
                    investigationInformation.setBianHao(scPlan.getBianHao());
                    investigationInformation.setTzName(scPlan.getTzName());
                }
            }
            LambdaQueryWrapper<GwWellExt> gwWellExtLambdaQueryWrapper = new LambdaQueryWrapper<>();
            gwWellExtLambdaQueryWrapper.eq(GwWellExt::getCodeExt, gwWellAndGzTzAndGwYyTzInfo.getCode());
            GwWellExt gwWellExt = gwWellExtService.getOne(gwWellExtLambdaQueryWrapper);
            if (Objects.nonNull(gwWellExt)) {
                BeanUtils.copyProperties(gwWellExt, operationsInformation);
                LambdaQueryWrapper<GwGxgz> gwGxgzLambdaQueryWrapper = new LambdaQueryWrapper<>();
                gwGxgzLambdaQueryWrapper.eq(GwGxgz::getUuid, gwWellExt.getGxgzPlan());
                GwGxgz gwGxgz = gwGxgzService.getOne(gwGxgzLambdaQueryWrapper);
                if (Objects.nonNull(gwGxgz))
                    BeanUtils.copyProperties(gwGxgz, hiddenInformation);
            }
            basicInformation.setSyxz(getNatureOfUseName(basicInformation.getSyxz()));
            basicInformation.setGzw(getGzwName(basicInformation.getGzw()));
            gwWellVo.setBasicInformation(basicInformation);
            gwWellVo.setHiddenInformation(hiddenInformation);
            gwWellVo.setEngineeringInformation(engineeringInformation);
            gwWellVo.setManagementInformation(managementInformation);
            gwWellVo.setOperationsInformation(operationsInformation);
            gwWellVo.setInvestigationInformation(investigationInformation);
            newFormat = GwWellVoToNewFormatConverter.convert(gwWellVo);
        }
        return ResponseData.ok(newFormat);
    }
    /**
     * 获取特殊设施名称
     * @return
     */
    /**
     * 获取特殊设施名称
     *
     * @param name 传入的标识字符串
     * @return 对应的特殊设施名称，如果未找到则返回 null
     */
    private String getSpecialFacilitiesName(String name) {
        if ("fts".equals(name)) {
            return "非特殊";
        } else if ("bg".equals(name)) {
            return "边沟";
        } else if ("dhx".equals(name)) {
            return "倒虹吸";
        } else if ("jlj".equals(name)) {
            return "截流井";
        } else if ("pg2".equals(name)) {
            return "双排管";
        } else if ("pg3".equals(name)) {
            return "三排管";
        } else if ("pg4".equals(name)) {
            return "四排管";
        } else if ("uxc".equals(name)) {
            return "U型槽";
        } else if ("ylg".equals(name)) {
            return "压力管";
        } else if ("yskzg".equals(name)) {
            return "雨水口支管";
        } else if ("wxy".equals(name)) {
            return "无下游";
        } else {
            return null;
        }
    }

    /**
     * 获取管线使用性质
     *
     * @param name
     * @return
     */
    private String getNatureOfUseName(String name) {
        if ("ys".equals(name))
            return "雨水";
        else if ("ws".equals(name))
            return "污水";
        else if ("hl".equals(name))
            return "合流";
        else
            return null;
    }

    /**
     * 获取是否权属名称
     *
     * @param name
     * @return
     */
    private String getSyqsName(String name) {
        if ("y".equals(name))
            return "是";
        else if ("n".equals(name))
            return "否";
        else
            return null;
    }

    /**
     * 获取管线调查信息运行状况名称
     *
     * @param name
     * @return
     */
    private String getYxzkName(String name) {
        if ("ct".equals(name))
            return "畅通";
        else
            return name;
    }

    /**
     * 井盖防盗名称
     *
     * @param name
     * @return
     */

    private String getDcFdName(String name) {
        if ("good".equals(name))
            return "完好";
        else if ("bad".equals(name))
            return "破损";
        else
            return null;
    }

    /**
     * 获得管底情况
     *
     * @param name
     * @return
     */

    private String getDcJdName(String name) {
        if ("gj".equals(name))
            return "干净";
        else
            return name;
    }

    /**
     * 获取设备情况
     */
    private String getSbZkName(String name) {
        if ("wh".equals(name))
            return "完好";
        else if ("sh".equals(name)) {
            return "缺损";
        } else if ("qs".equals(name))
            return "缺失";
        else
            return null;
    }

    /**
     * 获取使用状态名称
     *
     * @param name
     * @return
     */
    private String getActiveStateName(String name) {
        if ("using".equals(name))
            return "使用";
        else if ("toaban".equals(name))
            return "拟废";
        else if ("abandon".equals(name))
            return "报废";
        else if ("xcw".equals(name))
            return "现场无";
        else
            return null;
    }

    /**
     * 获取结构形式结构形式 round圆形，rect矩形，tixing
     *
     * @param name
     * @return
     */
    private String structuralStyleName(String name) {
        if ("round".equals(name))
            return "圆形";
        else if ("rect".equals(name))
            return "矩形";
        else if ("tixing".equals(name))
            return "梯形";
        else
            return null;
    }

    /**
     * 获得管线类型
     *
     * @param name
     * @return
     */
    private String getPipelineType(String name) {
        if ("main".equals(name))
            return "主干线";
        else if ("zg".equals(name))
            return "雨水口支管";
        else if ("branch".equals(name))
            return "次干线";
        else if ("zx".equals(name))
            return "支线";
        else if ("hx".equals(name))
            return "户线";
        else
            return null;
    }

    /**
     * 获取管线材质名称
     *
     * @param name
     * @return
     */
    private String pipelineMaterial(String name) {
        if ("hnt".equals(name))
            return "混凝土";
        else if ("zq".equals(name))
            return "砖砌";
        else if ("other".equals(name))
            return "其他";
        else if ("zt".equals(name))
            return "铸铁";

        else
            return name;
    }

    /**
     * 获取管径类型
     *
     * @param name
     * @return
     */
    // mid中型，min小型,big大型,bbb特大型
    private String pipeDiameterType(String name) {
        if ("min".equals(name))
            return "小型";
        else if ("mid".equals(name))
            return "中型";
        else if ("big".equals(name))
            return "大型";
        else if ("bbb".equals(name))
            return "超大型";
        else
            return name;
    }

    /**
     * 获取构筑物名称
     *
     * @param gzw
     * @return
     */
    private String getGzwName(String gzw) {
        if ("jcj".equals(gzw)) {
            return "检查井";
        } else if ("ysk".equals(gzw)) {
            return "雨水口";
        } else if ("phk".equals(gzw)) {
            return "排河口";
        } else if ("dhx".equals(gzw)) {
            return "倒虹吸";
        } else if ("jlj".equals(gzw)) {
            return "截流井";
        } else if ("zj".equals(gzw)) {
            return "闸门井";
        } else if ("dszj".equals(gzw)) {
            return "调水闸井";
        } else if ("dsj".equals(gzw)) {
            return "跌水井";
        } else if ("ylj".equals(gzw)) {
            return "溢流井";
        } else if ("jsk".equals(gzw)) {
            return "进水口";
        } else if ("csk".equals(gzw)) {
            return "出水口";
        } else if ("yb".equals(gzw)) {
            return "雨篦";
        } else if ("anjing".equals(gzw)) {
            return "暗井";
        } else if ("dlbz".equals(gzw)) {
            return "独立篦子";
        } else if ("hfc".equals(gzw)) {
            return "化粪池";
        } else if ("gd".equals(gzw)) {
            return "管点";
        } else if ("other".equals(gzw)) {
            return "其他";
        } else {
            return null;
        }
    }
}
