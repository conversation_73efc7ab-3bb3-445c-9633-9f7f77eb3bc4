package com.hjb.beijin_paishui_data_middleground.service.interactive;

import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.PointVo;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.PopUpNotificationVo;
import com.hjb.beijin_paishui_data_middleground.entity.sub.vo.StationInformationVo;
import com.hjb.beijin_paishui_data_middleground.entity.sub.vo.StationMonitoringVo;
import com.hjb.beijin_paishui_data_middleground.utils.interactive.StationMonitoringVoToNewFormatConverter;

import java.util.List;

/**
 * @Description: 桥区交互接口Service
 * @Author: lvhongen
 * @Date: 2025-05-14 17:08
 * @Version： 1.0
 **/
public interface TheBridgeInteractiveService {
    /**
     * 获取积水监测详细监测信息
     * @param stationCode
     * @return
     */
    ResponseData<StationMonitoringVoToNewFormatConverter.NewFormat> getMonitoringOfAccumulatedWaterInfo
    (String stationCode);

    /***
     * 获取分钟寺-十里河主要管线点位
     * @return
     */
    ResponseData<List<StationInformationVo>> getFzsSlhPoint ();

    /**
     * 获取分钟寺-十里河主要管线液位及充满度
     * @return
     */
    ResponseData<PopUpNotificationVo> getFzsSlhLiquidLevelByCode (String code);

    /**
     * 获取分钟寺-十里河雨情（实时雨量弹窗）
     * @return
     */
    ResponseData<PopUpNotificationVo> getFzsSlhRainCondition ();
}
