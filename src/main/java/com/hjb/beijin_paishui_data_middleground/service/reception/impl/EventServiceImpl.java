package com.hjb.beijin_paishui_data_middleground.service.reception.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.entity.sub.dto.EventTypeCountDTO;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.ps.V3dDrainCompany;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.ps.V3dDrainDepartment;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.ps.V3dDrainOrder;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.ps.V3dDrainOrderReply;
import com.hjb.beijin_paishui_data_middleground.entity.sub.vo.CallOutData;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.event.EventBusinessOfficeUndertakeEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.event.EventEventTypeEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.event.EventFacilityAppealEventEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.event.EventSubCenterUndertakeEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.event.*;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.flood.FloodWaterPlant;
import com.hjb.beijin_paishui_data_middleground.entity.szls.vo.WaterPlantResponseData;
import com.hjb.beijin_paishui_data_middleground.mapper.sub.V3dDrainOrderMapper;
import com.hjb.beijin_paishui_data_middleground.service.reception.EventService;
import com.hjb.beijin_paishui_data_middleground.service.sub.V3dDrainCompanyService;
import com.hjb.beijin_paishui_data_middleground.service.sub.V3dDrainDepartmentService;
import com.hjb.beijin_paishui_data_middleground.service.sub.V3dDrainOrderReplyService;
import com.hjb.beijin_paishui_data_middleground.service.sub.V3dDrainOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.HttpClient;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.ssl.SSLContextBuilder;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import javax.net.ssl.SSLContext;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Type;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 接诉即办
 * @Author: lvhongen
 * @Date: 2025-03-19 22:30
 * @Version： 1.0
 **/
@Service
@Slf4j
public class EventServiceImpl implements EventService {
    @Resource
    private V3dDrainOrderService v3dDrainOrderService;
    @Resource
    private V3dDrainOrderMapper v3dDrainOrderMapper;
    @Resource
    private V3dDrainOrderReplyService v3dDrainOrderReplyService;
    @Resource
    private V3dDrainDepartmentService v3dDrainDepartmentService;
    @Resource
    private V3dDrainCompanyService v3dDrainCompanyService;

    @Override
    public ResponseData<EventEventSource> getEventSource (LocalDateTime now) {
        EventEventSource result = new EventEventSource();
        // 计算近一年的起始时间
        LocalDateTime oneYearAgo = now.minusYears(1);
        // 计算近一年的去年的起始时间
        LocalDateTime twoYearsAgo = oneYearAgo.minusYears(1);

        // 统计近一年数据
        long count12345ThisYear = getEventCount(oneYearAgo, now, "6");
        long count96159ThisYear = getEventCount(oneYearAgo, now, "4");
        log.info(oneYearAgo + "-" + now + "时间内12345数据" + count12345ThisYear + "条");
        log.info(oneYearAgo + "-" + now + "时间内96159数据" + count96159ThisYear + "条");
        // 统计近一年的去年数据
        long count12345LastYear = getEventCount(twoYearsAgo, oneYearAgo.minusDays(1), "6");
        long count96159LastYear = getEventCount(twoYearsAgo, oneYearAgo.minusDays(1), "4");
        log.info(twoYearsAgo + "-" + oneYearAgo + "时间内12345数据" + count12345LastYear + "条");
        log.info(twoYearsAgo + "-" + oneYearAgo + "时间内96159数据" + count96159LastYear + "条");
        // 计算环比
        double ratio12345 = calculateRatio(count12345ThisYear, count12345LastYear);
        double ratio96159 = calculateRatio(count96159ThisYear, count96159LastYear);

        // 封装结果
        result.setQuantity12345(String.valueOf(count12345ThisYear));
        result.setQuantity12345Ratio(String.format("%.2f", ratio12345));
        result.setQuantity96159(String.valueOf(count96159ThisYear));
        result.setQuantity96159Ratio(String.format("%.2f", ratio96159));

        return ResponseData.ok(result);
    }

    private long getEventCount (LocalDateTime start, LocalDateTime end, String channelCode) {
        LambdaQueryWrapper<V3dDrainOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(V3dDrainOrder::getRegisterTime, start)
                .le(V3dDrainOrder::getRegisterTime, end)
                .eq(V3dDrainOrder::getChannelCode, channelCode)
                .eq(V3dDrainOrder::getBolOwnership, 1);
        return v3dDrainOrderService.count(wrapper);
    }

    private double calculateRatio (long thisYear, long lastYear) {
        if (lastYear == 0) return 0.0;
        return (double) (thisYear - lastYear) / lastYear * 100;
    }

    @Override
    public ResponseData<EventEvent> getEvent (LocalDateTime now) {
        EventEvent eventEvent = new EventEvent();
        // 计算近一年的起始时间
        LocalDateTime oneYearAgo = now.minusYears(1);
        // 计算近一年的去年的起始时间
        LocalDateTime twoYearsAgo = oneYearAgo.minusYears(1);
        // 近一年前一天
        LocalDateTime oneYearAgoMinusOneDay = oneYearAgo.minusDays(1);

        // 事件总量：通过查询v_3d_drain_order(排水事件视图)，通过register_time查询当前事件所在年份中的事件总量并且计算同比
        long totalEventsCurrentCount = v3dDrainOrderMapper.countTotalEventsCurrent(oneYearAgo, now);
        long totalEventsCurrentCountTwo = v3dDrainOrderMapper.countTotalEventsCurrent(twoYearsAgo, oneYearAgoMinusOneDay);

        // 近一年的运营范围数
        long operationalScopeOne = v3dDrainOrderMapper.countOperationalScopeCurrent(oneYearAgo, now);
        // 近一年去年的运营范围数
        long operationalScopeTwo = v3dDrainOrderMapper.countOperationalScopeCurrent(twoYearsAgo, oneYearAgoMinusOneDay);

        // 近一年非运营数
        long nonOperationalScopeOne = v3dDrainOrderMapper.countNonOperationalScopeCurrent(oneYearAgo, now);
        // 近一年去年的非运营数
        long nonOperationalScopeTwo = v3dDrainOrderMapper.countNonOperationalScopeCurrent(twoYearsAgo, oneYearAgoMinusOneDay);

        // 近一年确权中数
        long rightsConfirmationOne = v3dDrainOrderMapper.countRightsConfirmationCurrent(oneYearAgo, now);
        // 近一年去年确权中数
        long rightsConfirmationTwo = v3dDrainOrderMapper.countRightsConfirmationLastYear(twoYearsAgo, oneYearAgoMinusOneDay);

        double totalEventsRatio = totalEventsCurrentCountTwo > 0 ? ((double) (totalEventsCurrentCount - totalEventsCurrentCountTwo) / totalEventsCurrentCountTwo * 100) : 0;
        double operationalScopeRatio = operationalScopeTwo > 0 ? ((double) (operationalScopeOne - operationalScopeTwo) / operationalScopeTwo * 100) : 0;
        double nonOperationalScopeRatio = nonOperationalScopeTwo > 0 ? ((double) (nonOperationalScopeOne - nonOperationalScopeTwo) / nonOperationalScopeTwo * 100) : 0;
        double rightsConfirmationRatio = rightsConfirmationTwo > 0 ? ((double) (rightsConfirmationOne - rightsConfirmationTwo) / rightsConfirmationTwo * 100) : 0;

        eventEvent.setTotalEvents(String.valueOf(totalEventsCurrentCount));
        eventEvent.setTotalEventsRatio(String.format("%.2f", totalEventsRatio));
        eventEvent.setOperationalScope(String.valueOf(operationalScopeOne));
        eventEvent.setOperationalScopeRatio(String.format("%.2f", operationalScopeRatio));
        eventEvent.setNonOperationalScope(String.valueOf(nonOperationalScopeOne));
        eventEvent.setNonOperationalScopeRatio(String.format("%.2f", nonOperationalScopeRatio));
        eventEvent.setRightsConfirmation(String.valueOf(rightsConfirmationOne));
        eventEvent.setRightsConfirmationRatio(String.format("%.2f", rightsConfirmationRatio));

        return ResponseData.ok(eventEvent);
    }
//    @Override
//    public ResponseData<EventEvent> getEvent (LocalDateTime now) {
//        EventEvent eventEvent = new EventEvent();
//        // 计算近一年的起始时间
//        LocalDateTime oneYearAgo = now.minusYears(1);
//        // 计算近一年的去年的起始时间
//        LocalDateTime twoYearsAgo = oneYearAgo.minusYears(1);
//        // 事件总量：通过查询v_3d_drain_order(排水事件视图)，查询当前事件所在年份的事件总量，并且计算同比
//        LambdaQueryWrapper<V3dDrainOrder> totalEventsCurrentWrapper = new LambdaQueryWrapper<>();
//        totalEventsCurrentWrapper
//                .ge(V3dDrainOrder::getRegisterTime, oneYearAgo)
//                .le(V3dDrainOrder::getRegisterTime, now);
//        List<V3dDrainOrder> v3dDrainOrderListOne = v3dDrainOrderService.list(totalEventsCurrentWrapper);
//        long totalEventsCurrentCount = v3dDrainOrderService.count(totalEventsCurrentWrapper);
//        LambdaQueryWrapper<V3dDrainOrder> totalEventsLastYearWrapper = new LambdaQueryWrapper<>();
//        totalEventsLastYearWrapper
//                .ge(V3dDrainOrder::getRegisterTime, twoYearsAgo)
//                .le(V3dDrainOrder::getRegisterTime, oneYearAgo.minusDays(1));
//        List<V3dDrainOrder> v3dDrainOrderListTwo = v3dDrainOrderService.list(totalEventsLastYearWrapper);
//        //近一年的总数
//        long totalEventsCurrentCountOne = v3dDrainOrderListOne.size();
//        //近一年去年的总数
//        long totalEventsCurrentCountTwo = v3dDrainOrderListTwo.size();
//        //近一年的运营范围数
//        long operationalScopeOne = 0;
//        //近一年去年的运营范围数
//        long operationalScopeTwo = 0;
//        //近一年非运营数
//        long nonOperationalScopeOne = 0;
//        //近一年去年的非运营数
//        long nonOperationalScopeTwo = 0;
//        //近一年确权中数
//        long rightsConfirmationOne = 0;
//        //近一年去年确权中数
//        long rightsConfirmationTwo = 0;
//        if (v3dDrainOrderListOne.size() > 0) {
//            for (V3dDrainOrder v3dDrainOrder : v3dDrainOrderListOne) {
//                String bolOwnership = v3dDrainOrder.getBolOwnership();
//                if(bolOwnership != null){
//                    if ("1".equals(v3dDrainOrder.getBolOwnership()))
//                        operationalScopeOne++;
//                    if ("0".equals(v3dDrainOrder.getBolOwnership()))
//                        nonOperationalScopeOne++;
//                }else
//                    rightsConfirmationOne++;
//            }
//        }
//        if (v3dDrainOrderListTwo.size() > 0) {
//            for (V3dDrainOrder v3dDrainOrder : v3dDrainOrderListTwo) {
//                String bolOwnership = v3dDrainOrder.getBolOwnership();
//                if(bolOwnership != null){
//                    if ("1".equals(v3dDrainOrder.getBolOwnership()))
//                        operationalScopeOne++;
//                    if ("0".equals(v3dDrainOrder.getBolOwnership()))
//                        nonOperationalScopeOne++;
//                }else
//                    rightsConfirmationOne++;
//            }
//        }
//        double totalEventsRatio = ((double) (totalEventsCurrentCount - totalEventsCurrentCountTwo)
//                / totalEventsCurrentCountTwo * 100) ;
//        double operationalScopeRatio = ((double)
//                (operationalScopeOne - operationalScopeTwo) / operationalScopeTwo * 100);
//        double nonOperationalScopeRatio = ((double)
//                (nonOperationalScopeOne - nonOperationalScopeTwo) / nonOperationalScopeTwo * 100);
//        double rightsConfirmationRatio = ((double)
//                (rightsConfirmationOne - rightsConfirmationTwo) / rightsConfirmationTwo * 100);
//        eventEvent.setTotalEvents(String.valueOf(totalEventsCurrentCountOne));
//        eventEvent.setTotalEventsRatio(String.valueOf(totalEventsRatio));
//        eventEvent.setOperationalScope(String.valueOf(operationalScopeOne));
//        eventEvent.setOperationalScopeRatio(String.valueOf(operationalScopeRatio));
//        eventEvent.setNonOperationalScope(String.valueOf(nonOperationalScopeOne));
//        eventEvent.setNonOperationalScopeRatio(String.valueOf(nonOperationalScopeRatio));
//        eventEvent.setRightsConfirmation(String.valueOf(rightsConfirmationOne));
//        eventEvent.setRightsConfirmationRatio(String.valueOf(rightsConfirmationRatio));
//        return ResponseData.ok(eventEvent);
//    }

    @Override
    public ResponseData<EventEventType> getEventType (LocalDateTime now) {
        EventEventType eventEventType = new EventEventType();

        // 计算时间范围
        LocalDateTime oneYearAgo = now.minusYears(1);
        LocalDateTime twoYearsAgo = oneYearAgo.minusYears(1);

        // 使用优化后的SQL查询
        List<EventTypeCountDTO> eventTypeCounts = v3dDrainOrderMapper.countEventTypesByYearRange(
                now, oneYearAgo, twoYearsAgo);

        // 组装结果数据
        List<EventEventTypeEditDto.EchartsValue> eventTypeValueList = eventTypeCounts.stream()
                .map(dto -> {
                    EventEventTypeEditDto.EchartsValue echartsValue = new EventEventTypeEditDto.EchartsValue();
                    echartsValue.setName(dto.getClassifyOneName());
                    echartsValue.setValue(dto.getCurrentYearCount().intValue());

                    double ringCompare = dto.getLastYearCount() > 0 ?
                            ((double) (dto.getCurrentYearCount() - dto.getLastYearCount()) / dto.getLastYearCount() * 100) : 0;
                    // 使用 DecimalFormat 进行四舍五入保留两位小数
                    DecimalFormat df = new DecimalFormat("0.00");
                    String formattedRingCompare = df.format(ringCompare);
                    echartsValue.setRingCompare(Double.parseDouble(formattedRingCompare));
                    return echartsValue;
                })
                .collect(Collectors.toList());

        eventEventType.setEventTypeValue(eventTypeValueList);
        return ResponseData.ok(eventEventType);
    }

    @Override
    public ResponseData<EventFacilityAppealEvent> getFacilityAppealEvent (LocalDateTime now) {
        EventFacilityAppealEvent eventFacilityAppealEvent = new EventFacilityAppealEvent();

        // 计算近一年的起始时间
        LocalDateTime oneYearAgo = now.minusYears(1);
        List<V3dDrainOrder> currentYearOrders = v3dDrainOrderMapper.getFacilityAppealEventYearOrders
                (oneYearAgo, now);

        // 按类型2编码统计事件数量
        Map<String, Long> classifyTwoCountMap = currentYearOrders.stream()
                .collect(Collectors.groupingBy(V3dDrainOrder::getClassifyTwo, Collectors.counting()));

        // 组装结果数据
        List<EventFacilityAppealEventEditDto.TableData> eventClassificationData = new ArrayList<>();
        long totalCount = classifyTwoCountMap.values().stream().mapToLong(Long::longValue).sum();
        for (Map.Entry<String, Long> entry : classifyTwoCountMap.entrySet()) {
            String classifyTwo = entry.getKey();
            Long count = entry.getValue();
            EventFacilityAppealEventEditDto.TableData tableData = new EventFacilityAppealEventEditDto.TableData();
            // 获取类型2名称
            Optional<V3dDrainOrder> orderOptional = currentYearOrders.stream()
                    .filter(order -> classifyTwo.equals(order.getClassifyTwo()))
                    .findFirst();
            if (orderOptional.isPresent()) {
                tableData.setName(orderOptional.get().getClassifyTwoName());
            }

            tableData.setValue(count.intValue());
            double proportion = totalCount > 0 ? ((double) count / totalCount * 100) : 0;
            // 使用 DecimalFormat 进行四舍五入保留两位小数
            DecimalFormat df = new DecimalFormat("0.00");
            String formattedRingCompare = df.format(proportion);
            tableData.setProportion(Double.parseDouble(formattedRingCompare));
            eventClassificationData.add(tableData);
        }
        eventFacilityAppealEvent.setTotalNum(String.valueOf(totalCount));
        eventFacilityAppealEvent.setEventClassificationData(eventClassificationData);
        return ResponseData.ok(eventFacilityAppealEvent);

    }

    @Override
    public ResponseData<EventSubCenterUndertake> getSubCenterUndertake (LocalDateTime now) {
        EventSubCenterUndertake eventSubCenterUndertake = new EventSubCenterUndertake();
        // 计算近一年的起始时间
        LocalDateTime oneYearAgo = now.minusYears(1);
        // 查询今年的排水事件数据
        LambdaQueryWrapper<V3dDrainOrder> drainOrderWrapper = new LambdaQueryWrapper<>();
        drainOrderWrapper.select(V3dDrainOrder::getOrderSn)
                .ge(V3dDrainOrder::getRegisterTime, oneYearAgo)
                .le(V3dDrainOrder::getRegisterTime, now);
        List<V3dDrainOrder> v3dDrainOrders = v3dDrainOrderService.list(drainOrderWrapper);
        // 提取今年事件的orderSn
        List<String> orderSns = v3dDrainOrders.stream()
                .map(V3dDrainOrder::getOrderSn)
                .collect(Collectors.toList());

        // 根据orderSn关联查询处置记录数据
        LambdaQueryWrapper<V3dDrainOrderReply> replyWrapper = new LambdaQueryWrapper<>();
        replyWrapper.select(V3dDrainOrderReply::getReplyerCompanyName)
                .in(V3dDrainOrderReply::getOrderSn, orderSns);
        List<V3dDrainOrderReply> v3dDrainOrderReplies = v3dDrainOrderReplyService.list(replyWrapper);

        // 按处置单位进行分类统计
        Map<String, Long> replyerCompanyCountMap = v3dDrainOrderReplies.stream()
                .collect(Collectors.groupingBy(V3dDrainOrderReply::getReplyerCompanyName, Collectors.counting()));

        // 查询V3dDrainDepartment表中的name值
        LambdaQueryWrapper<V3dDrainCompany> companyLambdaQueryWrapper = new LambdaQueryWrapper<>();
        companyLambdaQueryWrapper.select(V3dDrainCompany::getName);
        List<V3dDrainCompany> v3dDrainCompanyList = v3dDrainCompanyService.list(companyLambdaQueryWrapper);
        List<String> v3dDrainCompanys = v3dDrainCompanyList.stream()
                .map(V3dDrainCompany::getName)
                .collect(Collectors.toList());

        // 统计V3dDrainDepartment表中name值与v_3d_drain_order表中replyer_company_name相同的数量
        Map<String, Long> sameNameCountMap = new HashMap<>();
        for (String v3dDrainCompanyName : v3dDrainCompanys) {
            long count = replyerCompanyCountMap.entrySet().stream()
                    .filter(entry -> entry.getKey().equals(v3dDrainCompanyName))
                    .mapToLong(Map.Entry::getValue)
                    .sum();
            sameNameCountMap.put(v3dDrainCompanyName, count);
        }

        // 组装图表数据
        EventSubCenterUndertakeEditDto.EchartsValue echartsValue =
                new EventSubCenterUndertakeEditDto.EchartsValue();
        List<String> xValue = new ArrayList<>();
        List<Integer> seriesValue = new ArrayList<>();
        for (Map.Entry<String, Long> entry : sameNameCountMap.entrySet()) {
            xValue.add(entry.getKey());
            seriesValue.add(entry.getValue().intValue());
        }
        echartsValue.setXValue(xValue);
        echartsValue.setSeriesValue(seriesValue);

        // 这里假设将echartsValue设置到eventBusinessOfficeUndertake中
        eventSubCenterUndertake.setSubCenterUndertakeValue(echartsValue);

        return ResponseData.ok(eventSubCenterUndertake);
    }

    @Override
    public ResponseData<EventBusinessOfficeUndertake> getBusinessOfficeUndertake (LocalDateTime now) {
        EventBusinessOfficeUndertake eventBusinessOfficeUndertake = new EventBusinessOfficeUndertake();
        // 计算近一年的起始时间
        LocalDateTime oneYearAgo = now.minusYears(1);
        // 查询今年的排水事件数据
        LambdaQueryWrapper<V3dDrainOrder> drainOrderWrapper = new LambdaQueryWrapper<>();
        drainOrderWrapper.select(V3dDrainOrder::getOrderSn)
                .ge(V3dDrainOrder::getRegisterTime, oneYearAgo)
                .le(V3dDrainOrder::getRegisterTime, now);
        List<V3dDrainOrder> v3dDrainOrders = v3dDrainOrderService.list(drainOrderWrapper);
        // 提取今年事件的orderSn
        List<String> orderSns = v3dDrainOrders.stream()
                .map(V3dDrainOrder::getOrderSn)
                .collect(Collectors.toList());

        // 根据orderSn关联查询处置记录数据
        LambdaQueryWrapper<V3dDrainOrderReply> replyWrapper = new LambdaQueryWrapper<>();
        replyWrapper.select(V3dDrainOrderReply::getReplyerCompanyName)
                .in(V3dDrainOrderReply::getOrderSn, orderSns);
        List<V3dDrainOrderReply> v3dDrainOrderReplies = v3dDrainOrderReplyService.list(replyWrapper);

        // 按处置单位进行分类统计
        Map<String, Long> replyerCompanyCountMap = v3dDrainOrderReplies.stream()
                .collect(Collectors.groupingBy(V3dDrainOrderReply::getReplyerCompanyName, Collectors.counting()));

        // 查询V3dDrainDepartment表中的name值
        LambdaQueryWrapper<V3dDrainDepartment> departmentWrapper = new LambdaQueryWrapper<>();
        departmentWrapper.select(V3dDrainDepartment::getName);
        List<V3dDrainDepartment> v3dDrainDepartments = v3dDrainDepartmentService.list(departmentWrapper);
        List<String> departmentNames = v3dDrainDepartments.stream()
                .map(V3dDrainDepartment::getName)
                .collect(Collectors.toList());

        // 统计V3dDrainDepartment表中name值与v_3d_drain_order表中replyer_company_name相同的数量
        Map<String, Long> sameNameCountMap = new HashMap<>();
        for (String departmentName : departmentNames) {
            long count = replyerCompanyCountMap.entrySet().stream()
                    .filter(entry -> entry.getKey().equals(departmentName))
                    .mapToLong(Map.Entry::getValue)
                    .sum();
            sameNameCountMap.put(departmentName, count);
        }

        // 组装图表数据
        EventBusinessOfficeUndertakeEditDto.EchartsValue echartsValue =
                new EventBusinessOfficeUndertakeEditDto.EchartsValue();
        List<String> xValue = new ArrayList<>();
        List<Integer> seriesValue = new ArrayList<>();
        for (Map.Entry<String, Long> entry : sameNameCountMap.entrySet()) {
            xValue.add(entry.getKey());
            seriesValue.add(entry.getValue().intValue());
        }
        echartsValue.setXValue(xValue);
        echartsValue.setSeriesValue(seriesValue);

        // 这里假设将echartsValue设置到eventBusinessOfficeUndertake中
        eventBusinessOfficeUndertake.setBusinessOfficeUndertakeValue(echartsValue);

        return ResponseData.ok(eventBusinessOfficeUndertake);
    }

    @SuppressWarnings("deprecation")
    public RestTemplate getRestTemplate () {
        try {
            // 创建SSLContext并信任所有证书
            SSLContext sslContext = SSLContextBuilder.create()
                    .loadTrustMaterial(null, (arg0, arg1) -> true)
                    .build();

            // 创建不验证主机名的SSLConnectionSocketFactory
            SSLConnectionSocketFactory socketFactory = new SSLConnectionSocketFactory(
                    sslContext,
                    NoopHostnameVerifier.INSTANCE
            );

            // 创建HttpClient并设置SSLConnectionSocketFactory
            HttpClient httpClient = org.apache.http.impl.client.HttpClients.custom()
                    .setSSLSocketFactory(socketFactory)
                    .build();

            // 使用配置好的HttpClient创建RestTemplate
            return new RestTemplate(new org.springframework.http.client.HttpComponentsClientHttpRequestFactory(httpClient));
        } catch (Exception e) {
            log.error("创建RestTemplate时发生异常", e);
            throw new RuntimeException("创建RestTemplate失败", e);
        }
    }

    public ResponseData<EventCallOut> getCallOut () {
        EventCallOut eventCallOut = new EventCallOut();
        RestTemplate restTemplate = getRestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        // 获取最近一周的时间
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime oneWeekAgo = now.minusWeeks(1);
        LocalDateTime twoWeeksAgo = oneWeekAgo.minusWeeks(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String currentStartDate = oneWeekAgo.format(formatter);
        String currentEndDate = now.format(formatter);
        String lastStartDate = twoWeeksAgo.format(formatter);
        String lastEndDate = oneWeekAgo.format(formatter);
        // 构建当前周请求URL
        UriComponentsBuilder currentBuilder = UriComponentsBuilder.fromHttpUrl("https://10.22.2.234/report/third/api/call/inAndOutStat")
                .queryParam("startDate", currentStartDate)
                .queryParam("endDate", currentEndDate)
                .queryParam("orgi", "ukewo");
        String currentUrl = currentBuilder.encode().toUriString();
        currentUrl = currentUrl.replace("%20", " ");
        log.info("Current Week URL: {}", currentUrl);

        // 构建上周请求URL
        UriComponentsBuilder lastBuilder = UriComponentsBuilder.fromHttpUrl("https://10.22.2.234/report/third/api/call/inAndOutStat")
                .queryParam("startDate", lastStartDate)
                .queryParam("endDate", lastEndDate)
                .queryParam("orgi", "ukewo");
        String lastUrl = lastBuilder.encode().toUriString();
        lastUrl = lastUrl.replace("%20", " ");
        log.info("Last Week URL: {}", lastUrl);

        try {
            // 发送当前周GET请求并获取响应
            ResponseEntity<String> currentResponseEntity = restTemplate.getForEntity(currentUrl, String.class);
            if (currentResponseEntity.getStatusCode().is2xxSuccessful()) {
                String currentJsonResponse = currentResponseEntity.getBody();
                log.info("当前周请求成功，返回水厂数据为" + currentJsonResponse);
                Gson gson = new Gson();
                Type type = new TypeToken<ResponseData<CallOutData>>() {
                }.getType();
                ResponseData<CallOutData> currentResponse = gson.fromJson(currentJsonResponse, type);
                CallOutData currentCallOutData = currentResponse.getData();

                // 发送上周GET请求并获取响应
                ResponseEntity<String> lastResponseEntity = restTemplate.getForEntity(lastUrl, String.class);
                if (lastResponseEntity.getStatusCode().is2xxSuccessful()) {
                    String lastJsonResponse = lastResponseEntity.getBody();
                    log.info("上周请求成功，返回水厂数据为" + lastJsonResponse);
                    ResponseData<CallOutData> lastResponse = gson.fromJson(lastJsonResponse, type);
                    CallOutData lastCallOutData = lastResponse.getData();

                    // 设置当前周数据
                    eventCallOut.setInComingCall(String.valueOf(currentCallOutData.getCallInTotalCount()));
                    eventCallOut.setOutComingCall(String.valueOf(currentCallOutData.getCallOutTotalCount()));
                    eventCallOut.setAnswerRate(currentCallOutData.getCallInAnswerRate());

                    // 计算环比
                    double inComingCallChainRatio = (double) (currentCallOutData.getCallInTotalCount() - lastCallOutData.getCallInTotalCount()) / lastCallOutData.getCallInTotalCount();
                    double outComingCallChainRatio = (double) (currentCallOutData.getCallOutTotalCount() - lastCallOutData.getCallOutTotalCount()) / lastCallOutData.getCallOutTotalCount();
                    eventCallOut.setInComingCallRatio(String.format("%.2f", inComingCallChainRatio * 100));
                    eventCallOut.setOutComingCallRatio(String.format("%.2f", outComingCallChainRatio * 100));

                    return ResponseData.ok(eventCallOut);
                } else {
                    log.error("上周请求失败，状态码: {}", lastResponseEntity.getStatusCode());
                    return ResponseData.error("上周请求失败，状态码: " + lastResponseEntity.getStatusCode());
                }
            } else {
                log.error("当前周请求失败，状态码: {}", currentResponseEntity.getStatusCode());
                return ResponseData.error("当前周请求失败，状态码: " + currentResponseEntity.getStatusCode());
            }
        } catch (Exception e) {
            log.error("请求发生异常", e);
            return ResponseData.error("请求发生异常");
        }
    }
}
