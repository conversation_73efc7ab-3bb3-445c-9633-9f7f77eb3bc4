package com.hjb.beijin_paishui_data_middleground.service.reception.impl;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.synthetical.SyntheticalFloodControlEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical.SyntheticalComplaintResolution;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical.SyntheticalFloodControl;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical.SyntheticalPipelineOperation;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical.SyntheticalPipelinePlate;
import com.hjb.beijin_paishui_data_middleground.mapper.sub.*;
import com.hjb.beijin_paishui_data_middleground.service.reception.SyntheticalService;
import com.hjb.beijin_paishui_data_middleground.service.sub.*;
import com.hjb.beijin_paishui_data_middleground.service.szls.SyntheticalPipelinePlateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @Description: 综合态势
 * @Author: lvhongen
 * @Date: 2025-03-19 22:30
 * @Version： 1.0
 **/
@Service
@Slf4j
public class SyntheticalServiceImpl implements SyntheticalService {
    @Resource
    private GwPipeMapper gwPipeMapper;
    @Resource
    private GwPipeYhzqHisMapper gwPipeYhzqHisMapper;
    @Resource
    private GwWellsMapper gwWellsMapper;
    @Resource
    private V3dDrainOrderMapper v3dDrainOrderMapper;
    @Resource
    private SharePodInfoMapper sharePodInfoMapper;
    @Resource
    private SyntheticalPipelinePlateService syntheticalPipelinePlateService;


    /**
     * 初步完结
     *
     * @return
     */
    @Override
    public ResponseData<SyntheticalPipelineOperation> getPipelineOperation () {
        SyntheticalPipelineOperation syntheticalPipelineOperation = new SyntheticalPipelineOperation();
        Double totalPipelineLength = gwPipeMapper.getTotalPipelineLength();
        Double rainwaterPipelineLength = gwPipeMapper.getRainwaterPipelineLength();
        Double sewagePipelineLength = gwPipeMapper.getSewagePipelineLength();
        log.info("管道总长：" + totalPipelineLength + "--雨水管道总长：" + rainwaterPipelineLength + "--污水管道总长："
                + sewagePipelineLength);
//        List<GwWells> gwWellList = gwPipeMapper.getList();
        syntheticalPipelineOperation = gwWellsMapper.getListByGwGcTzCode();
        syntheticalPipelineOperation.setTotalPipelineLength(String.valueOf(totalPipelineLength));
        syntheticalPipelineOperation.setRainwaterPipeline(String.valueOf(rainwaterPipelineLength));
        syntheticalPipelineOperation.setSewagePipeline(String.valueOf(sewagePipelineLength));
        return ResponseData.ok(syntheticalPipelineOperation);
    }


    /**
     * 初步完结
     *
     * @return
     */
    @Override
    public ResponseData<SyntheticalPipelinePlate> getPipelinePlate () {
        SyntheticalPipelinePlate reportedData = syntheticalPipelinePlateService.getReportedData();
        SyntheticalPipelinePlate syntheticalPipelinePlate = new SyntheticalPipelinePlate();
        Double amountOfCuring = gwPipeYhzqHisMapper.getAmountOfCuring();
        Double detectingMileage = gwPipeYhzqHisMapper.getDetectingMileage();
        String inspectionMileage = reportedData.getInspectionMileage();
        syntheticalPipelinePlate.setAmountOfCuring(amountOfCuring == null ? "0" : amountOfCuring.toString());
        syntheticalPipelinePlate.setDetectingMileage(detectingMileage == null ? "0" : detectingMileage.toString());
        syntheticalPipelinePlate.setInspectionMileage(inspectionMileage);
        return ResponseData.ok(syntheticalPipelinePlate);
    }

    @Override
    public ResponseData<SyntheticalComplaintResolution> getComplaintResolution (LocalDateTime now) {
        SyntheticalComplaintResolution syntheticalComplaintResolution = new SyntheticalComplaintResolution();
        // 计算近一年的起始时间
        LocalDateTime oneYearAgo = now.minusYears(1);
        // 计算近一年的去年的起始时间
        LocalDateTime twoYearsAgo = oneYearAgo.minusYears(1);
        // 近一年前一天
        LocalDateTime oneYearAgoMinusOneDay = oneYearAgo.minusDays(1);
        // 事件总量：通过查询v_3d_drain_order(排水事件视图)，通过register_time查询当前事件所在年份中bolOwnership（是否权属）为1的事件总量并且计算同比
        long totalEventsCurrentCount = v3dDrainOrderMapper.countTotalEventsCurrent(oneYearAgo, now);
        long totalEventsCurrentCountTwo = v3dDrainOrderMapper.countTotalEventsCurrent(twoYearsAgo, oneYearAgoMinusOneDay);
        // 近一年的运营范围数
        long operationalScopeOne = v3dDrainOrderMapper.countOperationalScopeCurrent(oneYearAgo, now);
        // 近一年非运营数
        long nonOperationalScopeOne = v3dDrainOrderMapper.countNonOperationalScopeCurrent(oneYearAgo, now);
        // 近一年确权中数
        long rightsConfirmationOne = v3dDrainOrderMapper.countRightsConfirmationCurrent(oneYearAgo, now);
        double totalEventsRatio = totalEventsCurrentCountTwo > 0 ?
                ((double) (totalEventsCurrentCount - totalEventsCurrentCountTwo) / totalEventsCurrentCountTwo * 100) : 0;

        syntheticalComplaintResolution.setTotalEvents(String.valueOf(totalEventsCurrentCount));
        syntheticalComplaintResolution.setTotalEventsRatio(String.format("%.2f", totalEventsRatio));
        syntheticalComplaintResolution.setOperationalScope(String.valueOf(operationalScopeOne));
        syntheticalComplaintResolution.setNonOperationalScope(String.valueOf(nonOperationalScopeOne));
        syntheticalComplaintResolution.setRightsConfirmation(String.valueOf(rightsConfirmationOne));
        return ResponseData.ok(syntheticalComplaintResolution);
    }
    private SyntheticalFloodControlEditDto.EchartsValue
    getSharePodInfoCountByMonth (List<Map<String, Object>> listByOrderByMonthCountnow, List<Map<String, Object>>
            listByOrderByMonthCountLast) {
        SyntheticalFloodControlEditDto.EchartsValue echartsValue = new SyntheticalFloodControlEditDto.EchartsValue();
        // 初始化 xValues 为 1 - 12 月
        List<String> xValues = new ArrayList<>();
        for (int i = 1; i <= 12; i++) {
            xValues.add(String.valueOf(i));
        }
        echartsValue.setXValues(xValues);
        // 初始化 nowYearValues 和 oldYearValues 为 12 个 0
        List<Integer> nowYearValues = new ArrayList<>(Collections.nCopies(12, 0));
        List<Integer> oldYearValues = new ArrayList<>(Collections.nCopies(12, 0));
        // 填充今年的数据
        for (Map<String, Object> map : listByOrderByMonthCountnow) {
            int month = ((BigDecimal) map.get("MONTH")).intValue();
            int count = ((BigDecimal) map.get("COUNT")).intValue();
            nowYearValues.set(month - 1, count);
        }

        // 填充去年的数据
        for (Map<String, Object> map : listByOrderByMonthCountLast) {
            int month = ((BigDecimal) map.get("MONTH")).intValue();
            int count = ((BigDecimal) map.get("COUNT")).intValue();
            oldYearValues.set(month - 1, count);
        }
        echartsValue.setNowYearValues(nowYearValues);
        echartsValue.setOldYearValues(oldYearValues);
        return echartsValue;
    }
}
