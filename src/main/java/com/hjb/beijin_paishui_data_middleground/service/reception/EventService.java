package com.hjb.beijin_paishui_data_middleground.service.reception;

import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.event.*;

import java.io.UnsupportedEncodingException;
import java.time.LocalDateTime;

/**
 * @Description: 接诉即办service
 * @Author: lvhongen
 * @Date: 2025-03-19 22:27
 * @Version： 1.0
 **/
public interface EventService {
    /**
     * 事件来源
     * @return
     */
    ResponseData<EventEventSource> getEventSource (LocalDateTime now);

    /**
     * 事件
     * @return
     */
    ResponseData<EventEvent> getEvent (LocalDateTime now);

    /**
     *事件类型
     * @param now 当前时间
     * @return
     */
    ResponseData<EventEventType> getEventType (LocalDateTime now);

    /**
     * 设施诉求类事件
     * @param now 当前时间
     * @return
     */
    ResponseData<EventFacilityAppealEvent> getFacilityAppealEvent (LocalDateTime now);

    /**
     * 按办理类型各分中心承办量数据
     * @param now
     * @return
     */
    ResponseData<EventSubCenterUndertake> getSubCenterUndertake (LocalDateTime now);

    /**
     * 按办理类型业务部承办量
     * @param now
     * @return
     */
    ResponseData<EventBusinessOfficeUndertake> getBusinessOfficeUndertake (LocalDateTime now);

    /**
     * 呼叫数据
     * @return
     */
    ResponseData<EventCallOut> getCallOut ();
}
