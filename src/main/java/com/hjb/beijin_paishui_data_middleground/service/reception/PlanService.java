package com.hjb.beijin_paishui_data_middleground.service.reception;

import com.hjb.beijin_paishui_data_middleground.entity.sub.dto.PlanPageRequest;
import com.hjb.beijin_paishui_data_middleground.entity.sub.vo.PlanPageResponse;

import java.util.List;

public interface PlanService {

    String getToken();

    PlanPageResponse selectPlanMsgList(PlanPageRequest planPageRequest);

    List<Integer> waterDepthByPlanId(String planId, String dateTime);

}
