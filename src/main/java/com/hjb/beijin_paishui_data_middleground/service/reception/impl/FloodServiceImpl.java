package com.hjb.beijin_paishui_data_middleground.service.reception.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.entity.json.JsonRainGaugeInformationResponse;
import com.hjb.beijin_paishui_data_middleground.entity.json.JsonStationMonitoringVoResponse;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.fx.*;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw.GwPipe;
import com.hjb.beijin_paishui_data_middleground.entity.sub.vo.RainGaugeInformationVo;
import com.hjb.beijin_paishui_data_middleground.entity.sub.vo.StationInfo;
import com.hjb.beijin_paishui_data_middleground.entity.sub.vo.StationInformationVo;
import com.hjb.beijin_paishui_data_middleground.entity.sub.vo.StationMonitoringVo;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.flood.FloodPipeNetworkMonitorEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.flood.FloodRecessedBridgeEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.flood.FloodRiskPointEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.flood.FloodWaterPlantEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.flood.*;
import com.hjb.beijin_paishui_data_middleground.entity.szls.vo.WaterPlantResponseData;
import com.hjb.beijin_paishui_data_middleground.service.reception.FloodService;
import com.hjb.beijin_paishui_data_middleground.service.sub.*;
import com.hjb.beijin_paishui_data_middleground.service.szls.StationGjService;
import com.hjb.beijin_paishui_data_middleground.utils.ExcelUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 防汛调度
 * @Author: lvhongen
 * @Date: 2025-03-19 22:28
 * @Version： 1.0
 **/
@Service
@Slf4j
public class FloodServiceImpl implements FloodService {
    @Resource
    private ShareBrgInfoService shareBrgInfoService;
    @Resource
    private ShareRiskPointInfoService shareRiskPointInfoService;
    @Resource
    private StationTypeDataService stationTypeDataService;
    @Resource
    private StationGjService stationGjService;
    @Resource
    private GwPipeService gwPipeService;
    @Resource
    private ShareCarInfoService shareCarInfoService;


    @Override
    public ResponseData<FloodRealTimeRainfall> getRealTimeRainfall () {
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> entity = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.getForEntity(
                "http://*********:23486/deviot-open-api/api/external/" +
                        "findNewsStationListByStationType?token=75693db267804dbdb7127e06064821ce&stationType=9",
                String.class, entity);
        if (response.getStatusCode().is2xxSuccessful()) {
            String jsonResponse = response.getBody();
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                // 这里假设 JsonStationMonitoringVoResponse 是正确的响应类，根据实际情况修改
                JsonRainGaugeInformationResponse jsonStationMonitoringVoResponse =
                        objectMapper.readValue(jsonResponse, JsonRainGaugeInformationResponse.class);
                // 这里假设 StationMonitoringVo 是正确的实体类，根据实际情况修改
                List<RainGaugeInformationVo> detail = jsonStationMonitoringVoResponse.getDetail();
                log.info(detail.toString());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    @Override
    public ResponseData<FloodRecessedBridge> getRecessedBridge (String searchAdministrative, String searchCompany) {
        // 构建查询条件
        LambdaQueryWrapper<ShareBrgInfo> queryWrapper = new LambdaQueryWrapper<>();
        if (searchAdministrative != null && !searchAdministrative.isEmpty()) {
            queryWrapper.eq(ShareBrgInfo::getAdnm, searchAdministrative);
        }
        if (searchCompany != null && !searchCompany.isEmpty()) {
            queryWrapper.eq(ShareBrgInfo::getComnm, searchCompany);
        }
        // 查询下凹桥信息
        List<ShareBrgInfo> shareBrgInfoList = shareBrgInfoService.list(queryWrapper);
        // 封装结果
        FloodRecessedBridge floodRecessedBridge = new FloodRecessedBridge();
        floodRecessedBridge.setSearchAdministrative(searchAdministrative);
        floodRecessedBridge.setSearchCompany(searchCompany);
        List<FloodRecessedBridgeEditDto.TableData> tableDataList = new ArrayList<>();
        for (ShareBrgInfo shareBrgInfo : shareBrgInfoList) {
            FloodRecessedBridgeEditDto.TableData tableData = new FloodRecessedBridgeEditDto.TableData();
            tableData.setName(shareBrgInfo.getBrName());
            tableData.setAffiliatedCompany(shareBrgInfo.getComnm());
            tableData.setAffiliatedAdministrative(shareBrgInfo.getAdnm());
            tableDataList.add(tableData);
        }
        floodRecessedBridge.setRecessedBridgeData(tableDataList);
        // 返回响应数据
        return ResponseData.ok(floodRecessedBridge);
    }

    @Override
    public ResponseData getRecessedBridgeQueryList () {
        Map<String, List<String>> maps = shareBrgInfoService.selectDistinctByComnmAndAdnm();
        return ResponseData.ok(maps);
    }

    @Override
    public ResponseData getRiskPointQueryList () {
        Map<String, List<String>> maps = shareRiskPointInfoService.selectDistinctByComnmAndAdnm();
        return ResponseData.ok(maps);
    }

    @Override
    public ResponseData getEmergencyUnitQueryList () {
        Map<String, List<String>> maps = shareCarInfoService.getEmergencyUnitQueryList();
        return ResponseData.ok(maps);
    }

    @Override
    public ResponseData<FloodRiskPoint> getRiskPoint (String searchAdministrative, String searchCompany) {
        // 构建查询条件
        LambdaQueryWrapper<ShareRiskPointInfo> queryWrapper = new LambdaQueryWrapper<>();
        if (searchAdministrative != null && !searchAdministrative.isEmpty()) {
            queryWrapper.eq(ShareRiskPointInfo::getAdnm, searchAdministrative);
        }
        if (searchCompany != null && !searchCompany.isEmpty()) {
            queryWrapper.eq(ShareRiskPointInfo::getComnm, searchCompany);
        }
        // 查询风险点信息
        List<ShareRiskPointInfo> shareRiskPointInfoList = shareRiskPointInfoService.list(queryWrapper);
        FloodRiskPoint floodRiskPoint = new FloodRiskPoint();
        floodRiskPoint.setSearchAdministrative(searchAdministrative);
        floodRiskPoint.setSearchCompany(searchCompany);
        List<FloodRiskPointEditDto.TableData> tableDataList = new ArrayList<>();
        for (ShareRiskPointInfo shareRiskPointInfo : shareRiskPointInfoList) {
            FloodRiskPointEditDto.TableData tableData = new FloodRiskPointEditDto.TableData();
            tableData.setName(shareRiskPointInfo.getName());
            tableData.setAffiliatedCompany(shareRiskPointInfo.getComnm());
            tableData.setAffiliatedAdministrative(shareRiskPointInfo.getAdnm());
            tableDataList.add(tableData);
        }
        floodRiskPoint.setRiskPointData(tableDataList);
        // 返回响应数据
        return ResponseData.ok(floodRiskPoint);
    }

    // 定义响应数据类
    @Override
    public ResponseData<FloodWaterPlant> getWaterPlant () {
        FloodWaterPlant floodWaterPlant = new FloodWaterPlant();
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new org.springframework.http.HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        // 假设这些是请求体中的数据，构建成List
        List<String> requestBodyList = Arrays.asList(
                "QH0829220220919", "QH0827220220919", "QH0000120221101",
                "QH0001020220919", "QH0002420220919", "QH0020120220919",
                "QHE0000520220823", "QHE0008020220823", "QHE0000920230601",
                "BXH0000120220606", "BXH0002220220606", "JXQ0007620220515",
                "JXQ0007420220515", "TXQ0009120220515", "GAT0000320220517",
                "GAT0006120220517", "GAT0000720230530", "GBD0116620220506",
                "GBD1918220220608", "GBD0001120230531", "DFZ0046420220525",
                "DFZ0000220220728", "XH1503620230313", "XH1507520230313",
                "XHM0000520230601", "HF0008820230313", "H0013420230313",
                "WJC0002320221013", "WJC0158520221013", "LGQ0054020221020",
                "LGQ0002420221020"
        );
        ObjectMapper objectMapper = new ObjectMapper();
        String requestBodyJson = "";
        try {
            requestBodyJson = objectMapper.writeValueAsString(requestBodyList);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        HttpEntity<String> entity = new HttpEntity<>(requestBodyJson, headers);
        String url = "http://10.2.110.37:18086/api/query_rt";
        String jsonResponse = restTemplate.postForObject(url, entity, String.class);
        log.info("请求成功，返回水厂数据为" + jsonResponse);
        Gson gson = new Gson();
        Type type = new TypeToken<WaterPlantResponseData>() {
        }.getType();
        WaterPlantResponseData response = gson.fromJson(jsonResponse, type);
        List<FloodWaterPlantEditDto.DetailData> list = new ArrayList<>();
        // 定义水厂信息映射表，根据你的数据结构填充
        Map<String, Map<String, String>> waterPlantMap = new HashMap<>();
        Map<String, String> map1 = new HashMap<>();
        map1.put("FrontLevel", "QH0827220220919");
        map1.put("InstantaneousLift", "QH0829220220919");
        map1.put("WhetherToCross", "QH0000120221101");
        map1.put("OverflowLevel", "5.80");
        waterPlantMap.put("清河40", map1);
        // 将后面的都改为相同的map类型
        Map<String, String> map2 = new HashMap<>();
        map2.put("FrontLevel", "QH0001020220919");
        map2.put("InstantaneousLift", "QH0002420220919");
        map2.put("WhetherToCross", "QH0020120220919");
        map2.put("OverflowLevel", "5.80");
        waterPlantMap.put("清河15", map2);
        Map<String, String> map3 = new HashMap<>();
        map3.put("FrontLevel", "QHE0000520220823");
        map3.put("InstantaneousLift", "QHE0008020220823");
        map3.put("WhetherToCross", "QHE0000920230601");
        map3.put("OverflowLevel", "8.00");
        waterPlantMap.put("清河二", map3);
        Map<String, String> map4 = new HashMap<>();
        map4.put("FrontLevel", "BXH0000120220606");
        map4.put("InstantaneousLift", "BXH0002220220606");
        map4.put("WhetherToCross", "无跨越");
        map4.put("OverflowLevel", "3.70");
        waterPlantMap.put("北小河", map4);
        Map<String, String> map5 = new HashMap<>();
        map5.put("FrontLevel", "JXQ0007620220515");
        map5.put("InstantaneousLift", "JXQ0007420220515");
        map5.put("WhetherToCross", "TXQ0009120220515");
        map5.put("OverflowLevel", "5.30");
        waterPlantMap.put("酒仙桥", map5);
        Map<String, String> map6 = new HashMap<>();
        map6.put("FrontLevel", "GAT0000320220517");
        map6.put("InstantaneousLift", "GAT0006120220517");
        map6.put("WhetherToCross", "GAT0000720230530");
        map6.put("OverflowLevel", "9.40");
        waterPlantMap.put("高安屯", map6);
        Map<String, String> map7 = new HashMap<>();
        map7.put("FrontLevel", "GBD0116620220506");
        map7.put("InstantaneousLift", "GBD1918220220608");
        map7.put("WhetherToCross", "GBD0001120230531");
        map7.put("OverflowLevel", "3.20");
        waterPlantMap.put("高碑店", map7);
        Map<String, String> map8 = new HashMap<>();
        map8.put("FrontLevel", "DFZ0046420220525");
        map8.put("InstantaneousLift", "DFZ0000220220728");
        map8.put("WhetherToCross", "无跨越");
        map8.put("OverflowLevel", "7.10");
        waterPlantMap.put("定福庄", map8);
        Map<String, String> map9 = new HashMap<>();
        map9.put("FrontLevel", "XH1503620230313");
        map9.put("InstantaneousLift", "XH1507520230313");
        map9.put("WhetherToCross", "XHM0000520230601");
        map9.put("OverflowLevel", "5.10");
        waterPlantMap.put("小红门", map9);
        Map<String, String> map10 = new HashMap<>();
        map10.put("FrontLevel", "HF0008820230313");
        map10.put("InstantaneousLift", "H0013420230313");
        map10.put("WhetherToCross", "无跨越");
        map10.put("OverflowLevel", "10.60");
        waterPlantMap.put("槐房", map10);
        Map<String, String> map11 = new HashMap<>();
        map11.put("FrontLevel", "WJC00023202211013");
        map11.put("InstantaneousLift", "WJC01585202211013");
        map11.put("WhetherToCross", "无跨越");
        map11.put("OverflowLevel", "5.62");
        waterPlantMap.put("吴家村", map11);
        Map<String, String> map12 = new HashMap<>();
        map12.put("FrontLevel", "LGQ00540202211020");
        map12.put("InstantaneousLift", "LGQ00024202211020");
        map12.put("WhetherToCross", "无监测");
        map12.put("OverflowLevel", "11.00");
        waterPlantMap.put("卢沟桥", map12);
        // 遍历响应数据，填充到对应的水厂信息中
        for (Map.Entry<String, Map<String, String>> stringMapEntry : waterPlantMap.entrySet()) {
            FloodWaterPlantEditDto.DetailData tableData = new FloodWaterPlantEditDto.DetailData();
            tableData.setWaterPlantName(stringMapEntry.getKey());
            Map<String, String> stringMapEntryValue = stringMapEntry.getValue();
            tableData.setOverflowLevel(stringMapEntryValue.get("OverflowLevel"));
            log.info(stringMapEntryValue.toString());
            if (response != null && response.getData() != null) {
                for (Map.Entry<String, Object> entry : response.getData().entrySet()) {
                    String key = entry.getKey();
                    log.info("键:" + key);
                    Object value = entry.getValue();
                    log.info("值:" + value);
                    if (Objects.isNull(value))
                        continue;
                    String valueStr = null;
                    if (value instanceof Double) {
                        valueStr = String.valueOf((Double) value);
                    }
                    log.info("键: {}, 值的类型: {}, 值: {}", key, value.getClass().getName(), value);
                    if (stringMapEntryValue.get("FrontLevel").equals(key)) {
                        tableData.setFrontLevel(valueStr);
                    }
                    if (stringMapEntryValue.get("InstantaneousLift").equals(key)) {
                        tableData.setInstantaneousLift(valueStr);
                    }
                    if (stringMapEntryValue.get("WhetherToCross").equals(key)) {
                        tableData.setWhetherToCross(valueStr);
                    } else {
                        tableData.setWhetherToCross(stringMapEntryValue.get("WhetherToCross"));
                    }
                }
            } else {
                log.error("响应数据或数据部分为 null");
            }
            list.add(tableData);
        }
        log.info("最终得到的list为" + list.toString());
        long overflow = 0;
        long stride = 0;
        for (FloodWaterPlantEditDto.DetailData detailData : list) {
            String frontLevel = detailData.getFrontLevel();
            String overflowLevel = detailData.getOverflowLevel();
            if (frontLevel != null && overflowLevel != null) {
                try {
                    if (Double.parseDouble(frontLevel) > Double.parseDouble(overflowLevel)) {
                        overflow++;
                    }
                } catch (NumberFormatException e) {
                    // 处理数字格式异常，例如记录日志
                    System.err.println("解析 FrontLevel 或 OverflowLevel 时出现数字格式异常，数据：" + detailData);
                    e.printStackTrace();
                }
            }
            if ("0".equals(detailData.getWhetherToCross())) {
                stride++;
            }
        }
        floodWaterPlant.setOverflow(String.valueOf(overflow));
        floodWaterPlant.setStride(String.valueOf(stride));
        floodWaterPlant.setWaterPlantDetailsData(list);
        return ResponseData.ok(floodWaterPlant);
    }

    @Override
    public ResponseData<FloodEmergencyUnit> getEmergencyUnit (String searchType, String searchCompany, String searchUnit) {
        return null;
    }

    @Override
    public ResponseData uploadStation (MultipartFile file) {
        List<StationTypeData> list = ExcelUtils.insertExcelDataToDatabase(file);
        stationTypeDataService.clearTable();
        stationTypeDataService.saveBatch(list);
        return ResponseData.ok("处理完成");
    }

    private final RestTemplate restTemplate = new RestTemplate();

    @Override
    public ResponseData<FloodPipeNetworkMonitorEditDto> getPipeNetworkMonitor (String type) {
        FloodPipeNetworkMonitorEditDto floodPipeNetworkMonitorEditDto = new FloodPipeNetworkMonitorEditDto();
        double rainwaterPipeLevelLow50 = 0;
        double rainwaterPipeLevel50To80 = 0;
        double rainwaterPipeLevel80To100 = 0;
        List<FloodPipeNetworkMonitorEditDto.TableData> tableDataList = new ArrayList<>();
        List<StationTypeData> stationTypeDataList = stationTypeDataService.list();
        Map<String, List<Integer>> typeMap = new HashMap<>();
        List<Integer> wsTypeList = new ArrayList<>();
        wsTypeList.add(18);
        wsTypeList.add(21);
        typeMap.put("wsgd", wsTypeList);
        List<Integer> ysTypeList = new ArrayList<>();
        ysTypeList.add(19);
        ysTypeList.add(82);
        ysTypeList.add(87);
        typeMap.put("ysgd", ysTypeList);
        List<Integer> cqylTypeList = new ArrayList<>();
        cqylTypeList.add(80);
        cqylTypeList.add(21);
        cqylTypeList.add(85);
        typeMap.put("cqyl", cqylTypeList);
        List<Integer> phkTypeList = new ArrayList<>();
        phkTypeList.add(85);
        typeMap.put("phk", phkTypeList);
        List<Integer> integerList = typeMap.get(type);
        List<StationGj> stationGjList = stationGjService.list();
        if (integerList != null && integerList.size() > 0) {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> entity = new HttpEntity<>(headers);
            // 批量处理网络请求
            List<ResponseEntity<String>> responses = integerList.stream()
                    .map(stationType -> restTemplate.getForEntity(
                            "http://*********:23486/deviot-open-api/api/external/" +
                                    "findNewsStationListByStationType?token=75693db267804dbdb7127e06064821ce&stationType=" + stationType,
                            String.class, entity))
                    .collect(Collectors.toList());
            // 使用 Set 来跟踪已经处理过的 facilityCode
            Set<String> processedFacilityCodes = new HashSet<>();
            for (ResponseEntity<String> response : responses) {
                if (response.getStatusCode().is2xxSuccessful()) {
                    String jsonResponse = response.getBody();
                    try {
                        ObjectMapper objectMapper = new ObjectMapper();
                        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                        JsonStationMonitoringVoResponse jsonStationMonitoringVoResponse =
                                objectMapper.readValue(jsonResponse, JsonStationMonitoringVoResponse.class);
                        List<StationMonitoringVo> stationInfoList = jsonStationMonitoringVoResponse.getDetail();
                        Map<String, StationTypeData> stationTypeDataMap = stationTypeDataList.stream()
                                .collect(Collectors.toMap(StationTypeData::getCode, s -> s));
                        for (StationMonitoringVo stationInformationVo : stationInfoList) {
                            if (stationInformationVo.getStationName().contains("(废)")) {
                                continue;
                            }
                            boolean is = "cqyl".equals(type) ^ stationTypeDataMap.containsKey(stationInformationVo.getStationCode());
                            if (!is) {
                                FloodPipeNetworkMonitorEditDto.TableData tableData = new FloodPipeNetworkMonitorEditDto.TableData();
                                tableData.setWaterPlateName(stationInformationVo.getStationName());
                                tableData.setFacilityCode(stationInformationVo.getStationCode());
                                tableData.setLiquidLevel(stationInformationVo.getLiquidLevel());
                                tableData.setVelocityOfFlow(stationInformationVo.getFlowVel());
                                // 检查 facilityCode 是否已经存在
                                if (!processedFacilityCodes.contains(tableData.getFacilityCode())) {
                                    double gj = 0;
                                    for (StationGj stationGj : stationGjList) {
                                        if (stationGj.getStationCode().equals(stationInformationVo.getStationCode())) {
                                            gj = stationGj.getGj();
                                            break;
                                        }
                                    }
                                    List<GwPipe> list1 = gwPipeService.list(new LambdaQueryWrapper<GwPipe>().eq(GwPipe::getXyjdm, stationInformationVo.getStationCode()));
                                    if (list1.size() > 0) {
                                        gj = Double.parseDouble(list1.get(0).getGk());
                                        if (list1.size() > 1) {
                                            List<GwPipe> list2 = gwPipeService.list(new LambdaQueryWrapper<GwPipe>().eq(GwPipe::getSyjdm, stationInformationVo.getStationCode()));
                                            if (list2.size() > 0) {
                                                gj = Double.parseDouble(list2.get(0).getGk());
                                            }
                                        }
                                    }
                                    log.info("测站编码为" + stationInformationVo.getStationCode() + "--------管径为" + gj);
                                    double v = 0;
                                    if (Objects.nonNull(stationInformationVo.getLiquidLevel())) {
                                        v = Double.parseDouble(stationInformationVo.getLiquidLevel()) * 1000.00 / gj * 100;
                                    }
                                    log.info("测站编码为" + stationInformationVo.getStationCode() + "--------充满度为" + v);
                                    if (v < 50) {
                                        rainwaterPipeLevelLow50++;
                                    }
                                    if (v >= 50 && v <= 80) {
                                        rainwaterPipeLevel50To80++;
                                    }
                                    if (v >= 80) {
                                        rainwaterPipeLevel80To100++;
                                    }
                                    tableData.setFillingDegree(String.format("%.2f", v));
                                    tableDataList.add(tableData);
                                    processedFacilityCodes.add(tableData.getFacilityCode());
                                }
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                } else {
                    System.out.println("请求失败，状态码: " + response.getStatusCode());
                }
            }
        }
        floodPipeNetworkMonitorEditDto.setStationDetailsData(tableDataList);
        floodPipeNetworkMonitorEditDto.setRainwaterPipeLevelLow50(String.format("%.2f", rainwaterPipeLevelLow50 /
                tableDataList.size() * 100));
        floodPipeNetworkMonitorEditDto.setRainwaterPipeLevel50To80(String.format("%.2f", rainwaterPipeLevel50To80 /
                tableDataList.size() * 100));
        floodPipeNetworkMonitorEditDto.setRainwaterPipeLevel80To100(String.format("%.2f", rainwaterPipeLevel80To100 /
                tableDataList.size() * 100));
        log.info(floodPipeNetworkMonitorEditDto.toString());
        return ResponseData.ok(floodPipeNetworkMonitorEditDto);
    }
}
