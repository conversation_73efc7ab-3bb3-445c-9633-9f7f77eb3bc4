package com.hjb.beijin_paishui_data_middleground.service.reception;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.entity.sub.vo.PipeNetworkDefectLibraryVo;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.drain.DrainOperationalRiskEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description: 排水设施Service
 * @Author: lvhongen
 * @Date: 2025-03-19 22:27
 * @Version： 1.0
 **/
public interface DrainService {
    /**
     * 管网及设施数据
     *
     * @return
     */
    ResponseData<DrainPipeNetworkAndFacilities> getPipeNetworkAndFacilities ();

    /**
     * 附属设施数据
     *
     * @return
     */
    ResponseData<DrainAncillaryFacilities> getAncillaryFacilities ();

    /**
     * 特殊设施
     *
     * @return
     */
    ResponseData<DrainSpecialFacility> getSpecialFacility ();

    /**
     * 混接
     *
     * @return
     */
    ResponseData<DrainHybridConnection> getHybridConnection ();

    /**
     * 周边施工
     * @return
     */
    ResponseData<DrainPeripheralConstruction> getPeripheralConstruction ();

    /**
     * 应急事件数据
     * @return
     */
    ResponseData<DrainEmergencyRepair> getEmergencyRepair (LocalDateTime now);

    /**
     * 运行风险
     * @return
     */
    ResponseData<DrainOperationalRiskEditDto> getOperationalRisk (LocalDateTime now);

    /**
     * 设施消隐
     * @return
     */
    ResponseData<DrainFacilityBlanking> getFacilityBlanking (LocalDateTime now);

    /**
     * 管网检测
     * @return
     */
    ResponseData<DrainPipeNetworkTesting> getPipeNetworkTesting ();

    /**
     * 养护生产数据
     * @param now
     * @return
     */
    ResponseData<DrainMaintenanceProduction> getMaintenanceProduction (LocalDate now);

    /**
     * 管网缺陷
     * @return
     */
    DrainPipelineDefect getDrainPipelineDefect();

    /**
     * 管道缺陷库
     * @return
     */
    Page<PipeNetworkDefectLibraryVo> getPipeNetworkDefectLibrary(String type, String degree, Integer currentPage, Integer pageSize);
}
