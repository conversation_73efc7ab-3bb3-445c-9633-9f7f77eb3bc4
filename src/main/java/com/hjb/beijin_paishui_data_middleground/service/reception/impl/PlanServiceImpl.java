package com.hjb.beijin_paishui_data_middleground.service.reception.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hjb.beijin_paishui_data_middleground.common.advice.CustomException;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.sub.dto.PlanPageRequest;
import com.hjb.beijin_paishui_data_middleground.entity.sub.vo.PlanPageResponse;
import com.hjb.beijin_paishui_data_middleground.service.reception.PlanService;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class PlanServiceImpl implements PlanService {

    public static final String BASE_URL = "http://10.15.100.102/bpflood-outside/";

    public static final String CLIENT_ID = "6c6e8214490a43aa96737cb9bda68adc";

    public static final String CLIENT_SECRET = "edQ@0945f1E61a47C7";

    @Override
    public String getToken() {
        Map<String, String> map = new HashMap<>();
        map.put("clientId", CLIENT_ID);
        map.put("clientSecret", CLIENT_SECRET);
        String url = BASE_URL + "external/oauth/token";
        HttpResponse response = HttpRequest.post(url)
                .body(JsonUtil.toJsonStr(map))
                .execute();
        String responseBody = response.body();
        System.out.println("接口响应内容：" + responseBody);

        JSONObject respJson = JSONUtil.parseObj(responseBody);
        Integer code = respJson.getInt("code");
        if (code == 200) {
            JSONObject result = respJson.getJSONObject("result");
            return result.getStr("accessToken");
        }
        throw new CustomException("获取token失败，错误信息:" + respJson.getStr("info"));
    }

    @Override
    public PlanPageResponse selectPlanMsgList(PlanPageRequest planPageRequest) {
        String url = BASE_URL + "external/selectPlanMsgList";
        HttpResponse response = HttpRequest.post(url)
                .body(JsonUtil.toJsonStr(planPageRequest))
                .header("token", getToken())
                .header("clientId", CLIENT_ID)
                .execute();
        String responseBody = response.body();
        System.out.println("接口响应内容：" + responseBody);

        JSONObject respJson = JSONUtil.parseObj(responseBody);
        Integer code = respJson.getInt("code");
        if (code == 200) {
            JSONObject result = respJson.getJSONObject("result");
            return result.toBean(PlanPageResponse.class);
        }
        throw new CustomException("获取失败，错误信息:" + respJson.getStr("info"));
    }

    @Override
    public List<Integer> waterDepthByPlanId(String planId, String dateTime) {
        Map<String, Object> map = new HashMap<>();
        map.put("planId", planId);
        map.put("dateTime", dateTime);
        String url = BASE_URL + "external/waterDepthByPlanId";
        HttpResponse response = HttpRequest.post(url)
                .body(JsonUtil.toJsonStr(map))
                .header("token", getToken())
                .header("clientId", CLIENT_ID)
                .execute();
        String responseBody = response.body();
        System.out.println("接口响应内容：" + responseBody);
        JSONObject respJson = JSONUtil.parseObj(responseBody);
        Integer code = respJson.getInt("code");
        if (code == 200) {
            Object result = respJson.get("result");
            return JSONUtil.parseArray(result).toList(Integer.class);
        }
        return Collections.emptyList();
    }


}
