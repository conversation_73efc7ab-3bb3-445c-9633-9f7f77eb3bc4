package com.hjb.beijin_paishui_data_middleground.service.reception.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.entity.sub.po.gw.*;
import com.hjb.beijin_paishui_data_middleground.entity.sub.vo.GwWbfxVo;
import com.hjb.beijin_paishui_data_middleground.entity.sub.vo.GxGxgzVo;
import com.hjb.beijin_paishui_data_middleground.entity.sub.vo.PipeNetworkDefectLibraryVo;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.drain.*;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.*;
import com.hjb.beijin_paishui_data_middleground.mapper.sub.SysResMapper;
import com.hjb.beijin_paishui_data_middleground.mapper.sub.*;
import com.hjb.beijin_paishui_data_middleground.service.reception.DrainService;
import com.hjb.beijin_paishui_data_middleground.service.sub.*;
import com.hjb.beijin_paishui_data_middleground.service.szls.DrainAncillaryFacilitiesService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 排水设施
 * @Author: lvhongen
 * @Date: 2025-03-19 22:28
 * @Version： 1.0
 **/
@Service
@Slf4j
public class DrainServiceImpl implements DrainService {
    @Resource
    private GwBengzhanService gwBengzhanService;
    @Resource
    private GwTzJljService gwTzJljService;
    @Resource
    private GwTzDhxService gwTzDhxService;
    @Resource
    private GwTzDsjService gwTzDsjService;
    @Resource
    private GwHjService gwHjService;
    @Resource
    private GwZbsgService gwZbsgService;
    @Resource
    private YjsjService yjsjService;
    @Resource
    private GwTzChuchouService gwTzChuchouService;
    @Resource
    private GwWbfxService gwWbfxService;
    @Resource
    private GwGxgzService gwGxgzService;
    @Resource
    private AXzjService aXzjService;
    @Resource
    private PipeNetworkTestingMapper pipeNetworkTestingMapper;
    @Resource
    private GwPipeMapper gwPipeMapper;
    @Resource
    private GwWellsMapper gwWellsMapper;
    @Resource
    private DrainAncillaryFacilitiesService drainAncillaryFacilitiesService;
    @Resource
    private ScPlanMapper scPlanMapper;

    /**
     * 初步完结
     *
     * @return
     */
    @Override
    public ResponseData<DrainPipeNetworkAndFacilities> getPipeNetworkAndFacilities() {
        DrainPipeNetworkAndFacilities drainPipeNetworkAndFacilities = new DrainPipeNetworkAndFacilities();
        Double totalPipelineLength = gwPipeMapper.getTotalPipelineLength();
        Double rainwaterPipelineLength = gwPipeMapper.getRainwaterPipelineLength();
        Double sewagePipelineLength = gwPipeMapper.getSewagePipelineLength();
        Double combinedSewerPipeLength = gwPipeMapper.getCombinedSewerPipelineLength();
        drainPipeNetworkAndFacilities.setTotalMileage(String.valueOf(totalPipelineLength));
        drainPipeNetworkAndFacilities.setRainwater(String.valueOf(rainwaterPipelineLength));
        drainPipeNetworkAndFacilities.setSewage(String.valueOf(sewagePipelineLength));
        drainPipeNetworkAndFacilities.setCombinedSewer(String.valueOf(combinedSewerPipeLength));
        return ResponseData.ok(drainPipeNetworkAndFacilities);
    }

    /**
     * 初步完结
     *
     * @return
     */
    @Override
    public ResponseData<DrainAncillaryFacilities> getAncillaryFacilities() {
        DrainAncillaryFacilities reportedData = drainAncillaryFacilitiesService.getReportedData();
        DrainAncillaryFacilities drainAncillaryFacilities = new DrainAncillaryFacilities();
        drainAncillaryFacilities = gwWellsMapper.getListByGwGcTzCodeSeachSyxzCount();
        long ysbz = gwBengzhanService.count(new LambdaQueryWrapper<GwBengzhan>().eq(
                GwBengzhan::getLeixing, "雨水"
        ));
        long yskcount = gwWellsMapper.getCountByGwz("ysk", "y");
        long phkSqs = gwWellsMapper.getCountByGwz("phk", "y");
        long phkFqs = gwWellsMapper.getCountByGwz("phk", "n");
        drainAncillaryFacilities.setRainwaterPumpingStation(String.valueOf(ysbz));
        drainAncillaryFacilities.setRainwaterInlet(String.valueOf(yskcount));
        drainAncillaryFacilities.setDrainageOutletAuth(String.valueOf(phkSqs));
        drainAncillaryFacilities.setDrainageOutletUnauth(String.valueOf(phkFqs));
        drainAncillaryFacilities.setInitialPond(reportedData.getInitialPond());
        drainAncillaryFacilities.setStoragePond(reportedData.getStoragePond());
        return ResponseData.ok(drainAncillaryFacilities);
    }

    /**
     * 初步完成
     *
     * @return
     */
    @Override
    public ResponseData<DrainSpecialFacility> getSpecialFacility() {
        DrainSpecialFacility drainSpecialFacility = new DrainSpecialFacility();
        drainSpecialFacility.setCatchPit(String.valueOf(gwTzJljService.count(
                new LambdaQueryWrapper<GwTzJlj>().eq(GwTzJlj::getSfqs, "y")
        )));
        drainSpecialFacility.setSluicShaft(String.valueOf(gwTzDsjService.count()));
        drainSpecialFacility.setInvertedSiphon(String.valueOf(gwTzDhxService.count()));
        drainSpecialFacility.setDeodorizer(String.valueOf(gwTzChuchouService.count(new LambdaQueryWrapper<
                GwTzChuchou>().eq(GwTzChuchou::getStatus, "sy"))));
        return ResponseData.ok(drainSpecialFacility);
    }

    /**
     * 初步完成
     *
     * @return
     */
    @Override
    public ResponseData<DrainHybridConnection> getHybridConnection() {
        DrainHybridConnection drainHybridConnection = new DrainHybridConnection();
        List<GwHj> gwHjList = gwHjService.list();
        drainHybridConnection.setTotalEvents(String.valueOf(gwHjList.size()));
        if (gwHjList.size() == 0)
            return ResponseData.ok(drainHybridConnection);
        double completionDegree = 0;
        completionDegree = (double) gwHjService.list(new LambdaQueryWrapper<GwHj>().eq(
                GwHj::getSfwc, "y"
        )).size() / gwHjList.size();
        drainHybridConnection.setCompletionDegree(String.format("%.2f", completionDegree * 100));
        List<DrainHybridConnectionEditDto.EchartsValue> administrativeProportionValue = new ArrayList<>();
        // 用于存储每个行政区的数量
        Map<String, Integer> districtCountMap = new HashMap<>();
        // 从行政区字典表中获取所有数据
        List<AXzj> aXzjList = aXzjService.list();
        // 将编码和名称的映射关系存储到map中
        Map<String, String> codeNameMap = aXzjList.stream()
                .collect(Collectors.toMap(AXzj::getCode, AXzj::getName));

        // 遍历 gwHjList，统计每个行政区的数量，并使用名称替换编码
        for (GwHj gwHj : gwHjList) {
            String districtCode = gwHj.getXzjid();
            String districtName = codeNameMap.getOrDefault(districtCode, "未知");
            districtCountMap.put(districtName, districtCountMap.getOrDefault(districtName, 0) + 1);
        }
        // 将统计结果存储到 administrativeProportionValue 列表中
        for (Map.Entry<String, Integer> entry : districtCountMap.entrySet()) {
            DrainHybridConnectionEditDto.EchartsValue echartsValue = new DrainHybridConnectionEditDto.EchartsValue();
            echartsValue.setName(entry.getKey());
            echartsValue.setValue(String.valueOf(entry.getValue()));
            administrativeProportionValue.add(echartsValue);
        }
        drainHybridConnection.setAdministrativeProportionValue(administrativeProportionValue);
        return ResponseData.ok(drainHybridConnection);
    }

    /**
     * 初步完结
     *
     * @return
     */
    @Override
    public ResponseData<DrainPeripheralConstruction> getPeripheralConstruction() {
        DrainPeripheralConstruction drainPeripheralConstruction = new DrainPeripheralConstruction();
        List<DrainPeripheralConstructionEditDto.EchartsValue> itemTypeProportionValue = new ArrayList<>();
        List<DrainPeripheralConstructionEditDto.EchartsValue> engineeringProgressProportionValue = new ArrayList<>();

        // 查询所有周边施工信息
        List<GwZbsg> gwZbsgList = gwZbsgService.list();
        // 统计项目类型的数量
        Map<String, Integer> itemTypeCountMap = new HashMap<>();
        for (GwZbsg gwZbsg : gwZbsgList) {
            String itemType = gwZbsg.getType();
            if (itemType != null) {
                if (itemType.contains(",")) {
                    // 如果项目类型包含逗号，拆分并分别统计
                    String[] types = itemType.split(",");
                    for (String type : types) {
                        type = type.trim();
                        matchingType(type, itemTypeCountMap);
                    }
                } else {
                    matchingType(itemType, itemTypeCountMap);
                }
            }
        }
        // 直接存储项目类型数量到列表
        for (Map.Entry<String, Integer> entry : itemTypeCountMap.entrySet()) {
            String itemType = entry.getKey();
            int count = entry.getValue();
            DrainPeripheralConstructionEditDto.EchartsValue value = new DrainPeripheralConstructionEditDto.EchartsValue();
            value.setName(itemType);
            value.setValue(String.valueOf(count));
            itemTypeProportionValue.add(value);
        }

        // 统计工程进展的数量
        Map<String, Integer> engineeringProgressCountMap = new HashMap<>();
        for (GwZbsg gwZbsg : gwZbsgList) {
            String engineeringProgress = gwZbsg.getGczt();
            if (engineeringProgressCountMap != null)
                matchProjectProgress(engineeringProgress, engineeringProgressCountMap);
        }

        // 直接存储工程进展数量到列表
        for (Map.Entry<String, Integer> entry : engineeringProgressCountMap.entrySet()) {
            String engineeringProgress = entry.getKey();
            int count = entry.getValue();
            DrainPeripheralConstructionEditDto.EchartsValue value = new DrainPeripheralConstructionEditDto.EchartsValue();
            value.setName(engineeringProgress);
            value.setValue(String.valueOf(count));
            engineeringProgressProportionValue.add(value);
        }

        // 设置数量列表到实体类
        drainPeripheralConstruction.setItemTypeProportionValue(itemTypeProportionValue);
        drainPeripheralConstruction.setEngineeringProgressProportionValue(engineeringProgressProportionValue);

        return ResponseData.ok(drainPeripheralConstruction);
    }

    /**
     * 匹配类型
     */
    private void matchingType(String type, Map<String, Integer> itemTypeCountMap) {
        if ("ditie".equals(type))
            itemTypeCountMap.put("地铁", itemTypeCountMap.getOrDefault("地铁", 0) + 1);
        if ("dlwj".equals(type))
            itemTypeCountMap.put("道路维修", itemTypeCountMap.getOrDefault("道路维修", 0) + 1);
        if ("dldx".equals(type))
            itemTypeCountMap.put("道路大修", itemTypeCountMap.getOrDefault("道路大修", 0) + 1);
        if ("fangjian".equals(type))
            itemTypeCountMap.put("房建", itemTypeCountMap.getOrDefault("房建", 0) + 1);
        if ("hedao".equals(type))
            itemTypeCountMap.put("河道", itemTypeCountMap.getOrDefault("河道", 0) + 1);
    }

    /**
     * 匹配工程进度
     *
     * @param engineeringProgress
     * @param itemTypeCountMap
     */
    private void matchProjectProgress(String engineeringProgress, Map<String, Integer> itemTypeCountMap) {
        if ("canceled".equals(engineeringProgress))
            itemTypeCountMap.put("项目取消", itemTypeCountMap.getOrDefault("项目取消", 0) + 1);
        if ("notStart".equals(engineeringProgress))
            itemTypeCountMap.put("未开工", itemTypeCountMap.getOrDefault("未开工", 0) + 1);
        if ("working".equals(engineeringProgress))
            itemTypeCountMap.put("施工中", itemTypeCountMap.getOrDefault("施工中", 0) + 1);
        if ("finish".equals(engineeringProgress))
            itemTypeCountMap.put("已完成", itemTypeCountMap.getOrDefault("已完成", 0) + 1);
        if ("stoping".equals(engineeringProgress))
            itemTypeCountMap.put("停滞中", itemTypeCountMap.getOrDefault("停滞中", 0) + 1);
    }

    /**
     * 初步完结
     *
     * @return
     */
    @Override
    public ResponseData<DrainEmergencyRepair> getEmergencyRepair(LocalDateTime now) {
        DrainEmergencyRepair drainEmergencyRepair = new DrainEmergencyRepair();
        // 计算近一年的起始时间
        LocalDateTime oneYearAgo = now.minusYears(1);

        // 查询所有已确权的应急事件
        List<Yjsj> yjsjList = yjsjService.list(
                new LambdaQueryWrapper<Yjsj>()
                        .eq(Yjsj::getIsQuanshu, "y")
                        .ge(Yjsj::getFssj, oneYearAgo)
                        .lt(Yjsj::getFssj, now)
        );

        // 按事件类型和月份统计，增加空值检查
        Map<String, Map<Integer, Long>> typeMonthCounts = yjsjList.stream()
                .filter(y -> y.getJblx() != null && y.getFssj() != null) // 过滤掉 jblx 或 fssj 为 null 的元素
                .collect(Collectors.groupingBy(
                        Yjsj::getJblx,
                        Collectors.groupingBy(
                                y -> y.getFssj().getMonthValue(),
                                Collectors.counting()
                        )
                ));

        // 生成各类型的统计数据
        Map<String, DrainEmergencyRepairEditDto.BaseData> dataMap = new HashMap<>();

        // 全部事件
        dataMap.put("qvanbu", buildBaseData(
                yjsjList.size(),
                typeMonthCounts.values().stream()
                        .flatMap(map -> map.entrySet().stream())
                        .collect(Collectors.groupingBy(Map.Entry::getKey, Collectors.summingLong(Map.Entry::getValue)))
        ));
        // 抢险事件
        dataMap.put("qiangxian", buildBaseData(
                getTotalFromMap(typeMonthCounts.getOrDefault("qiangxian", Collections.emptyMap())),
                typeMonthCounts.getOrDefault("qiangxian", Collections.emptyMap())
        ));
        // 抢修事件
        dataMap.put("qiangxiu", buildBaseData(
                getTotalFromMap(typeMonthCounts.getOrDefault("qiangxiu", Collections.emptyMap())),
                typeMonthCounts.getOrDefault("qiangxiu", Collections.emptyMap())
        ));
        // 防汛事件
        dataMap.put("fx", buildBaseData(
                getTotalFromMap(typeMonthCounts.getOrDefault("fx", Collections.emptyMap())),
                typeMonthCounts.getOrDefault("fx", Collections.emptyMap())
        ));

        // 设置结果到实体类，增加非空检查
        DrainEmergencyRepairEditDto.BaseData allData = dataMap.get("qvanbu");
        if (allData != null) {
            drainEmergencyRepair.setEmergencyRepairAllValue(allData);
        }
        DrainEmergencyRepairEditDto.BaseData rescueData = dataMap.get("qiangxian");
        if (rescueData != null) {
            drainEmergencyRepair.setEmergencyRescueValue(rescueData);
        }
        DrainEmergencyRepairEditDto.BaseData repairData = dataMap.get("qiangxiu");
        if (repairData != null) {
            drainEmergencyRepair.setRushToRepairValue(repairData);
        }
        DrainEmergencyRepairEditDto.BaseData floodData = dataMap.get("fx");
        if (floodData != null) {
            drainEmergencyRepair.setFloodPreventionValue(floodData);
        }

        return ResponseData.ok(drainEmergencyRepair);
    }

    private long getTotalFromMap(Map<Integer, Long> monthCounts) {
        return monthCounts.values().stream().mapToLong(Long::longValue).sum();
    }

    private DrainEmergencyRepairEditDto.BaseData buildBaseData(long total, Map<Integer, Long> monthCounts) {
        DrainEmergencyRepairEditDto.BaseData baseData = new DrainEmergencyRepairEditDto.BaseData();
        baseData.setEmergencyRepair(String.valueOf(total));

        DrainEmergencyRepairEditDto.EchartsValue echartsValue = new DrainEmergencyRepairEditDto.EchartsValue();
        List<String> xValues = new ArrayList<>();
        List<Integer> seriesValues = new ArrayList<>();

        for (int month = 1; month <= 12; month++) {
            xValues.add(month + "月");
            seriesValues.add(monthCounts.getOrDefault(month, 0L).intValue());
        }
        echartsValue.setXValue(xValues);
        echartsValue.setSeriesValue(seriesValues);
        baseData.setEchartsValue(echartsValue);
        return baseData;
    }

    //运行风险start (初步完成)
    private static final Map<String, String> RISK_TYPE_MAP = new HashMap<>();

    static {
        RISK_TYPE_MAP.put("zhanya", "占压");
        RISK_TYPE_MAP.put("quanzhan", "圈占");
        RISK_TYPE_MAP.put("chuanzao", "穿凿");
        RISK_TYPE_MAP.put("gas", "气体超标");
        RISK_TYPE_MAP.put("sj", "缩进");
        RISK_TYPE_MAP.put("gfh", "高负荷");
        RISK_TYPE_MAP.put("dtg", "断头管");
        RISK_TYPE_MAP.put("shd", "涉河道");
        RISK_TYPE_MAP.put("jsd", "积水点");
    }

    @Override
    public ResponseData<DrainOperationalRiskEditDto> getOperationalRisk(LocalDateTime now) {
        // 计算近一年的起始时间
        LocalDateTime oneYearAgo = now.minusYears(1);
        DrainOperationalRiskEditDto drainOperationalRiskEditDto = new DrainOperationalRiskEditDto();
        // 计算近一年的去年的起始时间
        LocalDateTime twoYearsAgo = oneYearAgo.minusYears(1);
        // 获取近一年和近一年的去年的风险数据
        List<GwWbfx> currentYearList = getRiskListByTimeRange(oneYearAgo, now);
        List<GwWbfx> lastYearList = getRiskListByTimeRange(twoYearsAgo, oneYearAgo.minusDays(1));

        // 计算今年和去年的 GIS 长度
        double currentYearGisLength = calculateGisLength(currentYearList);
        double lastYearGisLength = calculateGisLength(lastYearList);

        // 转换为 km
        currentYearGisLength /= 1000;
        lastYearGisLength /= 1000;

        drainOperationalRiskEditDto.setGisLength(currentYearGisLength);

        // 计算同比
        String chainRatio = calculateChainRatio(currentYearGisLength, lastYearGisLength);
        drainOperationalRiskEditDto.setChainRatio(chainRatio);

        // 计算已解决和未解决的数量
        int resolvedCount = calculateCountByStatus(currentYearList, "yjj");
        int unsolvedCount = calculateCountByStatus(currentYearList, "wjj");

        // 计算已解决和未解决的占比
        String resolvedProportion = calculateProportion(resolvedCount, currentYearList.size());
        String unsolvedProportion = calculateProportion(unsolvedCount, currentYearList.size());

        drainOperationalRiskEditDto.setResolved(String.valueOf(resolvedCount));
        drainOperationalRiskEditDto.setResolvedProportion(resolvedProportion);
        drainOperationalRiskEditDto.setUnsolved(String.valueOf(unsolvedCount));
        drainOperationalRiskEditDto.setUnsolvedProportion(unsolvedProportion);

        // 计算图表内容
        Map<String, Map<String, Double>> pipeLengthByTypeAndRiskType = calculatePipeLengthByTypeAndRiskType(currentYearList);
        drainOperationalRiskEditDto.setAmountCondition(createEchartsValuesByType(pipeLengthByTypeAndRiskType, "全部"));
        drainOperationalRiskEditDto.setRainCondition(createEchartsValuesByType(pipeLengthByTypeAndRiskType, "ys"));
        drainOperationalRiskEditDto.setSewageCondition(createEchartsValuesByType(pipeLengthByTypeAndRiskType, "ws"));

        // 这里假设 ResponseData 是一个通用的响应类，需要根据实际情况创建实例
        ResponseData<DrainOperationalRiskEditDto> responseData = new ResponseData<>();
        responseData.setData(drainOperationalRiskEditDto);
        return responseData;
    }

    /**
     * 根据年份获取风险列表
     *
     * @return 风险列表
     */
    private List<GwWbfx> getRiskListByTimeRange(LocalDateTime start, LocalDateTime end) {
        // 将 LocalDateTime 转换为 java.util.Date
        Date startDate = Timestamp.valueOf(start.atZone(ZoneId.systemDefault()).toLocalDateTime());
        Date endDate = Timestamp.valueOf(end.atZone(ZoneId.systemDefault()).toLocalDateTime());

        LambdaQueryWrapper<GwWbfx> queryWrapper = new LambdaQueryWrapper<>();
        // 使用转换后的 Date 对象进行筛选
        queryWrapper.ge(GwWbfx::getFxsj, startDate);
        queryWrapper.lt(GwWbfx::getFxsj, endDate);
        return gwWbfxService.list(queryWrapper);
    }

    /**
     * 计算 GIS 长度
     *
     * @param list 风险列表
     * @return GIS 长度
     */
    private double calculateGisLength(List<GwWbfx> list) {
        double totalLength = 0;
        for (GwWbfx gwWbfx : list) {
            GwWbfxVo.GwPipeData pipeData = parsePipeData(gwWbfx.getOldGwSumInfo());
            if (pipeData != null) {
                try {
                    totalLength += pipeData.getPipeLength();
                } catch (NumberFormatException e) {
                    // 处理长度解析异常
                }
            }
        }
        return totalLength;
    }

    /**
     * 计算指定状态的数量
     *
     * @param list   风险列表
     * @param status 状态
     * @return 数量
     */
    private int calculateCountByStatus(List<GwWbfx> list, String status) {
        int count = 0;
        for (GwWbfx gwWbfx : list) {
            if (status.equals(gwWbfx.getStatus())) {
                count++;
            }
        }
        return count;
    }

    /**
     * 计算占比
     *
     * @param count 数量
     * @param total 总数
     * @return 占比字符串
     */
    private String calculateProportion(int count, int total) {
        if (total == 0) {
            return "0%";
        }
        return String.format("%.2f%%", (double) count / total * 100);
    }

    /**
     * 计算同比
     *
     * @param currentValue 当前值
     * @param lastValue    去年值
     * @return 同比字符串
     */
    private String calculateChainRatio(double currentValue, double lastValue) {
        if (lastValue == 0) {
            return lastValue > currentValue ? "-100%" : "100%";
        }
        return String.format("%.2f%%", (currentValue - lastValue) / lastValue * 100);
    }

    /**
     * 计算不同类型管道和风险类型的长度
     *
     * @param list 风险列表
     * @return 不同类型管道和风险类型长度的映射
     */
    private Map<String, Map<String, Double>> calculatePipeLengthByTypeAndRiskType(List<GwWbfx> list) {
        Map<String, Map<String, Double>> pipeLengthByTypeAndRiskType = new HashMap<>();
        for (GwWbfx gwWbfx : list) {
            GwWbfxVo.GwPipeData pipeData = parsePipeData(gwWbfx.getOldGwSumInfo());
            if (pipeData != null) {
                String type = pipeData.getSyxz();
                String riskType = gwWbfx.getFxType();
                try {
                    double length = pipeData.getPipeLength() / 1000;
                    pipeLengthByTypeAndRiskType.computeIfAbsent(riskType, k -> new HashMap<>());
                    pipeLengthByTypeAndRiskType.get(riskType).put(type, pipeLengthByTypeAndRiskType.get(riskType).getOrDefault(type, 0.0) + length);
                    pipeLengthByTypeAndRiskType.get(riskType).put("全部", pipeLengthByTypeAndRiskType.get(riskType).getOrDefault("全部", 0.0) + length);
                } catch (NumberFormatException e) {
                    // 处理长度解析异常
                }
            }
        }
        return pipeLengthByTypeAndRiskType;
    }

    /**
     * 创建 EchartsValue 列表
     *
     * @param pipeLengthByTypeAndRiskType 不同类型管道和风险类型长度的映射
     * @param type                        指定类型
     * @return EchartsValue 列表
     */
    private List<DrainOperationalRiskEditDto.EchartsValue> createEchartsValuesByType(Map<String, Map<String, Double>> pipeLengthByTypeAndRiskType, String type) {
        List<DrainOperationalRiskEditDto.EchartsValue> echartsValues = new ArrayList<>();
        for (Map.Entry<String, Map<String, Double>> entry : pipeLengthByTypeAndRiskType.entrySet()) {
            DrainOperationalRiskEditDto.EchartsValue echartsValue = new DrainOperationalRiskEditDto.EchartsValue();
            // 将英文缩写转换为中文
            String riskTypeChinese = RISK_TYPE_MAP.getOrDefault(entry.getKey(), entry.getKey());
            echartsValue.setName(riskTypeChinese);
            double value = entry.getValue().getOrDefault(type, 0.0);
            // 使用 DecimalFormat 进行四舍五入保留两位小数
            java.text.DecimalFormat df = new java.text.DecimalFormat("0.00");
            String formattedValue = df.format(value);
            echartsValue.setValue(formattedValue);
            echartsValues.add(echartsValue);
        }
        return echartsValues;
    }

    /**
     * 解析管道数据
     *
     * @param oldGwSumInfo 管道信息
     * @return 管道数据对象
     */
    private GwWbfxVo.GwPipeData parsePipeData(String oldGwSumInfo) {
        try {
            return JSONObject.parseObject(oldGwSumInfo, GwWbfxVo.GwPipeData.class);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 将长度转换为 km
     *
     * @param pipeLengthByType 不同类型管道长度的映射
     * @return 转换为 km 后的映射
     */
    private Map<String, Double> convertToKm(Map<String, Double> pipeLengthByType) {
        Map<String, Double> pipeLengthByTypeInKm = new HashMap<>();
        for (Map.Entry<String, Double> entry : pipeLengthByType.entrySet()) {
            pipeLengthByTypeInKm.put(entry.getKey(), entry.getValue() / 1000);
        }
        return pipeLengthByTypeInKm;
    }
    //运行风险end

    /**
     * 初步完结
     *
     * @return
     */
    private static final Map<String, String> TYPE_MAP = new HashMap<>();

    static {
        TYPE_MAP.put("gxgz", "更新改造");
        TYPE_MAP.put("jgwx", "结构维修");
        TYPE_MAP.put("qlwx", "清理维修");
        TYPE_MAP.put("jgzl", "井盖治理");
    }

    @Override
    public ResponseData<DrainFacilityBlanking> getFacilityBlanking(LocalDateTime now) {
        // 计算近一年的起始时间
        LocalDateTime oneYearAgo = now.minusYears(1);
        Date startDate = Timestamp.valueOf(oneYearAgo.atZone(ZoneId.systemDefault()).toLocalDateTime());
        Date endDate = Timestamp.valueOf(now.atZone(ZoneId.systemDefault()).toLocalDateTime());
        // 筛选出状态为 "wangong" 且完工时间为近一年的数据
        LambdaQueryWrapper<GwGxgz> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GwGxgz::getStatus, "wangong")
                .ge(GwGxgz::getWgsj, startDate)
                .lt(GwGxgz::getWgsj, endDate);
        List<GwGxgz> gwGxgzList = gwGxgzService.list(wrapper);

        DrainFacilityBlanking drainFacilityBlanking = new DrainFacilityBlanking();

        // 初始化更新改造长度、局部维修数、井盖治理数
        double renewalLength = 0;
        int localMaintenance = 0;
        int manholeCoverTreatment = 0;

        // 初始化按时间和按类型的统计数据
        Map<Integer, Integer> monthlyCount = new HashMap<>();
        Map<String, Integer> resolvedCount = new HashMap<>();
        Map<String, Integer> unresolvedCount = new HashMap<>();
        Map<String, Integer> resolvedRateCount = new HashMap<>();

        ObjectMapper objectMapper = new ObjectMapper();

        for (GwGxgz gwGxgz : gwGxgzList) {
            try {
                String newGwSumInfo = gwGxgz.getNewGwSumInfo();
                if (newGwSumInfo != null) {
                    GxGxgzVo.CompletedIndoData completedIndoData = objectMapper.readValue(newGwSumInfo, GxGxgzVo.CompletedIndoData.class);
                    // 计算更新改造长度
                    if (completedIndoData.getPipeLengthWs() != null) {
                        renewalLength += completedIndoData.getPipeLengthWs();
                    }
                }
                // 计算局部维修数
                localMaintenance += Optional.ofNullable(gwGxgz.getWgJcjCount()).orElse(0)
                        + Optional.ofNullable(gwGxgz.getWgYskCount()).orElse(0)
                        + Optional.ofNullable(gwGxgz.getWgBzCount()).orElse(0);
                // 计算井盖治理数
                if ("jgzl".equals(gwGxgz.getPlanType())) {
                    manholeCoverTreatment++;
                }
                // 按时间统计
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(gwGxgz.getWgsj());
                int month = calendar.get(Calendar.MONTH) + 1;
                monthlyCount.put(month, monthlyCount.getOrDefault(month, 0) + 1);
                // 按类型统计
                String planType = gwGxgz.getPlanType();
                resolvedCount.put(planType, resolvedCount.getOrDefault(planType, 0) + 1);
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
        }

        // 计算未解决数量和解决率
        LambdaQueryWrapper<GwGxgz> unresolvedWrapper = new LambdaQueryWrapper<>();
        unresolvedWrapper.ne(GwGxgz::getStatus, "wangong")
                .ge(GwGxgz::getWgsj, oneYearAgo)
                .lt(GwGxgz::getWgsj, now);
        List<GwGxgz> unresolvedList = gwGxgzService.list(unresolvedWrapper);

        for (GwGxgz gwGxgz : unresolvedList) {
            String planType = gwGxgz.getPlanType();
            unresolvedCount.put(planType, unresolvedCount.getOrDefault(planType, 0) + 1);
        }

        for (Map.Entry<String, Integer> entry : resolvedCount.entrySet()) {
            String planType = entry.getKey();
            int resolved = entry.getValue();
            int unresolved = unresolvedCount.getOrDefault(planType, 0);
            int total = resolved + unresolved;
            int resolvedRate = total == 0 ? 0 : (int) ((double) resolved / total * 100);
            resolvedRateCount.put(planType, resolvedRate);
        }

        // 设置更新改造长度、局部维修数、井盖治理数
        drainFacilityBlanking.setRenewalLength(renewalLength);
        drainFacilityBlanking.setLocalMaintenance(String.valueOf(localMaintenance));
        drainFacilityBlanking.setManholeCoverTreatment(String.valueOf(manholeCoverTreatment));

        // 设置按时间的统计数据
        DrainFacilityBlankingEditDto.EchartsValue annualTrendValue = new DrainFacilityBlankingEditDto.EchartsValue();
        List<String> xValue = new ArrayList<>();
        List<Integer> seriesValue = new ArrayList<>();
        for (int i = 1; i <= 12; i++) {
            xValue.add(String.valueOf(i));
            seriesValue.add(monthlyCount.getOrDefault(i, 0));
        }
        annualTrendValue.setXValue(xValue);
        annualTrendValue.setSeriesValue(seriesValue);
        drainFacilityBlanking.setAnnualTrendValue(annualTrendValue);

        // 设置按类型的统计数据
        DrainFacilityBlankingEditDto.EchartsValueSolution solutionInformationValue = new DrainFacilityBlankingEditDto.EchartsValueSolution();
        List<String> typeXValue = Arrays.asList("gxgz", "jgwx", "qlwx", "jgzl");
        List<Integer> resolved = new ArrayList<>();
        List<Integer> unresolved = new ArrayList<>();
        List<Integer> resolvedRate = new ArrayList<>();
        for (String type : typeXValue) {
            resolved.add(resolvedCount.getOrDefault(type, 0));
            unresolved.add(unresolvedCount.getOrDefault(type, 0));
            resolvedRate.add(resolvedRateCount.getOrDefault(type, 0));
        }
        // 将横坐标转换为中文
        List<String> chineseTypeXValue = new ArrayList<>();
        for (String type : typeXValue) {
            chineseTypeXValue.add(TYPE_MAP.getOrDefault(type, type));
        }

        solutionInformationValue.setXValue(chineseTypeXValue);
        solutionInformationValue.setResolvedValue(resolved);
        solutionInformationValue.setUnresolvedValue(unresolved);
        solutionInformationValue.setResolvedRateValue(resolvedRate);
        drainFacilityBlanking.setSolutionInformationValue(solutionInformationValue);

        return ResponseData.ok(drainFacilityBlanking);
    }

//    @Override
//    public ResponseData<DrainPipeNetworkTesting> getPipeNetworkTesting () {
//        DrainPipeNetworkTesting drainPipeNetworkTesting = new DrainPipeNetworkTesting();
//        double pipeNetworkGoodRate = 0;
//        double actualDetectionLength = 0;
//        //缺陷长度
//        double defectLength = 0;
//        double rainDetectionLength = 0;
//        double sewageDetectionLength = 0;
//        DrainPipeNetworkTestingEditDto.EchartsValue pipeNetworkInspectionValue =
//                new DrainPipeNetworkTestingEditDto.EchartsValue();
//        // 用于记录每个结构缺陷名称对应的检测长度总和
//        Map<String, Double> defectLengthMap = new HashMap<>();
//        List<String> xValue = new ArrayList<>();
//        List<Integer> seriesValue = new ArrayList<>();
//        List<Cctv> cctvList = cctvService.list();
//        if (cctvList.size() > 0) {
//            for (Cctv cctv : cctvList) {
//                CctvWorkRecord cctvWorkRecord = cctvWorkRecordService.getById(cctv.getCctvWorkId());
//                if (Objects.nonNull(cctvWorkRecord)) {
//                    defectLength += cctvWorkRecord.getBhLength();
//                    actualDetectionLength += cctvWorkRecord.getWorkLength();
//                    GwPipe gwPipe = gwPipeService.getById(cctv.getCctvGddm());
//                    if (Objects.nonNull(gwPipe)) {
//                        if ("ys".equals(gwPipe.getSyxz()))
//                            rainDetectionLength += cctvWorkRecord.getWorkLength();
//                        if ("ws".equals(gwPipe.getSyxz()))
//                            sewageDetectionLength += cctvWorkRecord.getWorkLength();
//                    }
//                }
//                // 填充图表数据
//                String[] jgqxCodes = cctv.getCctvJgqx().split(",");
//                for (String code : jgqxCodes) {
//                    CctvJgqx cctvJgqx = cctvJgqxService.getById(code);
//                    if (Objects.nonNull(cctvJgqx)) {
//                        String defectName = cctvJgqx.getName();
//                        double lengthInKm = cctvWorkRecord.getWorkLength() / 1000.0;
//                        defectLengthMap.put(defectName, defectLengthMap.getOrDefault(defectName,
//                                0.0) + lengthInKm);
//
//                    }
//                }
//            }
//        }
//        // 计算管网完好率
//        if (actualDetectionLength > 0) {
//            pipeNetworkGoodRate = (actualDetectionLength - defectLength) / actualDetectionLength;
//        }
//
//        // 将缺陷长度总和映射转换为图表所需的横坐标和纵坐标数据
//        for (Map.Entry<String, Double> entry : defectLengthMap.entrySet()) {
//            xValue.add(entry.getKey());
//            seriesValue.add(entry.getValue().intValue());
//        }
//        pipeNetworkInspectionValue.setXValue(xValue);
//        pipeNetworkInspectionValue.setSeriesValue(seriesValue);
//        // 设置DrainPipeNetworkTesting对象的属性
//        drainPipeNetworkTesting.setPipeNetworkGoodRate(String.valueOf(pipeNetworkGoodRate));
//        drainPipeNetworkTesting.setActualDetectionLength(String.valueOf(actualDetectionLength / 1000));
//        drainPipeNetworkTesting.setRainDetectionLength(String.valueOf(rainDetectionLength / 1000));
//        drainPipeNetworkTesting.setSewageDetectionLength(String.valueOf(sewageDetectionLength / 1000));
//        drainPipeNetworkTesting.setPipeNetworkInspectionValue(pipeNetworkInspectionValue);
//        // 这里假设ResponseData是一个通用的返回数据结构，根据实际情况进行调整
//        return ResponseData.ok(drainPipeNetworkTesting);
//    }

    @Override
    public ResponseData<DrainPipeNetworkTesting> getPipeNetworkTesting() {
        DrainPipeNetworkTesting drainPipeNetworkTesting = pipeNetworkTestingMapper.getPipeNetworkTesting();
        List<Map<String, Object>> results = pipeNetworkTestingMapper.getPipeNetworkTestingChart();
        DrainPipeNetworkTestingEditDto.EchartsValue echartsValue = new DrainPipeNetworkTestingEditDto.EchartsValue();
        List<String> xValues = new ArrayList<>();
        List<Integer> seriesValues = new ArrayList<>();
        for (Map<String, Object> result : results) {
            xValues.add((String) result.get("xvalue"));
            seriesValues.add((Integer) result.get("seriesvalue"));
        }
        echartsValue.setXValue(xValues);
        echartsValue.setSeriesValue(seriesValues);
        drainPipeNetworkTesting.setPipeNetworkInspectionValue(echartsValue);
        return ResponseData.ok(drainPipeNetworkTesting);
    }

    // 数字月份到中文月份的映射
    private static final Map<String, String> MONTH_MAP = new HashMap() {{
        put("01", "1月");
        put("02", "2月");
        put("03", "3月");
        put("04", "4月");
        put("05", "5月");
        put("06", "6月");
        put("07", "7月");
        put("08", "8月");
        put("09", "9月");
        put("10", "10月");
        put("11", "11月");
        put("12", "12月");
    }};

    @Override
    public ResponseData<DrainMaintenanceProduction> getMaintenanceProduction(LocalDate now) {
        // 计算近一年的起始时间
        LocalDate oneYearAgo = now.minusYears(1);
        // 计算近一年雨水计划件数（按月）
        Map<String, Integer> ysCountByMonth = getCountByMonth(oneYearAgo, now, null, "ys");
        // 计算近一年雨水计划完成（按月）
        Map<String, Integer> repliedYsCountByMonth = getCountByMonth(oneYearAgo, now, "replied", "ys");
        // 计算近一年污水计划件数（按月）
        Map<String, Integer> wsCountByMonth = getCountByMonth(oneYearAgo, now, null, "ws");
        // 计算近一年污水计划完成（按月）
        Map<String, Integer> repliedWsCountByMonth = getCountByMonth(oneYearAgo, now, "replied", "ws");
        // 创建 EchartsValue 对象并填充数据
        DrainMaintenanceProductionEditDto.EchartsValue echartsValue = new DrainMaintenanceProductionEditDto.EchartsValue();
        echartsValue.setXValues(new ArrayList<>(MONTH_MAP.values()));
        // 填充雨水相关数据
        echartsValue.setSeriesBarPlanRainwater(convertToValueList(ysCountByMonth));
        echartsValue.setSeriesBarRealRainwater(convertToValueList(repliedYsCountByMonth));
        // 填充污水相关数据
        echartsValue.setSeriesBarPlanSewage(convertToValueList(wsCountByMonth));
        echartsValue.setSeriesBarRealSewage(convertToValueList(repliedWsCountByMonth));
        // 计算并填充完成率数据（这里简单示例，实际计算可能更复杂）
        echartsValue.setSeriesLineFinishReta(calculateFinishRate(ysCountByMonth, repliedYsCountByMonth, wsCountByMonth, repliedWsCountByMonth));
        Double scPlanLength = scPlanMapper.getScPlanLength(oneYearAgo, now);
        BigDecimal bd = new BigDecimal(scPlanLength / 1000);
        bd = bd.setScale(2, RoundingMode.HALF_UP);
        Double ys = scPlanMapper.getMudOutput(oneYearAgo, now, "ys");
        Double ws = scPlanMapper.getMudOutput(oneYearAgo, now, "ws");
        Double operationRateAll = gwPipeMapper.getnormalOperationRateAll();
        Double operationRate = gwPipeMapper.getnormalOperationRate();
        // 创建 DrainMaintenanceProduction 对象并填充数据
        DrainMaintenanceProduction production = new DrainMaintenanceProduction();
        production.setAnnualTrendValue(echartsValue);
        production.setActualCuringLength(bd.doubleValue());
        production.setRainSludge(String.valueOf(ys));
        production.setSewageSludge(String.valueOf(ws));
        production.setPipeNetworkSmooth(String.format("%.2f", operationRate / operationRateAll * 100));
        return ResponseData.ok(production);
    }

    @Resource
    private CctvBhMapper cctvBhMapper;

    @Override
    public DrainPipelineDefect getDrainPipelineDefect() {
        DrainPipelineDefect result = new DrainPipelineDefect();

        // 查询 bhJgqx 不为空
        LambdaQueryWrapper<CctvBh> wrapper = new LambdaQueryWrapper<>();
        wrapper.isNotNull(CctvBh::getBhJgqx).ne(CctvBh::getBhJgqx, "");

        List<CctvBh> list = cctvBhMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            Map<String, String> TYPE_MAP = new HashMap<>();
            TYPE_MAP.put("FS", "腐蚀");
            TYPE_MAP.put("PL", "破裂");
            TYPE_MAP.put("BX", "变形");
            TYPE_MAP.put("CK", "错口");
            TYPE_MAP.put("TJ", "脱节");
            TYPE_MAP.put("SL", "渗漏");
            TYPE_MAP.put("QR", "侵入");
            result.setPipelineDefectTypeValue(buildDefectChartValue(list, CctvBh::getBhJgqx, TYPE_MAP));
            Map<String, String> DEGREE_MAP = new HashMap<>();
            DEGREE_MAP.put("QD", "轻度");
            DEGREE_MAP.put("ZD", "中度");
            DEGREE_MAP.put("SD", "重度");
            DEGREE_MAP.put("PD", "重度+");
            DEGREE_MAP.put("w", "无危害");
            result.setPipelineDefectDegreeValue(buildDefectChartValue(list, CctvBh::getBhJgqxcd, DEGREE_MAP));
        }
        return result;
    }

    @Resource
    private SysResMapper sysResMapper;

    @Override
    public Page<PipeNetworkDefectLibraryVo> getPipeNetworkDefectLibrary(String type, String degree, Integer currentPage, Integer pageSize) {
        Map<String, String> TYPE_MAP = new HashMap<>();
        TYPE_MAP.put("腐蚀", "FS");
        TYPE_MAP.put("破裂", "PL");
        TYPE_MAP.put("变形", "BX");
        TYPE_MAP.put("错口", "CK");
        TYPE_MAP.put("脱节", "TJ");
        TYPE_MAP.put("渗漏", "SL");
        TYPE_MAP.put("侵入", "QR");

        Map<String, String> DEGREE_MAP = new HashMap<>();
        DEGREE_MAP.put("轻度", "QD");
        DEGREE_MAP.put("中度", "ZD");
        DEGREE_MAP.put("重度", "SD");
        DEGREE_MAP.put("重度+", "PD");
        DEGREE_MAP.put("无危害", "w");

        String typeCode = TYPE_MAP.get(type);
        String degreeCode = DEGREE_MAP.get(degree);

        LambdaQueryWrapper<CctvBh> wrapper = new LambdaQueryWrapper<>();
        wrapper.isNotNull(CctvBh::getBhJgqx).ne(CctvBh::getBhJgqx, "")
                .eq(CctvBh::getBhJgqx, typeCode)
                .eq(CctvBh::getBhJgqxcd, degreeCode);

        Page<CctvBh> page = new Page<>(currentPage, pageSize);
        Page<CctvBh> bhPage = cctvBhMapper.selectPage(page, wrapper);

        List<PipeNetworkDefectLibraryVo> voList = bhPage.getRecords().stream().map(item -> {
            PipeNetworkDefectLibraryVo vo = new PipeNetworkDefectLibraryVo();
            BeanUtils.copyProperties(item, vo);

            // 查询并设置图片路径列表
            List<SysRes> sysResList = sysResMapper.selectList(new LambdaQueryWrapper<SysRes>()
                    .like(SysRes::getRelId, item.getBhGddm() + "_" + item.getBhWorkId())
                    .eq(SysRes::getResType, "cctvBh"));
            List<String> imageUrls = sysResList.stream()
                    .map(res -> {
                        LocalDateTime createTime = res.getCreateDate();
                        int year = createTime.getYear();
                        int month = createTime.getMonthValue();
                        return String.format("Z:/pic/cctvBh/%d/%d/%s", year, month, res.getFileName());
                    })
                    .collect(Collectors.toList());

            vo.setImages(imageUrls);
            return vo;
        }).collect(Collectors.toList());

        // 构造新的分页对象，设置新记录
        Page<PipeNetworkDefectLibraryVo> resultPage = new Page<>();
        resultPage.setCurrent(bhPage.getCurrent());
        resultPage.setSize(bhPage.getSize());
        resultPage.setTotal(bhPage.getTotal());
        resultPage.setPages(bhPage.getPages());
        resultPage.setRecords(voList);

        return resultPage;
    }


    private List<DrainPipelineDefectEditDto.EchartsValue> buildDefectChartValue(
            List<CctvBh> dataList,
            Function<CctvBh, String> fieldExtractor,
            Map<String, String> labelMap
    ) {
        // 将字段统一转为大写后进行分组计数
        Map<String, Long> countMap = dataList.stream()
                .map(item -> {
                    String field = fieldExtractor.apply(item);
                    return field != null ? field.toUpperCase() : ""; // 防空指针
                })
                .filter(s -> !s.isEmpty()) // 过滤空字符串
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

        return labelMap.entrySet().stream()
                .map(entry -> {
                    String code = entry.getKey();
                    String name = entry.getValue();
                    long count = countMap.getOrDefault(code, 0L);

                    DrainPipelineDefectEditDto.EchartsValue val = new DrainPipelineDefectEditDto.EchartsValue();
                    val.setName(name);
                    val.setValue(String.valueOf(count)); // 👈 直接用数量
                    return val;
                })
                .collect(Collectors.toList());
    }


    private Map<String, Integer> getCountByMonth(LocalDate oneYearAgo, LocalDate now, String status, String syxz) {
        Map<String, Integer> result = new HashMap<>();
        List<Map> countList = scPlanMapper.getQueryQuantityByMonth(oneYearAgo, now, status, syxz);
        for (Map<String, Object> item : countList) {
            String monthNum = (String) item.get("month");
            String month = MONTH_MAP.getOrDefault(monthNum, monthNum);  // 转换为中文月份
            Integer count = (Integer) item.get("count");
            result.put(month, count);
        }
        return result;
    }

    private List<Integer> convertToValueList(Map<String, Integer> countMap) {
        List<Integer> valueList = new ArrayList<>();
        for (String month : MONTH_MAP.values()) {
            valueList.add(countMap.getOrDefault(month, 0));
        }
        return valueList;
    }

    private List<Double> calculateFinishRate(Map<String, Integer> planYsMap, Map<String, Integer> realYsMap,
                                             Map<String, Integer> planWsMap, Map<String, Integer> realWsMap) {
        List<Double> finishRateList = new ArrayList<>();
        for (String month : MONTH_MAP.values()) {
            int planYs = planYsMap.getOrDefault(month, 0);
            int realYs = realYsMap.getOrDefault(month, 0);
            int planWs = planWsMap.getOrDefault(month, 0);
            int realWs = realWsMap.getOrDefault(month, 0);
            int totalPlan = planYs + planWs;
            int totalReal = realYs + realWs;
            double finishRate = totalPlan > 0 ? (totalReal * 100 / totalPlan) : 0;
            finishRateList.add(finishRate);
        }
        return finishRateList;
    }
}
