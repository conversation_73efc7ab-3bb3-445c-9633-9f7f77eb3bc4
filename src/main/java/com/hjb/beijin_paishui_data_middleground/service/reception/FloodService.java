package com.hjb.beijin_paishui_data_middleground.service.reception;

import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.entity.sub.vo.StationInfo;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.flood.FloodPipeNetworkMonitorEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.flood.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @Description: 防汛调度service
 * @Author: lvhongen
 * @Date: 2025-03-19 22:28
 * @Version： 1.0
 **/
public interface FloodService {
    /**
     * 下凹桥
     * @param searchAdministrative
     * @param searchCompany
     * @return
     */
    ResponseData<FloodRecessedBridge> getRecessedBridge (String searchAdministrative, String searchCompany);
    /**
     * 获取实时雨量
     * @return
     */
    ResponseData<FloodRealTimeRainfall> getRealTimeRainfall ();

    /**
     * 风险点
     * @param searchAdministrative
     * @param searchCompany
     * @return
     */
    ResponseData<FloodRiskPoint> getRiskPoint (String searchAdministrative, String searchCompany);

    /**
     * 水厂数据
     * @return
     */
    ResponseData<FloodWaterPlant> getWaterPlant ();

    /**
     * 抢险单元
     * @param searchType
     * @param searchCompany
     * @param searchUnit
     * @return
     */
    ResponseData<FloodEmergencyUnit> getEmergencyUnit (String searchType, String searchCompany, String searchUnit);

    /**
     * 处理物理网检测站点清单
     * @param file
     */
    ResponseData uploadStation (MultipartFile file);

    /**
     * 管网监测
     * @param type
     * @return
     */
    ResponseData<FloodPipeNetworkMonitorEditDto> getPipeNetworkMonitor (String type);

    /**
     * 查询下凹桥查询列表
     * @return
     */
    ResponseData getRecessedBridgeQueryList ();

    ResponseData getRiskPointQueryList ();

//    ResponseData<List<StationInfo>> getUpdateStation ();

    /**
     * 查询抢险单元查询列表
     * @return
     */
    ResponseData getEmergencyUnitQueryList ();
}
