package com.hjb.beijin_paishui_data_middleground.service.reception;

import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical.*;

import java.time.LocalDateTime;

/**
 * @Description: 综合态势service
 * @Author: lvhongen
 * @Date: 2025-03-19 22:26
 * @Version： 1.0
 **/
public interface SyntheticalService {
    /**
     * 管网运营数据
     * @return
     */
    ResponseData<SyntheticalPipelineOperation> getPipelineOperation ();

    /**
     * 管网板块数据
     * @return
     */
    ResponseData<SyntheticalPipelinePlate> getPipelinePlate ();

    /**
     * 接诉即办
     * @param now 时间
     * @return
     */
    ResponseData<SyntheticalComplaintResolution> getComplaintResolution (LocalDateTime now);

}
