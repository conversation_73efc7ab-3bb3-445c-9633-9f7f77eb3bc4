package com.hjb.beijin_paishui_data_middleground.controller.interactive;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.*;
import com.hjb.beijin_paishui_data_middleground.entity.sub.vo.RainGaugeInformationVo;
import com.hjb.beijin_paishui_data_middleground.entity.sub.vo.StationMonitoringVo;
import com.hjb.beijin_paishui_data_middleground.service.interactive.FloodInteractiveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/1/15 上午10:08
 * @description 综合态势相关接口
 */
@RestController
@Api(tags = "防汛调度相关接口")
@RequestMapping("interactiveSzls/flood")
@Validated
@Slf4j
public class FloodInteractiveController {
    @Resource
    private FloodInteractiveService floodInteractiveService;

    @GetMapping("getRainwaterPumpingStation")
    @ApiOperation("获取雨水泵站POI信息")
    public ResponseData<List<PumpStationInteractiveVo>> getRainwaterPumpingStation(){
     return floodInteractiveService.getRainwaterPumpingStation("雨");
    }
    @GetMapping("getSewagePumpingStation")
    @ApiOperation("获取污水泵站POI信息")
    public ResponseData<List<PumpStationInteractiveVo>> getSewagePumpingStation(){
        return floodInteractiveService.getRainwaterPumpingStation("污");
    }

    @GetMapping("getRealTimeRainfallSituation")
    @ApiOperation("获取实时雨晴")
    public ResponseData<List<RainGaugeInformationVo>> getRealTimeRainfallSituation () {
        return floodInteractiveService.getRealTimeRainfall();
    }

    @GetMapping("getCarPoint")
    @ApiOperation("获取车辆实时位置信息")
    public ResponseData<List<CarPointVo>> getCarInfo () {
        return floodInteractiveService.getCarInfo();
    }

    @GetMapping("getCarInfoByCarNo")
    @ApiOperation("通过车牌号获取车辆详细信息(车辆弹窗)")
    public ResponseData<PopUpNotificationVo> getCarInfoByCarNo (String carNo) {
        return floodInteractiveService.getCarInfoByCarNo(carNo);
    }

    @GetMapping("getRainwaterLevel")
    @ApiOperation("获取雨水液位")
    public ResponseData<List<StationMonitoringVo>> getRainwaterLevel () {
        return floodInteractiveService.getPipelineLiquidLevel("ysgd");
    }

    @GetMapping("getSewagewaterLevel")
    @ApiOperation("获取污水液位")
    public ResponseData<List<StationMonitoringVo>> getSewagewaterLevel () {
        return floodInteractiveService.getPipelineLiquidLevel("wsgd");
    }

    @GetMapping("getWaterPlant")
    @ApiOperation("获取水厂监测数据（排河口流量）")
    public ResponseData getWaterPlant () {
        return floodInteractiveService.getWaterPlant();
    }
    @GetMapping("getUnderdrainLiquidLevel")
    @ApiOperation("获取暗沟液位")
    public ResponseData<List<StationMonitoringVo>> getUnderdrainLiquidLevel () {
        return floodInteractiveService.getPipelineLiquidLevel("agyw");
    }
}
