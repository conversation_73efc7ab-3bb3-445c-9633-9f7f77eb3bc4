package com.hjb.beijin_paishui_data_middleground.controller.interactive;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.test.CookieData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;

@RestController
@Api(tags = "感知检测数据接口")
@RequestMapping("interactive/getPerceptionData") //获取感知检测数据接口
@Validated
@Slf4j
public class PerceptionDetectionController {
    public static Map<String, Map<String, String>> DEFAULT_DATA_MAP;


    public static Map<String, String> createEnergyMap(String json, String url) {
        Map<String, String> energyMap = new HashMap<>();
        energyMap.put("json", json);
        energyMap.put("url", url);
        return energyMap;
    }


    static {
        // 初始化外层 Map（键：主类别，值：子类别映射）
        DEFAULT_DATA_MAP = new HashMap<>();
        // 示例数据 1：能源类型映射
        //实时雨量
        DEFAULT_DATA_MAP.put("ssyl", createEnergyMap(
                "{\n" +
                        "  \"isDataAcc\": \"1\",\n" +
                        "  \"stfield\": 1,\n" +
                        "  \"order\": \"0\",\n" +
                        "  \"stnm\": \"\",\n" +
                        "  \"adlist\": [],\n" +
                        "  \"comlist\": [],\n" +
                        "  \"subWaterFac\": \"\",\n" +
                        "  \"stationType\": \"bdc\"\n" +
                        "}",
                "http://10.2.103.36/api/rain/raindata-list-map"
        ));


        //实时雨强
        DEFAULT_DATA_MAP.put("ssyq", createEnergyMap(
                "{\n" +
                        "  \"isDataAcc\": \"1\",\n" +
                        "  \"stfield\": 1,\n" +
                        "  \"order\": \"0\",\n" +
                        "  \"stnm\": \"\",\n" +
                        "  \"adlist\": [],\n" +
                        "  \"comlist\": [],\n" +
                        "  \"subWaterFac\": \"\",\n" +
                        "  \"stationType\": \"bdc\"\n" +
                        "}",
                "http://10.2.103.36/api/rain/raindata-list-map"
        ));


        //污水泵站
        DEFAULT_DATA_MAP.put("wsbz", createEnergyMap(
                "{\n" +
                        "  \"stm\": \"\",\n" +
                        "  \"etm\": \"\",\n" +
                        "  \"isInitpool\": null,\n" +
                        "  \"isStorepool\": null,\n" +
                        "  \"stnm\": \"\",\n" +
                        "  \"pumpType\": 2,\n" +
                        "  \"comlist\": [],\n" +
                        "  \"adlist\": []\n" +
                        "}",
                "http://10.2.103.36/api/device/pump-list-map-back"
        ));


        //雨水泵站
        DEFAULT_DATA_MAP.put("ysbz", createEnergyMap(
                "{\n" +
                        "  \"stm\": \"\",\n" +
                        "  \"etm\": \"\",\n" +
                        "  \"isInitpool\": null,\n" +
                        "  \"isStorepool\": null,\n" +
                        "  \"comlist\": [],\n" +
                        "  \"adlist\": []\n" +
                        "}",
                "http://10.2.103.36/api/device/pump-list-map-back"
        ));


        //排河口流量


        //暗沟监测
        DEFAULT_DATA_MAP.put("agjc", createEnergyMap(
                "{\n" +
                        "  \"pageNum\": \"1\",\n" +
                        "  \"pageSize\": \"100\",\n" +
                        "  \"name\": \"\",\n" +
                        "  \"deviceType\": \"\",\n" +
                        "  \"comcd\": \"\",\n" +
                        "  \"category\": \"6\",\n" +
                        "  \"adcd\": \"\",\n" +
                        "  \"status\": \"\",\n" +
                        "  \"swCodeList\": []\n" +
                        "}",
                "http://10.2.103.36/api/IoTDevice/queryByPage"
        ));


        //污水管道流量
        DEFAULT_DATA_MAP.put("wsgdll", createEnergyMap(
                "{\n" +
                        "  \"pageNum\": \"1\",\n" +
                        "  \"pageSize\": \"99999\",\n" +
                        "  \"name\": \"\",\n" +
                        "  \"deviceType\": \"\",\n" +
                        "  \"comcd\": \"\",\n" +
                        "  \"category\": \"3\",\n" +
                        "  \"adcd\": \"\",\n" +
                        "  \"status\": \"\",\n" +
                        "  \"swCodeList\": []\n" +
                        "}",
                "http://10.2.103.36/api/IoTDevice/queryByPageByRedis"
        ));


        //再生水输配
        DEFAULT_DATA_MAP.put("zsssp", createEnergyMap(
                "{\n" +
                        "  \"winame\": \"\",\n" +
                        "  \"rcname\": \"\",\n" +
                        "  \"wrcodelist\": [],\n" +
                        "  \"adcd\": \"\",\n" +
                        "  \"wsaid\": \"\",\n" +
                        "  \"swBsCodelist\": []\n" +
                        "}",
                "http://10.2.103.36/api/waterinlet/getWInletList"
        ));


        //雨水管道液位
        DEFAULT_DATA_MAP.put("ysgdyw", createEnergyMap(
                "{\n" +
                        "  \"pageNum\": \"1\",\n" +
                        "  \"pageSize\": \"9999\",\n" +
                        "  \"name\": \"\",\n" +
                        "  \"deviceType\": \"\",\n" +
                        "  \"comcd\": \"\",\n" +
                        "  \"category\": \"2\",\n" +
                        "  \"adcd\": \"\",\n" +
                        "  \"status\": \"\",\n" +
                        "  \"swCodeList\": []\n" +
                        "}",
                "http://10.2.103.36/api/IoTDevice/queryByPage"
        ));


        //积水监测点
        DEFAULT_DATA_MAP.put("jsjcd", createEnergyMap(
                "{\n" +
                        "  \"code\": \"\",\n" +
                        "  \"name\": \"\",\n" +
                        "  \"status\": 1,\n" +
                        "  \"isShow\": 1\n" +
                        "}",
                "http://10.2.103.36/api/podProne/queryList"
        ));


        //低洼风险点
        DEFAULT_DATA_MAP.put("dwfxd", createEnergyMap(
                "{\n" +
                        "  \"comcd\": \"\",\n" +
                        "  \"adcd\": \"\",\n" +
                        "  \"riskName\": \"\",\n" +
                        "  \"bgDepth\": \"\",\n" +
                        "  \"edDepth\": \"\"\n" +
                        "}",
                "http://10.2.103.36/api/risk/getRiskList"
        ));


        //视频监控
        DEFAULT_DATA_MAP.put("spjk", createEnergyMap(
                "{\n" +
                        "  \"county\": [],\n" +
                        "  \"company\": [],\n" +
                        "  \"name\": \"\",\n" +
                        "  \"stationTypes\": []\n" +
                        "}",
                "http://10.2.103.36/api/video/list"
        ));


        //布控点
        DEFAULT_DATA_MAP.put("bkd", createEnergyMap(
                "{\n" +
                        "  \"name\": \"\",\n" +
                        "  \"adlist\": [],\n" +
                        "  \"comlist\": [],\n" +
                        "  \"arrStatus\": \"\",\n" +
                        "  \"rainBsCode\": \"\",\n" +
                        "  \"swBsCode\": [],\n" +
                        "  \"dutyStatus\": \"\"\n" +
                        "}",
                "http://10.2.103.36/api/crtPoint/findCtrPointCurrentInfo"
        ));


        //车辆
        DEFAULT_DATA_MAP.put("cl", createEnergyMap(
                "{\n" +
                        "  \"keyword\": \"\",\n" +
                        "  \"isTask\": \"\",\n" +
                        "  \"isRoute\": 0,\n" +
                        "  \"isTrail\": 0,\n" +
                        "  \"comList\": [],\n" +
                        "  \"unitList\": [],\n" +
                        "  \"stm\": \"\",\n" +
                        "  \"etm\": \"\"\n" +
                        "}",
                "http://10.2.103.36/api/vehicle-unit/tree-list"
        ));

    }


    @GetMapping("getPerceptionDataType")
    @ApiOperation("获取感知监测数据")
    public ResponseData getPerceptionDataType(String type) {
//        System.out.println("type = " + type);
        Map<String, String> map = DEFAULT_DATA_MAP.get(type);
//        System.out.println("map.get ==> " + map.get("json") + "," + map.get("url"));
        return getData(map.get("json"), map.get("url"));
    }


    //强化版获取车量类型数组数据
    @GetMapping("getCarDataType")
    @ApiOperation("获取车量类型数组数据")
    public ResponseData getCarDataType(String type) {
        Map<String, String> map = DEFAULT_DATA_MAP.get(type);

        // 并行获取数据
        CompletableFuture<ResponseData> dataFuture = CompletableFuture.supplyAsync(() ->
                getData(map.get("json"), map.get("url")));
        CompletableFuture<ResponseData> jingWeiDuFuture = CompletableFuture.supplyAsync(() ->
                getData("{\"isRoute\": 0}", "http://10.2.103.36/api/vehicle-unit/getCarCurrentInfo"));

        // 等待并获取结果
        ResponseData responseData = dataFuture.join();
        ResponseData jingWeiDuAll = jingWeiDuFuture.join();

        LinkedHashMap<String, Object> data = (LinkedHashMap<String, Object>) responseData.getData();
        ArrayList<LinkedHashMap<String, Object>> result = (ArrayList<LinkedHashMap<String, Object>>) data.get("result");

        LinkedHashMap<String, Object> jingWeiDuData = (LinkedHashMap<String, Object>) jingWeiDuAll.getData();
        ArrayList<LinkedHashMap<String, Object>> jingWeiDuresult = (ArrayList<LinkedHashMap<String, Object>>) jingWeiDuData.get("result");

        // 转换为Map提高查找效率
        Map<String, LinkedHashMap<String, Object>> jingWeiDuMap = jingWeiDuresult.stream()
                .collect(Collectors.toMap(
                        m -> (String) m.get("carNo"),
                        Function.identity(),
                        (existing, replacement) -> existing
                ));

        Map<String, List<LinkedHashMap<String, Object>>> unitMap = new HashMap<>();
        unitMap.put("largeUnit", new ArrayList<>());
        unitMap.put("mediumUnit", new ArrayList<>());
        unitMap.put("smallUnit", new ArrayList<>());

        for (LinkedHashMap<String, Object> resultMap : result) {
            processChildren(resultMap, unitMap, jingWeiDuMap);
        }

        return ResponseData.ok(unitMap);
    }

    private void processChildren(LinkedHashMap<String, Object> resultMap,
                                 Map<String, List<LinkedHashMap<String, Object>>> unitMap,
                                 Map<String, LinkedHashMap<String, Object>> jingWeiDuMap) {
        ArrayList<LinkedHashMap<String, Object>> children = (ArrayList<LinkedHashMap<String, Object>>) resultMap.get("children");

        for (LinkedHashMap<String, Object> childMap : children) {
            String name = (String) childMap.get("name");
            List<LinkedHashMap<String, Object>> targetList = null;

            if ("大型单元".equals(name)) {
                targetList = unitMap.get("largeUnit");
            } else if ("中型单元".equals(name)) {
                targetList = unitMap.get("mediumUnit");
            } else if ("小型单元".equals(name)) {
                targetList = unitMap.get("smallUnit");
            }

            if (targetList != null) {
                addJingWeiDu(childMap, jingWeiDuMap);
                targetList.addAll((ArrayList<LinkedHashMap<String, Object>>) childMap.get("children"));
            }
        }
    }

    private void addJingWeiDu(LinkedHashMap<String, Object> childMap,
                              Map<String, LinkedHashMap<String, Object>> jingWeiDuMap) {
        ArrayList<LinkedHashMap<String, Object>> carList = (ArrayList<LinkedHashMap<String, Object>>) childMap.get("children");

        for (LinkedHashMap<String, Object> carInfo : carList) {
            LinkedHashMap<String, Object> jingWeiDu = jingWeiDuMap.get(carInfo.get("id"));
            if (jingWeiDu != null) {
                carInfo.put("bd_LGTD", jingWeiDu.get("bd_LGTD"));
                carInfo.put("bd_LTTD", jingWeiDu.get("bd_LTTD"));
                carInfo.put("lgtd", jingWeiDu.get("lgtd"));
                carInfo.put("lttd", jingWeiDu.get("lttd"));
            }
        }
    }


    //简单版获取车量类型数组数据
//    @GetMapping("getCarDataType")
//    @ApiOperation("获取车量类型数组数据")
//    public ResponseData getCarDataType(String type) {
//        Map<String, String> map = DEFAULT_DATA_MAP.get(type);
//        // 获取tree-list的数据（车辆的大部分数据）
//        ResponseData responseData = getData(map.get("json"), map.get("url"));
//        LinkedHashMap<String, Object> data = (LinkedHashMap<String, Object>) responseData.getData();
//        ArrayList<LinkedHashMap<String, Object>> result = (ArrayList<LinkedHashMap<String, Object>>) data.get("result");
//
//        // 获取所有车辆的经纬度信息
//        ResponseData jingWeiDuAll = getData("{\n" +
//                "  \"isRoute\": 0\n" +
//                "}","http://10.2.103.36/api/vehicle-unit/getCarCurrentInfo");
//        LinkedHashMap<String, Object> jingWeiDuData = (LinkedHashMap<String, Object>) jingWeiDuAll.getData();
//        ArrayList<LinkedHashMap<String, Object>> jingWeiDuresult = (ArrayList<LinkedHashMap<String, Object>>) jingWeiDuData.get("result");
//
//        // 总集合
//        LinkedHashMap<String, Object> linkedHashMap = new LinkedHashMap<>();
//
//        // 单一的车辆集合
//        ArrayList<LinkedHashMap<String, Object>> largeUnit = new ArrayList<>();
//        ArrayList<LinkedHashMap<String, Object>> mediumUnit = new ArrayList<>();
//        ArrayList<LinkedHashMap<String, Object>> smallUnit = new ArrayList<>();
//
//        for (LinkedHashMap<String, Object> resultMap : result) {
//            ArrayList<LinkedHashMap<String, Object>> children = (ArrayList<LinkedHashMap<String, Object>>) resultMap.get("children");
//            for (LinkedHashMap<String, Object> childMap : children) {
//                if ("大型单元".equals(childMap.get("name"))) {
//                    addJingWeiDu(childMap, jingWeiDuresult);
//                    largeUnit.addAll((ArrayList<LinkedHashMap<String, Object>>) childMap.get("children")); // 展开并添加
//                } else if ("中型单元".equals(childMap.get("name"))) {
//                    addJingWeiDu(childMap, jingWeiDuresult);
//                    mediumUnit.addAll((ArrayList<LinkedHashMap<String, Object>>) childMap.get("children")); // 展开并添加
//                } else if ("小型单元".equals(childMap.get("name"))) {
//                    addJingWeiDu(childMap, jingWeiDuresult);
//                    smallUnit.addAll((ArrayList<LinkedHashMap<String, Object>>) childMap.get("children")); // 展开并添加
//                }
//            }
//        }
//
//        // 合并成一个新的集合
//        linkedHashMap.put("largeUnit", largeUnit);
//        linkedHashMap.put("mediumUnit", mediumUnit);
//        linkedHashMap.put("smallUnit", smallUnit);
//
//        return ResponseData.ok(linkedHashMap);
//    }
//
//
//
//    //为买一个车辆添加经纬度信息
//    private void addJingWeiDu(LinkedHashMap<String, Object> childMap, ArrayList<LinkedHashMap<String, Object>> jingWeiDuresult){
//        //所有大型单元所包含的车辆集合
//        ArrayList<LinkedHashMap<String, Object>> carList = (ArrayList<LinkedHashMap<String, Object>>) childMap.get("children");
//        for (LinkedHashMap<String, Object> jingWeiDu : jingWeiDuresult) {
//            //遍历车辆集合获取每一个车辆信息
//            for (LinkedHashMap<String, Object> carInfo : carList) {
//                //判断当前车辆的id是否匹配到了相应的经纬度
//                if (carInfo.get("id").equals(jingWeiDu.get("carNo"))) {
//                    //如果匹配到了，则将相应的经纬度信息添加到该车辆信息中
//                    carInfo.put("bd_LGTD",jingWeiDu.get("bd_LGTD"));
//                    carInfo.put("bd_LTTD",jingWeiDu.get("bd_LTTD"));
//                    carInfo.put("lgtd",jingWeiDu.get("lgtd"));
//                    carInfo.put("lttd",jingWeiDu.get("lttd"));
//                }
//            }
//        }
//    }

    //获取对应url接口的数据
    private ResponseData getData(String json, String url) {
        Map<String, String> cookie = getCookie();
        RestTemplate restTemplate = new RestTemplate();
        // 准备请求头
        HttpHeaders headers = new HttpHeaders();
        // 准备请求体
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Cookie", cookie.get("cookie"));
        headers.set("Setcookie", cookie.get("setCookie"));
        // 创建请求实体
        HttpEntity<String> entity = new HttpEntity<>(json, headers);
        // 发送POST请求
        ResponseEntity<String> response = restTemplate.postForEntity(
                url,
                entity,
                String.class);
//        System.out.println("response==>"+response);

        //判断是否请求成功
        if (response.getStatusCode().is2xxSuccessful()) {
            String jsonResponse = response.getBody();//获取响应体
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                // 将JSON字符串转换为Java对象
                Map<String, Object> responseMap = objectMapper.readValue(jsonResponse, Map.class);
//                System.out.println(responseMap);
                return ResponseData.ok(responseMap);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        } else {
            System.out.println("请求失败，状态码: " + response.getStatusCode());// 获取状态码
        }
        return ResponseData.ok(new HashMap<>());
    }


    //获取cookie与setCookie集合
    private static Map<String, String> getCookie() {
        Map<String, String> map = new HashMap<>();
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        String requestBody = "{\"userName\":\"51worldtwh\",\"password\":\"e9875c6c179a508f81d233627b5bd376\"}";
        HttpEntity<String> entity = new HttpEntity<>(requestBody, headers);
        ResponseEntity<String> response = restTemplate.postForEntity(
                "http://10.2.103.36/api/login",
                entity,
                String.class);
        if (response.getStatusCode().is2xxSuccessful()) {
            String jsonResponse = response.getBody();
//            System.out.println("cookie:"+jsonResponse);
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                DataJson dataJson =
                        objectMapper.readValue(jsonResponse, DataJson.class);
//                System.out.println("cookie==>:"+dataJson.toString());
                CookieData result = dataJson.getResult();
                String setCookie = result.getSetCookie();
//                System.out.println("11111111111111:" + result.getSetCookie());
                String cookie = "JSESSIONID=" + setCookie.substring(setCookie.indexOf("_") + 1);
//                System.out.println(cookie);
                map.put("cookie", cookie);
                map.put("setCookie", setCookie);
                return map;
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        } else {
            System.out.println("请求失败，状态码: " + response.getStatusCode());
        }
        return new HashMap<>();
    }
}

@Data
class DataJson {
    private String url;
    private CookieData result;
}
