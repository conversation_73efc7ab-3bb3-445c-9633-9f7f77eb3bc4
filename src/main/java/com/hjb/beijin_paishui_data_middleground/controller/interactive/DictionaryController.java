package com.hjb.beijin_paishui_data_middleground.controller.interactive;


import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

@RestController
@Api(tags = "字典接口")
@RequestMapping("interactive/dictionary")
@Validated
@Slf4j
public class DictionaryController {
    
    public static final Map<String, String> DICTIONARY_MAP = new LinkedHashMap<>();

    static {
//        DICTIONARY_MAP.put("typeName", "typeValue");
        DICTIONARY_MAP.put("实时雨量", "ssyl");
        DICTIONARY_MAP.put("实时雨强", "ssyq");
        DICTIONARY_MAP.put("污水泵站", "wsbz");
        DICTIONARY_MAP.put("雨水泵站", "ysbz");
        DICTIONARY_MAP.put("排河口流量（未找到）", "phkll");
        DICTIONARY_MAP.put("暗沟监测", "agjc");
        DICTIONARY_MAP.put("污水管道流量", "wsgdll");
        DICTIONARY_MAP.put("再生水输配", "zsssp");
        DICTIONARY_MAP.put("雨水管道液位", "ysgdyw");
        DICTIONARY_MAP.put("积水监测点", "jsjcd");
        DICTIONARY_MAP.put("低洼风险点", "dwfxd");
        DICTIONARY_MAP.put("视频监控", "spjk");
        DICTIONARY_MAP.put("布控点", "bkd");
        DICTIONARY_MAP.put("车辆","cl");

    }


    @GetMapping("getDictionary")
    public ResponseData getDictionary() {
        return ResponseData.ok(DictionaryController.DICTIONARY_MAP);
    }


}
