package com.hjb.beijin_paishui_data_middleground.controller.interactive;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSort;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.dto.GwPipeSeachDto;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.dto.GwWellSeachDto;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.*;
import com.hjb.beijin_paishui_data_middleground.service.interactive.SyntheticaInteractiveService;
import com.hjb.beijin_paishui_data_middleground.utils.interactive.GwPipeVoToNewFormatConverter;
import com.hjb.beijin_paishui_data_middleground.utils.interactive.GwWellVoToNewFormatConverter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.List;
/**
 * <AUTHOR>
 * @date 2025/1/15 上午10:08
 * @description 综合态势相关接口
 */
@RestController
@Api(tags = "综合态势和排水设施交互相关接口")
@RequestMapping("interactive/zhts")
@Validated
@Slf4j
public class SyntheticaInteractiveController {
    @Resource
    private SyntheticaInteractiveService syntheticaInteractiveService;
    @GetMapping("getSyntheticaSelectData")
    @ApiOperation("获取综合态势查询列表")
    @ApiOperationSort(1)
    public ResponseData<SyntheticaSelectData> getSyntheticaSelectData(){
        return syntheticaInteractiveService.getSyntheticaSelectData();
    }
    @PostMapping("seachGwPipePoint")
    @ApiOperation("查询管道台账列表")
    @ApiOperationSort(2)
    public ResponseData<IPage<GwPipeVo>>  seachGwPipePoint(@RequestBody GwPipeSeachDto gwPipeSeachDto){
        return syntheticaInteractiveService.seachGwPipePoint(gwPipeSeachDto);
    }
    @PostMapping("getGwPipeByCode")
    @ApiOperation("通过管道代码查询管线详细信息(雨水管线。污水管线，合流管线弹窗)")
    @ApiOperationSort(3)
    public ResponseData<GwPipeVoToNewFormatConverter.NewFormat> getGwPipeByCode(String code){
        return syntheticaInteractiveService.getPipeInfo(code);
    }
    @PostMapping("seachGwWellPoint")
    @ApiOperation("查询管井台账列表")
    @ApiOperationSort(4)
    public ResponseData<IPage<GwWellVo>> seachGwWellPoint(@RequestBody GwWellSeachDto gwWellSeachDto){
        return syntheticaInteractiveService.seachGwWellPoint(gwWellSeachDto);
    }
    @PostMapping("getGwWellByCode")
    @ApiOperation("通过管井代码查询管井详细信息(倒虹吸，截流井，排河口，检查井，雨水口弹窗)")
    @ApiOperationSort(5)
    public ResponseData<GwWellVoToNewFormatConverter.NewFormat> getGwWellByCode(String code){
        return syntheticaInteractiveService.getWellByCode(code);
    }
    @GetMapping("getAllAdministrativeRegion")
    @ApiOperation("获取所有的行政区POI点位信息列表")
    @ApiOperationSort(44)
    public ResponseData<List<PointVo>> getAllAdministrativeRegion(){
        return syntheticaInteractiveService.getAllAdministrativeRegion();
    }
//    @GetMapping("getAdministrativeRegionByCode")
//    @ApiOperation("通过行政区code获取行政区详细信息")
//    @ApiOperationSort(55)
//    public ResponseData<PartialInteractionVo> getAdministrativeRegionByCode(String code){
//        return syntheticaInteractiveService.getAdministrativeRegionByCode(code);
//    }
    @GetMapping("getAllWaterworks")
    @ApiOperation("获取所有的再生水厂POI点位信息列表")
    @ApiOperationSort(66)
    public ResponseData<List<PointVo>> getAllWaterworks(){
        return syntheticaInteractiveService.getAllWaterworks();
    }
//    @GetMapping("getWaterworksByCode")
//    @ApiOperation("通过再生水厂code获取详细信息")
//    @ApiOperationSort(77)
//    public ResponseData<PartialInteractionVo> getWaterworksByCode(String code){
//        return syntheticaInteractiveService.getWaterworksByCode(code);
//    }
//    @GetMapping("getAllSewageBackbone")
//    @ApiOperation("获取所有污水主干点位信息列表")
//    @ApiOperationSort(88)
//    public ResponseData<List<PointVo>> getAllSewageBackbone(){
//        return syntheticaInteractiveService.getAllSewageBackbone();
//    }
//    @GetMapping("getSewageBackboneByCode")
//    @ApiOperation("通过污水主干code获取详细信息")
//    @ApiOperationSort(99)
//    public ResponseData<PartialInteractionVo> getSewageBackboneByCode(String code){
//        return syntheticaInteractiveService.getSewageBackboneByCode(code);
//    }
//    @GetMapping("getAllRainwaterBackbone")
//    @ApiOperation("获取所有污水主干点位信息列表")
//    @ApiOperationSort(100)
//    public ResponseData<List<PointVo>> getAllRainwaterBackbone(){
//        return syntheticaInteractiveService.getAllRainwaterBackbone();
//    }
//    @GetMapping("getRainwaterBackboneByCode")
//    @ApiOperation("通过雨水主干code获取详细信息")
//    @ApiOperationSort(110)
//    public ResponseData<PartialInteractionVo> getRainwaterBackboneByCode(String code){
//        return syntheticaInteractiveService.getRainwaterBackboneByCode(code);
//    }
//    @GetMapping("getAllOperationUnit")
//    @ApiOperation("获取所有运营单位POI点位信息列表")
//    @ApiOperationSort(120)
//    public ResponseData<List<PointVo>> getAllOperationUnit(){
//        return syntheticaInteractiveService.getAllOperationUnit();
//    }
//    @GetMapping("getOperationUnitByCode")
//    @ApiOperation("通过运营单位code获取详细信息")
//    @ApiOperationSort(130)
//    public ResponseData<PartialInteractionVo> getOperationUnitByCode(String code){
//        return syntheticaInteractiveService.getOperationUnitByCode(code);
//    }
//    @GetMapping("getAllRunningClass")
//    @ApiOperation("获取所有运行班POI点位信息列表")
//    @ApiOperationSort(140)
//    public ResponseData<List<PointVo>> getAllRunningClass(){
//        return syntheticaInteractiveService.getAllRunningClass();
//    }
//    @GetMapping("getRunningClassByCode")
//    @ApiOperation("通过运行班code获取详细信息")
//    @ApiOperationSort(150)
//    public ResponseData<ABanzuVo> getRunningClassByCode(String code){
//        return syntheticaInteractiveService.getRunningClassByCode(code);
//    }
//    @GetMapping("getAllSewageSmallWatershed")
//    @ApiOperation("获取所有污水小流域点位信息列表")
//    @ApiOperationSort(160)
//    public ResponseData<List<PointVo>> getAllSewageSmallWatershed(){
//        return syntheticaInteractiveService.getAllSewageSmallWatershed();
//    }
    @GetMapping("getSewageSmallWatershed")
    @ApiOperation("获取所有污水小流域poi详细信息")
    @ApiOperationSort(170)
    public ResponseData<List<AWsXlyVo>> getSewageSmallWatershedByCode(){
        return syntheticaInteractiveService.getSewageSmallWatershedByCode(null);
    }
//    @GetMapping("getAllRainwaterSmallWatershed")
//    @ApiOperation("获取所有雨水小流域点位信息列表")
//    @ApiOperationSort(180)
//    public ResponseData<List<PointVo>> getAllRainwaterSmallWatershed(){
//        return syntheticaInteractiveService.getAllRainwaterSmallWatershed();
//    }
    @GetMapping("getRainwaterSmallWatershed")
    @ApiOperation("获取所有雨水小流域poi详细信息")
    @ApiOperationSort(190)
    public ResponseData<List<AYsXlyVo>> getRainwaterSmallWatershedByCode(){
        return syntheticaInteractiveService.getRainwaterSmallWatershedByCode(null);
    }
//    @GetMapping("getAllSewageBasin")
//    @ApiOperation("获取所有污水流域点位信息列表")
//    @ApiOperationSort(200)
//    public ResponseData<List<PointVo>> getAllSewageBasin(){
//        return syntheticaInteractiveService.getAllSewageBasin();
//    }
//    @GetMapping("getSewageBasinByCode")
//    @ApiOperation("通过污水流域code获取详细信息")
//    @ApiOperationSort(210)
//    public ResponseData<PartialInteractionVo> getSewageBasinByCode(String code){
//        return syntheticaInteractiveService.getSewageBasinByCode(code);
//    }
//    @GetMapping("getAllRainwaterBasin")
//    @ApiOperation("获取所有雨水流域点位信息列表")
//    @ApiOperationSort(220)
//    public ResponseData<List<PointVo>> getAllRainwaterBasin(){
//        return syntheticaInteractiveService.getAllRainwaterBasin();
//    }
//    @GetMapping("getRainwaterBasinByCode")
//    @ApiOperation("通过雨水流域code获取详细信息")
//    @ApiOperationSort(230)
//    public ResponseData<PartialInteractionVo> getRainwaterBasinByCode(String code){
//        return syntheticaInteractiveService.getRainwaterBasinByCode(code);
//    }
//    @GetMapping("getAllInspectionScope")
//    @ApiOperation("获取所有巡查范围点位信息列表")
//    @ApiOperationSort(240)
//    public ResponseData<List<PointVo>> getAllInspectionScope(){
//        return syntheticaInteractiveService.getAllInspectionScope();
//    }
//    @GetMapping("getInspectionScopeByCode")
//    @ApiOperation("通过巡查范围code获取详细信息")
//    @ApiOperationSort(250)
//    public ResponseData<AXunchaVo> getInspectionScopeByCode(String code){
//        return syntheticaInteractiveService.getInspectionScopeByCode(code);
//    }
//    @GetMapping("getAlloverpass")
//    @ApiOperation("获取所有下凹桥区点位信息列表")
//    @ApiOperationSort(260)
//    public ResponseData<List<ShareBrgInfoVo>> getAlloverpass(){
//        return syntheticaInteractiveService.getAlloverpass();
//    }
//    @GetMapping("getoverpassByCode")
//    @ApiOperation("通过下凹桥code获取详细信息")
//    @ApiOperationSort(270)
//    public ResponseData<ShareBrgInfo> getoverpassByCode(String code){
//        return syntheticaInteractiveService.getoverpassByCode(code);
//    }
//    @GetMapping("getAllRainwaterPipeline")
//    @ApiOperation("获取所有雨水管线点位信息列表")
//    @ApiOperationSort(280)
//    public ResponseData<List<PointVo>> getAllRainwaterPipeline(){
//        return syntheticaInteractiveService.getAllRainwaterPipeline();
//    }
//    @GetMapping("getAllRainwaterPipelineInfo")
//    @ApiOperation("获取所有雨水管线点位信息列表")
//    @ApiOperationSort(280)
//    public ResponseData<List<PointVo>> getAllRainwaterPipelineInfo(){
//        return syntheticaInteractiveService.getAllRainwaterPipeline();
//    }
//    @GetMapping("getAllSewagePipeline")
//    @ApiOperation("获取所有污水管线点位信息列表")
//    @ApiOperationSort(300)
//    public ResponseData<List<PointVo>> getAllSewagePipeline(){
//        return syntheticaInteractiveService.getAllSewagePipeline();
//    }
//    @GetMapping("getAllConfluentPipeline")
//    @ApiOperation("获取所有合流管线点位信息列表")
//    @ApiOperationSort(320)
//    public ResponseData<List<PointVo>> getAllConfluentPipeline(){
//        return syntheticaInteractiveService.getAllConfluentPipeline();
//    }
//    @GetMapping("getAllPaihekou")
//    @ApiOperation("获取所有排河口点位信息列表")
//    @ApiOperationSort(380)
//    public ResponseData<List<PointVo>> getAllPaihekou(){
//        return syntheticaInteractiveService.getAllWellByGzw("phk");
//    }
//    @GetMapping("getAllInvertedSiphon")
//    @ApiOperation("获取所有倒虹吸点位信息列表")
//    @ApiOperationSort(400)
//    public ResponseData<List<PointVo>> getAllInvertedSiphon(){
//        return syntheticaInteractiveService.getAllWellByGzw("dhx");
//    }
//    @GetMapping("getAllInterceptingWell")
//    @ApiOperation("获取所有截流井点位信息列表")
//    @ApiOperationSort(420)
//    public ResponseData<List<PointVo>> getAllInterceptingWell(){
//        return syntheticaInteractiveService.getAllWellByGzw("jlj");
//    }
    }
