package com.hjb.beijin_paishui_data_middleground.controller.interactive;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.PopUpNotificationVo;
import com.hjb.beijin_paishui_data_middleground.entity.sub.vo.StationInformationVo;
import com.hjb.beijin_paishui_data_middleground.service.interactive.TheBridgeInteractiveService;
import com.hjb.beijin_paishui_data_middleground.utils.interactive.StationMonitoringVoToNewFormatConverter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description: 桥区交互接口
 * @Author: lvhongen
 * @Date: 2025-05-14 16:54
 * @Version： 1.0
 **/
@RestController
@Api(tags = "桥区交互接口相关接口")
@RequestMapping("interactive/theBridge")
@Validated
@Slf4j
public class TheBridgeInteractiveController {
    @Resource
    private TheBridgeInteractiveService theBridgeInteractiveService;
    @GetMapping("getMonitoringOfAccumulatedWaterInfo")
    @ApiOperation("获取积水监测详细监测信息(积水监测点弹窗)")
    public ResponseData<StationMonitoringVoToNewFormatConverter.NewFormat> getMonitoringOfAccumulatedWaterInfo (@NotNull String stationCode) {
        return theBridgeInteractiveService.getMonitoringOfAccumulatedWaterInfo(stationCode);
    }

    @GetMapping("getFzsSlhPointl")
    @ApiOperation("获取分钟寺-十里河主要管线点位")
    public ResponseData<List<StationInformationVo>> getFzsSlhPointl(){
        return theBridgeInteractiveService.getFzsSlhPoint();
    }
    @GetMapping("getFzsSlhLiquidLevel")
    @ApiOperation("获取分钟寺-十里河主要管线液位及充满度（雨水管道液位弹窗）")
    public ResponseData<PopUpNotificationVo> getFzsSlhLiquidLevel(String code){
        return theBridgeInteractiveService.getFzsSlhLiquidLevelByCode(code);
    }

    @GetMapping("getFzsSlhRainCondition")
    @ApiOperation("获取分钟寺-十里河雨情（实时雨量弹窗）")
    public ResponseData<PopUpNotificationVo> getFzsSlhRainCondition(){
        return theBridgeInteractiveService.getFzsSlhRainCondition();
    }

}
