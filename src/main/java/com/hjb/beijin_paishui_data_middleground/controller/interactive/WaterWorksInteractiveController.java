package com.hjb.beijin_paishui_data_middleground.controller.interactive;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.PopUpNotificationVo;
import com.hjb.beijin_paishui_data_middleground.service.interactive.WaterWorksInteractiveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * @date 2025/7/2 17:38
 * @description 水厂交互接口
 */
@RestController
@Api(tags = "水厂交互接口相关接口")
@RequestMapping("interactive/waterworks")
@Validated
@Slf4j
public class WaterWorksInteractiveController {

    @Resource
    private WaterWorksInteractiveService waterWorksInteractiveService;

    @GetMapping("hf/getCoarseScreenAndIntakePumpRoom")
    @ApiOperation("粗中格栅间及进水泵房(弹窗)")
    @ApiOperationSupport(order = 1)
    public ResponseData<PopUpNotificationVo> getCoarseScreenAndIntakePumpRoom() {
        List<IndicatorInfo> list = Arrays.asList(
                new IndicatorInfo("HF0013420230313", "总进水量", "m³/h"),
                new IndicatorInfo("HF0008820230313", "栅前液位", "m"),
                new IndicatorInfo("HF1015320230720", "泵前1#", "m"),
                new IndicatorInfo("HF1019820230720", "1#累计流量", "m³/h"),
                new IndicatorInfo("HF1019420230720", "2#累计流量", "m³/h"),
                new IndicatorInfo("HF1019020230720", "3#累计流量", "m³/h"),
                new IndicatorInfo("HF1018620230720", "4#累计流量", "m³/h"),
                new IndicatorInfo("HF1018220230720", "5#累计流量", "m³/h"),
                new IndicatorInfo("HF1017720230720", "6#累计流量", "m³/h"),
                new IndicatorInfo("HF1017320230720", "7#累计流量", "m³/h"),
                new IndicatorInfo("HF1016920230720", "8#累计流量", "m³/h"),
                new IndicatorInfo("HF1016520230720", "9#累计流量", "m³/h"),
                new IndicatorInfo("HF1016120230720", "10#累计流量", "m³/h"),
                new IndicatorInfo("HF1019720230720", "1#进水流量", "m³/h"),
                new IndicatorInfo("HF1019320230720", "2#进水流量", "m³/h"),
                new IndicatorInfo("HF1018920230720", "3#进水流量", "m³/h"),
                new IndicatorInfo("HF1018520230720", "4#进水流量", "m³/h"),
                new IndicatorInfo("HF1018120230720", "5#进水流量", "m³/h"),
                new IndicatorInfo("HF1017620230720", "6#进水流量", "m³/h"),
                new IndicatorInfo("HF1017220230720", "7#进水流量", "m³/h"),
                new IndicatorInfo("HF1016820230720", "8#进水流量", "m³/h"),
                new IndicatorInfo("HF1016420230720", "9#进水流量", "m³/h"),
                new IndicatorInfo("HF1016020230720", "10#进水流量", "m³/h")
        );
        return ResponseData.ok(waterWorksInteractiveService.buildIndicatorSection("粗中格栅间及进水泵房", list));
    }

    @GetMapping("hf/getHighPressureBlowerRoom")
    @ApiOperation("高压鼓风机房1#-12#(弹窗)")
    @ApiOperationSupport(order = 2)
    public ResponseData<PopUpNotificationVo> getHighPressureBlowerRoom(@NotNull Integer code) {
        List<IndicatorInfo> list = null;
        switch (code) {
            case 1:
                list = Arrays.asList(
                        new IndicatorInfo("HF0911320230720", code + "#出口压力", "kpa"),
                        new IndicatorInfo("HF0909820230720", code + "#驱动端轴承温度", "℃"),
                        new IndicatorInfo("HF0909720230720", code + "#非驱动端温度", "℃"),
                        new IndicatorInfo("HF0911020230720", code + "#进口导叶开度", ""),
                        new IndicatorInfo("HF0911320230720", code + "#出口导叶开度", "")
                );
                break;
            case 2:
                list = Arrays.asList(
                        new IndicatorInfo("HF0913520230720", code + "#出口压力", "kpa"),
                        new IndicatorInfo("HF0912220230720", code + "#驱动端轴承温度", "℃"),
                        new IndicatorInfo("HF0912320230720", code + "#非驱动端温度", "℃"),
                        new IndicatorInfo("HF0913720230720", code + "#进口导叶开度", ""),
                        new IndicatorInfo("HF0913320230720", code + "#出口导叶开度", "")
                );
                break;
            case 3:
                list = Arrays.asList(
                        new IndicatorInfo("HF0915720230720", code + "#出口压力", "kpa"),
                        new IndicatorInfo("HF0914420230720", code + "#驱动端轴承温度", "℃"),
                        new IndicatorInfo("HF0914520230720", code + "#非驱动端温度", "℃"),
                        new IndicatorInfo("HF0915420230720", code + "#进口导叶开度", ""),
                        new IndicatorInfo("HF0915520230720", code + "#出口导叶开度", "")
                );
                break;
            case 4:
                list = Arrays.asList(
                        new IndicatorInfo("HF0917920230720", code + "#出口压力", "kpa"),
                        new IndicatorInfo("HF0916620230720", code + "#驱动端轴承温度", "℃"),
                        new IndicatorInfo("HF0916720230720", code + "#非驱动端温度", "℃"),
                        new IndicatorInfo("HF0917620230720", code + "#进口导叶开度", ""),
                        new IndicatorInfo("HF0917720230720", code + "#出口导叶开度", "")
                );
                break;
            case 5:
                list = Arrays.asList(
                        new IndicatorInfo("HF0919120230720", code + "#出口压力", "kpa"),
                        new IndicatorInfo("HF0920120230720", code + "#驱动端轴承温度", "℃"),
                        new IndicatorInfo("HF0920220230720", code + "#非驱动端温度", "℃"),
                        new IndicatorInfo("HF0918820230720", code + "#进口导叶开度", ""),
                        new IndicatorInfo("HF0918920230720", code + "#出口导叶开度", "")
                );
                break;
            case 6:
                list = Arrays.asList(
                        new IndicatorInfo("HF0920720230720", code + "#出口压力", "kpa"),
                        new IndicatorInfo("HF0921420230720", code + "#驱动端轴承温度", "℃"),
                        new IndicatorInfo("HF0921320230720", code + "#非驱动端温度", "℃"),
                        new IndicatorInfo("HF0920420230720", code + "#进口导叶开度", ""),
                        new IndicatorInfo("HF0920520230720", code + "#出口导叶开度", "")
                );
                break;
            case 7:
                list = Arrays.asList(
                        new IndicatorInfo("HF0923220230720", code + "#出口压力", "kpa"),
                        new IndicatorInfo("HF0923620230720", code + "#驱动端轴承温度", "℃"),
                        new IndicatorInfo("HF0923820230720", code + "#非驱动端温度", "℃"),
                        new IndicatorInfo("HF0924020230720", code + "#进口导叶开度", ""),
                        new IndicatorInfo("HF0923020230720", code + "#出口导叶开度", "")
                );
                break;
            case 8:
                list = Arrays.asList(
                        new IndicatorInfo("HF0926620230720", code + "#出口压力", "kpa"),
                        new IndicatorInfo("HF0925520230720", code + "#驱动端轴承温度", "℃"),
                        new IndicatorInfo("HF0926820230720", code + "#非驱动端温度", "℃"),
                        new IndicatorInfo("HF0926320230720", code + "#进口导叶开度", ""),
                        new IndicatorInfo("HF0926420230720", code + "#出口导叶开度", "")
                );
                break;
            case 9:
                list = Arrays.asList(
                        new IndicatorInfo("HF0929020230720", code + "#出口压力", "kpa"),
                        new IndicatorInfo("HF0927920230720", code + "#驱动端轴承温度", "℃"),
                        new IndicatorInfo("HF0927820230720", code + "#非驱动端温度", "℃"),
                        new IndicatorInfo("HF0927220230720", code + "#进口导叶开度", ""),
                        new IndicatorInfo("HF0927120230720", code + "#出口导叶开度", "")
                );
                break;
            case 10:
                list = Arrays.asList(
                        new IndicatorInfo("HF1371420230720", code + "#出口压力", "kpa"),
                        new IndicatorInfo("HF1372420230720", code + "#驱动端轴承温度", "℃"),
                        new IndicatorInfo("HF1372520230720", code + "#非驱动端温度", "℃"),
                        new IndicatorInfo("HF1373220230720", code + "#进口导叶开度", ""),
                        new IndicatorInfo("HF1373320230720", code + "#出口导叶开度", "")
                );
                break;
            case 11:
                list = Arrays.asList(
                        new IndicatorInfo("HF1373820230720", code + "#出口压力", "kpa"),
                        new IndicatorInfo("HF1374620230720", code + "#驱动端轴承温度", "℃"),
                        new IndicatorInfo("HF1374720230720", code + "#非驱动端温度", "℃"),
                        new IndicatorInfo("HF1373520230720", code + "#进口导叶开度", ""),
                        new IndicatorInfo("HF1373620230720", code + "#出口导叶开度", "")
                );
                break;
            case 12:
                list = Arrays.asList(
                        new IndicatorInfo("HF1377520230720", code + "#出口压力", "kpa"),
                        new IndicatorInfo("HF1376220230720", code + "#驱动端轴承温度", "℃"),
                        new IndicatorInfo("HF1376320230720", code + "#非驱动端温度", "℃"),
                        new IndicatorInfo("HF1377220230720", code + "#进口导叶开度", ""),
                        new IndicatorInfo("HF1377320230720", code + "#出口导叶开度", "")
                );
                break;

        }
        return ResponseData.ok(waterWorksInteractiveService.buildIndicatorSection("高压鼓风机房", list));

    }

    @GetMapping("hf/getBiologicalTank")
    @ApiOperation("生物池A-D(弹窗)")
    @ApiOperationSupport(order = 3)
    public ResponseData<PopUpNotificationVo> getBiologicalTank(@NotNull String code) {
        List<IndicatorInfo> list = null;
        switch (code) {
            case "A":
                list = Arrays.asList(
                        new IndicatorInfo("HF0263120230720", "1#ORP", "mV"),
                        new IndicatorInfo("HF0263020230720", "2#ORP", "mV"),
                        new IndicatorInfo("HF0262920230720", "3#ORP", "mV"),
                        new IndicatorInfo("HF0262820230720", "4#ORP", "mV")
                );
                break;
            case "B":
                list = Arrays.asList(
                        new IndicatorInfo("HF0333020230720", "1#ORP", "mV"),
                        new IndicatorInfo("HF0333120230720", "2#ORP", "mV"),
                        new IndicatorInfo("HF0333220230720", "3#ORP", "mV"),
                        new IndicatorInfo("HF0333320230720", "4#ORP", "mV")
                );
                break;
            case "C":
                list = Arrays.asList(
                        new IndicatorInfo("HF0378320230720", "1#ORP", "mV"),
                        new IndicatorInfo("HF0378420230720", "2#ORP", "mV"),
                        new IndicatorInfo("HF0378520230720", "3#ORP", "mV"),
                        new IndicatorInfo("HF0378620230720", "4#ORP", "mV")
                );
                break;
            case "D":
                list = Arrays.asList(
                        new IndicatorInfo("HF0448520230720", "1#ORP", "mV"),
                        new IndicatorInfo("HF0448620230720", "2#ORP", "mV"),
                        new IndicatorInfo("HF0448720230720", "3#ORP", "mV"),
                        new IndicatorInfo("HF0448820230720", "4#ORP", "mV")
                );
                break;
        }
        return ResponseData.ok(waterWorksInteractiveService.buildIndicatorSection("生物池" + code, list));

    }

    @GetMapping("hf/getPrimarySedimentationTank")
    @ApiOperation("初沉池-槐房(弹窗)")
    @ApiOperationSupport(order = 4)
    public ResponseData<PopUpNotificationVo> getPrimarySedimentationTank() {
        // 2 16
        List<IndicatorInfo> list = Arrays.asList(
                new IndicatorInfo("HF1160720230720", "1#排泥泵运行"),
                new IndicatorInfo("HF1160120230720", "2#排泥泵运行"),
                new IndicatorInfo("HF1159320230720", "3#排泥泵运行"),
                new IndicatorInfo("HF1158520230720", "4#排泥泵运行"),
                new IndicatorInfo("HF1157720230720", "5#排泥泵运行"),
                new IndicatorInfo("HF1156920230720", "6#排泥泵运行"),
                new IndicatorInfo("HF1156120230720", "7#排泥泵运行"),
                new IndicatorInfo("HF1155320230720", "8#排泥泵运行"),
                new IndicatorInfo("HF1154520230720", "9#排泥泵运行"),
                new IndicatorInfo("HF1153720230720", "10#排泥泵运行"),
                new IndicatorInfo("HF1152920230720", "11#排泥泵运行"),
                new IndicatorInfo("HF1152120230720", "12#排泥泵运行"),
                new IndicatorInfo("HF1151320230720", "13#排泥泵运行"),
                new IndicatorInfo("HF1150520230720", "14#排泥泵运行"),
                new IndicatorInfo("HF1149720230720", "15#排泥泵运行"),
                new IndicatorInfo("HF1148920230720", "16#排泥泵运行")
        );
        return ResponseData.ok(waterWorksInteractiveService.buildIndicatorSection("初沉池", list));
    }

    @GetMapping("hf/getBetweenTheFineGrilles")
    @ApiOperation("细格栅间(弹窗)")
    @ApiOperationSupport(order = 5)
    public ResponseData<PopUpNotificationVo> getBetweenTheFineGrilles() {
        List<IndicatorInfo> list = Arrays.asList(
                new IndicatorInfo("HF0102520230720", "1#格栅前液位", "m"),
                new IndicatorInfo("HF0102620230720", "1#格栅后液位", "m"),
                new IndicatorInfo("HF0102820230720", "2#格栅前液位", "m"),
                new IndicatorInfo("HF0102720230720", "2#格栅后液位", "m"),
                new IndicatorInfo("HF0103820230720", "沉砂池液位1", "m"),
                new IndicatorInfo("HF0103920230720", "沉砂池液位2", "m"),
                new IndicatorInfo("HF0104020230720", "沉砂池液位3", "m")
        );
        return ResponseData.ok(waterWorksInteractiveService.buildIndicatorSection("细格栅间", list));
    }

    @GetMapping("hf/getMembranePool")
    @ApiOperation("膜池A-D(弹窗)")
    @ApiOperationSupport(order = 6)
    public ResponseData<PopUpNotificationVo> getMembranePool(@NotNull String code) {
        List<IndicatorInfo> list = null;
        switch (code) {
            case "A":
                list = Arrays.asList(
                        new IndicatorInfo("HF0577020230720", "1#压力", "kpa"),
                        new IndicatorInfo("HF0579520230720", "2#流量", "m³/h"),
                        new IndicatorInfo("HF0566620230720", "1#液位", "m"),
                        new IndicatorInfo("HF0574920230720", "1#吹扫压力", "kpa"),
                        new IndicatorInfo("HF0553020230720", "1#透水率", "%"),
                        new IndicatorInfo("HF0569520230720", "1#跨膜压差", "kpa"),
                        new IndicatorInfo("HF0517120230720", "1#曝气流量", "m³/h")
                );
                break;
            case "B":
                list = Arrays.asList(
                        new IndicatorInfo("HF0613920230720", "1#压力", "kpa"),
                        new IndicatorInfo("HF0611420230720", "2#流量", "m³/h"),
                        new IndicatorInfo("HF0659520230720", "1#液位", "m"),
                        new IndicatorInfo("HF0616020230720", "1#吹扫压力", "kpa"),
                        new IndicatorInfo("HF0645020230720", "1#透水率", "%"),
                        new IndicatorInfo("HF0621420230720", "1#跨膜压差", "kpa"),
                        new IndicatorInfo("HF0592920230720", "1#曝气流量", "m³/h")
                );
                break;
            case "C":
                list = Arrays.asList(
                        new IndicatorInfo("HF0711520230720", "1#压力", "kpa"),
                        new IndicatorInfo("HF0709020230720", "2#流量", "m³/h"),
                        new IndicatorInfo("HF0722020230720", "1#液位", "m"),
                        new IndicatorInfo("HF0713620230720", "1#吹扫压力", "kpa"),
                        new IndicatorInfo("HF0723120230720", "1#透水率", "%"),
                        new IndicatorInfo("HF0718920230720", "1#跨膜压差", "kpa"),
                        new IndicatorInfo("HF0663720230720", "1#曝气流量", "m³/h")
                );
                break;
            case "D":
                list = Arrays.asList(
                        new IndicatorInfo("HF0754520230720", "1#压力", "kpa"),
                        new IndicatorInfo("HF0757120230720", "2#流量", "m³/h"),
                        new IndicatorInfo("HF0743720230720", "1#液位", "m"),
                        new IndicatorInfo("HF0752320230720", "1#吹扫压力", "kpa"),
                        new IndicatorInfo("HF0742420230720", "1#透水率", "%"),
                        new IndicatorInfo("HF0746720230720", "1#跨膜压差", "kpa"),
                        new IndicatorInfo("HF0734720230720", "1#曝气流量", "m³/h")
                );
                break;
        }
        return ResponseData.ok(waterWorksInteractiveService.buildIndicatorSection("膜池" + code, list));

    }

    @GetMapping("hf/getOzonePreparationRoom")
    @ApiOperation("臭氧制备间(弹窗)")
    @ApiOperationSupport(order = 7)
    public ResponseData<PopUpNotificationVo> getOzonePreparationRoom() {
        List<IndicatorInfo> list = Collections.singletonList(
                new IndicatorInfo("HF0837620230720", "主管臭氧量", "mg/l")
        );
        return ResponseData.ok(waterWorksInteractiveService.buildIndicatorSection("臭氧制备间", list));
    }

    @GetMapping("hf/getHotHydrolysis")
    @ApiOperation("热水解(弹窗)")
    @ApiOperationSupport(order = 8)
    public ResponseData<PopUpNotificationVo> getHotHydrolysis() {
        List<IndicatorInfo> list = Arrays.asList(
                new IndicatorInfo("HF3219120230720", "热水解1 浆化罐液位（%）", "%"),
                new IndicatorInfo("HF3220020230720", "热水解2 浆化罐液位（%）", "%"),
                new IndicatorInfo("HF3220920230720", "热水解3 浆化罐液位（%）", "%"),
                new IndicatorInfo("HF3221820230720", "热水解4 浆化罐液位（%）", "%")
        );
        return ResponseData.ok(waterWorksInteractiveService.buildIndicatorSection("热水解", list));
    }

    @GetMapping("hf/getExternalSludgeTransfer")
    @ApiOperation("外接泥转运(弹窗)")
    @ApiOperationSupport(order = 9)
    public ResponseData<PopUpNotificationVo> getExternalSludgeTransfer() {
        List<IndicatorInfo> list = Arrays.asList(
                new IndicatorInfo("HF3064920230720", "1#大料斗料位", "m"),
                new IndicatorInfo("HF3066520230720", "2#大料斗料位", "m"),
                new IndicatorInfo("HF3068220230720", "3#大料斗料位", "m")
        );
        return ResponseData.ok(waterWorksInteractiveService.buildIndicatorSection("外接泥转运", list));
    }

    @GetMapping("hf/getPreDehydrationArea")
    @ApiOperation("预脱水区域(弹窗)")
    @ApiOperationSupport(order = 10)
    public ResponseData<PopUpNotificationVo2> getPreDehydrationArea() {
        return ResponseData.ok(waterWorksInteractiveService.getPreDehydrationArea("预脱水区域"));
    }

    @GetMapping("hf/getBiogasDigester")
    @ApiOperation("沼气池(弹窗)")
    @ApiOperationSupport(order = 11)
    public ResponseData<PopUpNotificationVo> getBiogasDigester() {
        List<IndicatorInfo> list = Arrays.asList(
                new IndicatorInfo("HF3438220230720", "沼气总管流量", "m³/h"),
                new IndicatorInfo("HF3439020230720", "沼气总管压力", "kpa"),
                new IndicatorInfo("HF3438120230720", "沼气总管累计流量", "m³"),
                new IndicatorInfo("HF3428620230720", "1#沼气柜液位", "m³"),
                new IndicatorInfo("HF3430520230720", "2#沼气柜液位", "m³"),
                new IndicatorInfo("HF3432420230720", "3#沼气柜液位", "m³")
        );
        return ResponseData.ok(waterWorksInteractiveService.buildIndicatorSection("沼气池", list));
    }

    @GetMapping("gbd/getMainIntakePumpRoom")
    @ApiOperation("总进水泵房(弹窗)")
    @ApiOperationSupport(order = 12)
    public ResponseData<PopUpNotificationVo> getMainIntakePumpRoom() {
        List<IndicatorInfo> list = Arrays.asList(
                new IndicatorInfo("GBD1919120220608", "191闸1#液位", "m"),
                new IndicatorInfo("GBD1919320220608", "192闸2#液位", "m"),
                new IndicatorInfo("GBD2030320230730", "泵前池东侧液位", "m"),
                new IndicatorInfo("GBD2030420230730", "泵前池西侧液位", "m"),
                new IndicatorInfo("GBD2075020230720", "处理水量m³/s", "m³/s"),
                new IndicatorInfo("GBD2031120230730", "进水泵房-跨越溢流量", "m³/h")
        );
        return ResponseData.ok(waterWorksInteractiveService.buildIndicatorSection("总进水泵房", list));
    }

    @GetMapping("gbd/getAerationGritChamber")
    @ApiOperation("曝气沉砂池(一 - 二)(弹窗)")
    @ApiOperationSupport(order = 13)
    public ResponseData<PopUpNotificationVo> getAerationGritChamber(@NotNull String code) {
        List<IndicatorInfo> list = null;
        switch (code) {
            case "一":
                list = Arrays.asList(
                        new IndicatorInfo("GBD2001320230730", "1系列管道温度", "℃"),
                        new IndicatorInfo("GBD2001420230730", "1系列曝气流量", "m³/h"),
                        new IndicatorInfo("GBD2001520230730", "1系列压力", "kpa")
                );
                break;
            case "二":
                list = Arrays.asList(
                        new IndicatorInfo("GBD2001020230730", "2系列管道温度", "℃"),
                        new IndicatorInfo("GBD2032120230730", "2系列曝气流量", "m³/h"),
                        new IndicatorInfo("GBD2001220230730", "2系列压力", "kpa")
                );
                break;
        }
        return ResponseData.ok(waterWorksInteractiveService.buildIndicatorSection("曝气沉砂池(" + code + "系列)" + code, list));

    }

    @GetMapping("gbd/getPrimarySedimentationTank")
    @ApiOperation("初沉池-高碑店(一 - 四)(弹窗)")
    @ApiOperationSupport(order = 14)
    public ResponseData<PopUpNotificationVo> getPrimarySedimentationTankGbd(@NotNull String code) {
        List<IndicatorInfo> list = null;
        switch (code) {
            case "一":
                list = Collections.singletonList(
                        new IndicatorInfo("GBD2101220230720", "初沉池1系列-ORP")
                );
                break;
            case "二 ":
                list = Arrays.asList(
                        new IndicatorInfo("GBD2102920230720", "初沉池2系列-ORP"),
                        new IndicatorInfo("GBD2102520230720", "进水硝氮1", "mg/L"),
                        new IndicatorInfo("GBD2102620230720", "进水硝氮2", "mg/L"),
                        new IndicatorInfo("GBD2102720230720", "出水硝氮1", "mg/L"),
                        new IndicatorInfo("GBD2102820230720", "出水硝氮2", "mg/L")
                );
                break;
            case "三":
                list = Arrays.asList(
                        new IndicatorInfo("GBD2568120230720", "13_14污泥浓度", "mg/L"),
                        new IndicatorInfo("GBD2568220230720", "15_16污泥浓度", "mg/L"),
                        new IndicatorInfo("GBD2568420230720", "3系列流量", "m³/s"),
                        new IndicatorInfo("GBD2568320230720", "3系列累计流量", "m³"),
                        new IndicatorInfo("GBD2555420230720 ", "旁通流量", "mg/s")
                );
                break;
            case "四":
                list = Arrays.asList(
                        new IndicatorInfo("GBD2555520230720", "3系列-ORP"),
                        new IndicatorInfo("GBD2572220230720", "19_20污泥浓度", "mg/L"),
                        new IndicatorInfo("GBD2572320230720", "21_22污泥浓度", "mg/L"),
                        new IndicatorInfo("GBD2572120230720", "4系列累计流量", "m³/s"),
                        new IndicatorInfo("GBD2556820230720", "4系列旁通流量", "m³/s")
                );
                break;
        }
        return ResponseData.ok(waterWorksInteractiveService.buildIndicatorSection("初沉池(" + code + "系列)" + code, list));

    }

    @GetMapping("gbd/getAerationTank")
    @ApiOperation("曝气池-高碑店(一 - 四)(弹窗)")
    @ApiOperationSupport(order = 15)
    public ResponseData<PopUpNotificationVo> getAerationTank(@NotNull String code) {
        List<IndicatorInfo> list = null;
        switch (code) {
            case "一":
                list = Arrays.asList(
                        new IndicatorInfo("GBD2034120230730", "1#溶解氧", "mg/L"),
                        new IndicatorInfo("GBD2034720230730", "2#溶解氧", "mg/L"),
                        new IndicatorInfo("GBD2035420230730", "3#溶解氧", "mg/L"),
                        new IndicatorInfo("GBD2036120230730", "4#溶解氧", "mg/L"),
                        new IndicatorInfo("GBD2036720230730", "5#溶解氧", "mg/L"),
                        new IndicatorInfo("GBD2037220230730", "6#溶解氧", "mg/L"),
                        new IndicatorInfo("GBD2036520230730", "污泥浓度", "mg/L")
                );
                break;
            case "二":
                list = Arrays.asList(
                        new IndicatorInfo("GBD2518420230720", "7#溶解氧", "mg/L"),
                        new IndicatorInfo("GBD2518520230720", "8#溶解氧", "mg/L"),
                        new IndicatorInfo("GBD2518620230720", "9#溶解氧", "mg/L"),
                        new IndicatorInfo("GBD2518720230720", "10#溶解氧", "mg/L"),
                        new IndicatorInfo("GBD2518820230720", "11#溶解氧", "mg/L"),
                        new IndicatorInfo("GBD2518920230720", "12#溶解氧", "mg/L"),
                        new IndicatorInfo("GBD2519020230720", "污泥浓度", "mg/L")
                );
                break;
            case "三":
                list = Arrays.asList(
                        new IndicatorInfo("GBD2584820230720", "13#溶解氧", "mg/L"),
                        new IndicatorInfo("GBD2584920230720", "14#溶解氧", "mg/L"),
                        new IndicatorInfo("GBD2585020230720", "15#溶解氧", "mg/L"),
                        new IndicatorInfo("GBD2585120230720", "16#溶解氧", "mg/L"),
                        new IndicatorInfo("GBD2585220230720", "17#溶解氧", "mg/L"),
                        new IndicatorInfo("GBD2585320230720", "18#溶解氧", "mg/L"),
                        new IndicatorInfo("GBD2585520230720", "污泥浓度", "mg/L")
                );
                break;
            case "四":
                list = Arrays.asList(
                        new IndicatorInfo("GBD2597620230720", "19#溶解氧", "mg/L"),
                        new IndicatorInfo("GBD2597720230720", "20#溶解氧", "mg/L"),
                        new IndicatorInfo("GBD2597820230720", "21#溶解氧", "mg/L"),
                        new IndicatorInfo("GBD2597920230720", "22#溶解氧", "mg/L"),
                        new IndicatorInfo("GBD2598020230720", "23#溶解氧", "mg/L"),
                        new IndicatorInfo("GBD2598120230720", "24#溶解氧", "mg/L")
                );
                break;
        }
        return ResponseData.ok(waterWorksInteractiveService.buildIndicatorSection("曝气池(" + code + "系列)" + code, list));

    }

    @GetMapping("gbd/getBlowerRoom")
    @ApiOperation("鼓风机房(一 - 二)(弹窗)")
    @ApiOperationSupport(order = 16)
    public ResponseData<PopUpNotificationVo> getBlowerRoom(@NotNull String code) {
        List<IndicatorInfo> list = null;
        switch (code) {
            case "一":
                list = Arrays.asList(
                        new IndicatorInfo("GBD2073120230730", "总管压力", "kpa"),
                        new IndicatorInfo("GBD2073020230730", "总管流量", "m³/h")
                );
                break;
            case "二":
                list = Arrays.asList(
                        new IndicatorInfo("GBD2602920230720", "总管压力", "kpa"),
                        new IndicatorInfo("GBD2602720230720", "南管道流量", "m³/h"),
                        new IndicatorInfo("GBD2602820230720", "北管道流量", "m³/h")
                );
                break;
        }
        return ResponseData.ok(waterWorksInteractiveService.buildIndicatorSection("鼓风机房(" + code + "系列)" + code, list));

    }


    @GetMapping("gbd/getDenitrificationFilter")
    @ApiOperation("反硝化滤池(弹窗)")
    @ApiOperationSupport(order = 17)
    public ResponseData<PopUpNotificationVo> getDenitrificationFilter() {
        List<IndicatorInfo> list = Collections.singletonList(
                new IndicatorInfo("GBD3012320230720", "1#液位", "m")
        );
        PopUpNotificationVo vo = waterWorksInteractiveService.buildIndicatorSection("反硝化滤池", list);
        List<PopUpNotificationVo.KeyValue> data = vo.getData().get(0).getData();
        data.add(0, new PopUpNotificationVo.KeyValue("二系列", ""));
        data.add(1, new PopUpNotificationVo.KeyValue("一系列", ""));
        return ResponseData.ok(vo);
    }

    @GetMapping("gbd/getOzone")
    @ApiOperation("臭氧(弹窗)")
    @ApiOperationSupport(order = 18)
    public ResponseData<PopUpNotificationVo> getOzone() {
        // 2 16
        List<IndicatorInfo> list = Arrays.asList(
                new IndicatorInfo("GBD3574920230720", "1#功率", "kW"),
                new IndicatorInfo("GBD3575120230720", "1#浓度", "mg/L"),
                new IndicatorInfo("GBD3575220230720", "1#流量", "m³/h"),
                new IndicatorInfo("GBD3575320230720", "1#压力", "m³"),
                new IndicatorInfo("GBD3584820230720", "1#投加流量", "m³/h"),
                new IndicatorInfo("GBD3584920230720", "1#溶水浓度", "ppm"),
                new IndicatorInfo("GBD3585020230720", "1#尾气浓度", "mg/L")
        );
        return ResponseData.ok(waterWorksInteractiveService.buildIndicatorSection("臭氧", list));
    }

    @GetMapping("gbd/getUltraviolet")
    @ApiOperation("紫外(弹窗)")
    @ApiOperationSupport(order = 19)
    public ResponseData<PopUpNotificationVo> getUltraviolet() {
        List<IndicatorInfo> list = Arrays.asList(
                new IndicatorInfo("GBD3600920230720", "1#水位", "cm"),
                new IndicatorInfo("GBD3600820230720", "1#流量", "m³/day"),
                new IndicatorInfo("GBD3601620230720", "1#开度", "%"),
                new IndicatorInfo("GBD3602220230720", "2#水位", "cm"),
                new IndicatorInfo("GBD3602120230720", "2#流量", "m³/day"),
                new IndicatorInfo("GBD3602920230720", "2#开度", "%"),
                new IndicatorInfo("GBD3603520230720", "3#水位", "cm"),
                new IndicatorInfo("GBD3603420230720", "3#流量", "m³/day"),
                new IndicatorInfo("GBD3604220230720", "3#开度", "%"),
                new IndicatorInfo("GBD3592420230720", "4#水位", "cm"),
                new IndicatorInfo("GBD3592320230720", "4#流量", "m³/day"),
                new IndicatorInfo("GBD3593120230720", "4#开度", "%"),
                new IndicatorInfo("GBD3593720230720", "5#水位", "cm"),
                new IndicatorInfo("GBD3593620230720", "5#流量", "m³/day"),
                new IndicatorInfo("GBD3594320230720", "5#开度", "%"),
                new IndicatorInfo("GBD3594920230720", "6#水位", "cm"),
                new IndicatorInfo("GBD3594820230720", "6#流量", "m³/day"),
                new IndicatorInfo("GBD3595520230720", "6#开度", "%"),
                new IndicatorInfo("GBD3596120230720", "7#水位", "cm"),
                new IndicatorInfo("GBD3596020230720", "7#流量", "m³/day"),
                new IndicatorInfo("GBD3596720230720", "7#开度", "%"),
                new IndicatorInfo("GBD3597320230720", "8#水位", "cm"),
                new IndicatorInfo("GBD3597220230720", "8#流量", "m³/day"),
                new IndicatorInfo("GBD3597920230720", "8#开度", "%"),
                new IndicatorInfo("GBD3598520230720", "9#水位", "cm"),
                new IndicatorInfo("GBD3598420230720", "9#流量", "m³/day"),
                new IndicatorInfo("GBD3599120230720", "9#开度", "%"),
                new IndicatorInfo("GBD3599720230720", "10#水位", "cm"),
                new IndicatorInfo("GBD3599620230720", "10#流量", "m³/day"),
                new IndicatorInfo("GBD3600320230720", "10#开度", "%")
        );
        return ResponseData.ok(waterWorksInteractiveService.buildIndicatorSection("紫外", list));
    }

    @GetMapping("gbd/getTheMixerIsInOperation")
    @ApiOperation("搅拌机运行(1#-8#)(弹窗)")
    @ApiOperationSupport(order = 20)
    public ResponseData<PopUpNotificationVo> getTheMixerIsInOperation(@NotNull Integer code) {
        List<IndicatorInfo> list = null;
        switch (code) {
            case 1:
                list = Arrays.asList(
                        new IndicatorInfo("GBD4006220230720", code + "#顶部温度", "℃"),
                        new IndicatorInfo("GBD4005720230720", code + "#进泥流量", "m³/h"),
                        new IndicatorInfo("GBD4006520230720", code + "#排泥流量", "m³/h"),
                        new IndicatorInfo("GBD4006920230720", code + "#沼气流量", "m³/h"),
                        new IndicatorInfo("GBD4006320230720", code + "#底部温度", "℃")
                );
                break;
            case 2:
                list = Arrays.asList(
                        new IndicatorInfo("GBD4018020230720", code + "#顶部温度", "℃"),
                        new IndicatorInfo("GBD4017520230720", code + "#进泥流量", "m³/h"),
                        new IndicatorInfo("GBD4018320230720", code + "#排泥流量", "m³/h"),
                        new IndicatorInfo("GBD4018720230720", code + "#沼气流量", "m³/h"),
                        new IndicatorInfo("GBD4018120230720", code + "#底部温度", "℃")
                );
                break;
            case 3:
                list = Arrays.asList(
                        new IndicatorInfo("GBD4029820230720", code + "#顶部温度", "℃"),
                        new IndicatorInfo("GBD4029320230720", code + "#进泥流量", "m³/h"),
                        new IndicatorInfo("GBD4030120230720", code + "#排泥流量", "m³/h"),
                        new IndicatorInfo("GBD4030520230720", code + "#沼气流量", "m³/h"),
                        new IndicatorInfo("GBD4029920230720", code + "#底部温度", "℃")
                );
                break;
            case 4:
                list = Arrays.asList(
                        new IndicatorInfo("GBD4041620230720", code + "#顶部温度", "℃"),
                        new IndicatorInfo("GBD4041120230720", code + "#进泥流量", "m³/h"),
                        new IndicatorInfo("GBD4041920230720", code + "#排泥流量", "m³/h"),
                        new IndicatorInfo("GBD4042320230720", code + "#沼气流量", "m³/h"),
                        new IndicatorInfo("GBD4041720230720", code + "#底部温度", "℃")
                );
                break;
            case 5:
                list = Arrays.asList(
                        new IndicatorInfo("GBD4053420230720", code + "#顶部温度", "℃"),
                        new IndicatorInfo("GBD4052920230720", code + "#进泥流量", "m³/h"),
                        new IndicatorInfo("GBD4054120230720", code + "#排泥流量", "m³/h"),
                        new IndicatorInfo("GBD4053720230720", code + "#沼气流量", "m³/h"),
                        new IndicatorInfo("GBD4053520230720", code + "#底部温度", "℃")
                );
                break;
            case 6:
                list = Arrays.asList(
                        new IndicatorInfo("GBD4065220230720", code + "#顶部温度", "℃"),
                        new IndicatorInfo("GBD4064720230720", code + "#进泥流量", "m³/h"),
                        new IndicatorInfo("GBD4065520230720", code + "#排泥流量", "m³/h"),
                        new IndicatorInfo("GBD4065920230720", code + "#沼气流量", "m³/h"),
                        new IndicatorInfo("GBD4065320230720", code + "#底部温度", "℃")
                );
                break;
            case 7:
                list = Arrays.asList(
                        new IndicatorInfo("GBD4077020230720", code + "#顶部温度", "℃"),
                        new IndicatorInfo("GBD4076520230720", code + "#进泥流量", "m³/h"),
                        new IndicatorInfo("GBD4077320230720", code + "#排泥流量", "m³/h"),
                        new IndicatorInfo("GBD4077720230720", code + "#沼气流量", "m³/h"),
                        new IndicatorInfo("GBD4077120230720", code + "#底部温度", "℃")
                );
                break;
            case 8:
                list = Arrays.asList(
                        new IndicatorInfo("GBD4088820230720", code + "#顶部温度", "℃"),
                        new IndicatorInfo("GBD4088320230720", code + "#进泥流量", "m³/h"),
                        new IndicatorInfo("GBD4089120230720", code + "#排泥流量", "m³/h"),
                        new IndicatorInfo("GBD4089120230720", code + "#沼气流量", "m³/h"),
                        new IndicatorInfo("GBD4088920230720", code + "#底部温度", "℃")
                );
                break;
        }
        return ResponseData.ok(waterWorksInteractiveService.buildIndicatorSection(code + "#搅拌机运行", list));

    }

    @GetMapping("gbd/getDryDesulfurization")
    @ApiOperation("干式脱硫(1#-8#)(弹窗)")
    @ApiOperationSupport(order = 21)
    public ResponseData<PopUpNotificationVo> getDryDesulfurization(@NotNull Integer code) {
        List<IndicatorInfo> list = null;
        switch (code) {
            case 1:
                list = Arrays.asList(
                        new IndicatorInfo("GBD4097920230720", code + "#沼气柜柜位", "m³"),
                        new IndicatorInfo("GBD4098120230720", code + "#沼气柜温度", "m³"),
                        new IndicatorInfo("GBD4098020230720", code + "#沼气柜压力", "kpa")
                );
                break;
            case 2:
                list = Arrays.asList(
                        new IndicatorInfo("GBD4098620230720", code + "#沼气柜柜位", "m³"),
                        new IndicatorInfo("GBD4098820230720", code + "#沼气柜温度", "m³"),
                        new IndicatorInfo("GBD4098720230720", code + "#沼气柜压力", "kpa")
                );
                break;
            case 3:
                list = Arrays.asList(
                        new IndicatorInfo("GBD4099320230720", code + "#沼气柜柜位", "m³"),
                        new IndicatorInfo("GBD4099520230720", code + "#沼气柜温度", "m³"),
                        new IndicatorInfo("GBD4099420230720", code + "#沼气柜压力", "kpa")
                );
                break;
        }
        return ResponseData.ok(waterWorksInteractiveService.buildIndicatorSection("干式脱硫", list));

    }

    @GetMapping("gbd/getExternalSludgeTransfer")
    @ApiOperation("外接泥转运(弹窗)")
    @ApiOperationSupport(order = 22)
    public ResponseData<PopUpNotificationVo> getExternalSludgeTransferGdb() {
        List<IndicatorInfo> list = Arrays.asList(
                new IndicatorInfo("GBD4205420230720", "1#斗料位", "m"),
                new IndicatorInfo("GBD4205520230720", "2#斗料位", "m"),
                new IndicatorInfo("GBD4206620230720", "1#柱塞泵出口压力", "kpa"),
                new IndicatorInfo("GBD4206720230720", "2#柱塞泵出口压力", "kpa"),
                new IndicatorInfo("GBD4206420230720", "1#柱塞泵出口流量", "m³/h"),
                new IndicatorInfo("GBD4206520230720", "2#柱塞泵出口流量", "m³/h")
        );
        return ResponseData.ok(waterWorksInteractiveService.buildIndicatorSection("外接泥转运", list));
    }

    @GetMapping("gbd/getPreDehydrationMachineRoom")
    @ApiOperation("预脱水机房(弹窗)")
    @ApiOperationSupport(order = 23)
    public ResponseData<PopUpNotificationVo> getPreDehydrationMachineRoom() {
        List<IndicatorInfo> list = Arrays.asList(
                new IndicatorInfo("GBD4181320230720", "1#斗料位", "m"),
                new IndicatorInfo("GBD4181420230720", "2#斗料位", "m"),
                new IndicatorInfo("GBD4188720230720", "3#斗料位", ""),
                new IndicatorInfo("GBD4195220230720", "1#出泥流量", "m³/h"),
                new IndicatorInfo("GBD4195320230720", "2#出泥流量", "m³/h"),
                new IndicatorInfo("GBD4189120230720", "高碑店消化 污泥输送系统1号柱塞泵压力", "m³/h"),
                new IndicatorInfo("GBD4189320230720", "高碑店消化 污泥输送系统1号柱塞泵压力", "m³/h")
        );
        return ResponseData.ok(waterWorksInteractiveService.buildIndicatorSection("预脱水机房", list));
    }


    @Data
    public static class PopUpNotificationVo2 {
        String title;
        List<Section> data;

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        // 定义新格式的Section类
        public static class Section {
            String title;
            List<KeyValue> data;
        }

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class KeyValue {
            String title;
            String label;
            String value;
        }
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class IndicatorInfo {
        private String id;       // 指标ID
        private String label;    // 展示名
        private String unit;     // 单位，例如 m³/h, m 等

        public IndicatorInfo(String id, String label) {
            this.id = id;
            this.label = label;
            this.unit = "";
        }
    }


}
