package com.hjb.beijin_paishui_data_middleground.controller.manage.waterwork;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.waterwork.WaterworkLevelMonitoringEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.waterwork.WaterworkLevelMonitoring;
import com.hjb.beijin_paishui_data_middleground.service.szls.WaterworkLevelMonitoringService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @createDate 2025/7/30 16:58
 * @description 液位检测
 */
@RestController
@Api(tags = "数据填报-防汛调度-液位检测")
@RequestMapping("manage/waterwork/levelMonitoring")
@Validated
@Slf4j
public class WaterworkLevelMonitoringController {

    @Resource
    private WaterworkLevelMonitoringService service;

    @GetMapping("get")
    @ApiOperation("获取数据")
    public ResponseData<WaterworkLevelMonitoring> get() {

        return ResponseData.ok(service.getReportedData());
    }

    @PostMapping("save")
    @ApiOperation("保存数据")
    public ResponseData<Boolean> save(@RequestBody WaterworkLevelMonitoringEditDto editDto) {
        WaterworkLevelMonitoring one = service.getOne(
          new LambdaQueryWrapper<WaterworkLevelMonitoring>()
                  .eq(WaterworkLevelMonitoring::getIsReported, 1)
                  .eq(Objects.nonNull(editDto.getId()),
                          WaterworkLevelMonitoring::getId,
                          editDto.getId())
        );
        boolean save;
        if(Objects.isNull(one)) {
            // 保存
            one = new WaterworkLevelMonitoring();
            BeanUtils.copyProperties(editDto, one);
            one.setIsReported(1);
            one.setLevelMonitoring(JsonUtil.toJsonStr(editDto.getLevelMonitoringValue()));
            save = service.save(one);
        } else {
            // 修改
            BeanUtils.copyProperties(editDto, one);
            one.setIsReported(1);
            one.setLevelMonitoring(JsonUtil.toJsonStr(editDto.getLevelMonitoringValue()));
            save = service.updateById(one);
        }
        return save ? ResponseData.ok(true) : ResponseData.warn("保存失败");
    }
}
