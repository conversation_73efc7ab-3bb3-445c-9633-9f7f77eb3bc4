package com.hjb.beijin_paishui_data_middleground.controller.manage.drain;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainPipeNetworkAndFacilities;
import com.hjb.beijin_paishui_data_middleground.service.szls.DrainPipeNetworkAndFacilitiesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/1/10 上午9:29
 * @description 管网及设施营控制器
 */
@RestController
@Api(tags = "数据填报-排水设施-管网及设施相关接口")
@RequestMapping("manage/drain/pipeNetworkAndFacilities")
@Validated
@Slf4j
public class DrainPipeNetworkAndFacilitiesController {

    @Resource
    private DrainPipeNetworkAndFacilitiesService service;

    @GetMapping("get")
    @ApiOperation("获取数据")
    public ResponseData<DrainPipeNetworkAndFacilities> get() {

        return ResponseData.ok(service.getReportedData());
    }

    @PostMapping("save")
    @ApiOperation("保存数据")
    public ResponseData<Boolean> save(@RequestBody DrainPipeNetworkAndFacilities editDto) {
        DrainPipeNetworkAndFacilities one = service.getOne(
                new LambdaQueryWrapper<DrainPipeNetworkAndFacilities>()
                        .eq(DrainPipeNetworkAndFacilities::getIsReported, 1)
                        .eq(Objects.nonNull(editDto.getId()),
                                DrainPipeNetworkAndFacilities::getId,
                                editDto.getId())
        );
        boolean save;
        if (Objects.isNull(one)) {
            // 保存
            one = new DrainPipeNetworkAndFacilities();
            BeanUtils.copyProperties(editDto, one);
            one.setIsReported(1);
            save = service.save(one);
        } else {
            // 更新
            BeanUtils.copyProperties(editDto, one);
            one.setIsReported(1);
            save = service.updateById(one);
        }

        return save ? ResponseData.ok(true) :ResponseData.warn("保存失败");

    }



}
