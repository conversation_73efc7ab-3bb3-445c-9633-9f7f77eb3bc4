package com.hjb.beijin_paishui_data_middleground.controller.manage.drain;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.drain.DrainOperationalRiskEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainOperationalRisk;
import com.hjb.beijin_paishui_data_middleground.service.szls.DrainOperationalRiskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/1/10 上午9:29
 * @description 附属设施控制器
 */
@RestController
@Api(tags = "数据填报-排水设施-运行风险相关接口")
@RequestMapping("manage/drain/operationalRisk")
@Validated
@Slf4j
public class DrainOperationalRiskController {

    @Resource
    private DrainOperationalRiskService service;

    @GetMapping("get")
    @ApiOperation("获取数据")
    public ResponseData<DrainOperationalRiskEditDto> get() {
        List<DrainOperationalRiskEditDto> reportedData = service.getReportedData();
        if(reportedData.size() > 0)
        return ResponseData.ok(reportedData.get(0));
        return ResponseData.ok(new DrainOperationalRiskEditDto());
    }

    @PostMapping("save")
    @ApiOperation("保存数据")
    public ResponseData<Boolean> save(@RequestBody DrainOperationalRiskEditDto editDto) {
        DrainOperationalRisk one = service.getOne(
                new LambdaQueryWrapper<DrainOperationalRisk>()
                        .eq(DrainOperationalRisk::getIsReported, 1)
                        .eq(Objects.nonNull(editDto.getId()),
                                DrainOperationalRisk::getId,
                                editDto.getId())
        );
        boolean save;
        if (Objects.isNull(one)) {
            // 保存
            one = new DrainOperationalRisk();
            BeanUtils.copyProperties(editDto, one);
            one.setIsReported(1);
            one.setRainCondition(JsonUtil.toJsonStr(editDto.getRainCondition()));
            one.setSewageCondition(JsonUtil.toJsonStr(editDto.getSewageCondition()));
            one.setAmountCondition(JsonUtil.toJsonStr(editDto.getAmountCondition()));
            save = service.save(one);
        } else {
            // 更新
            BeanUtils.copyProperties(editDto, one);
            one.setIsReported(1);
            one.setRainCondition(JsonUtil.toJsonStr(editDto.getRainCondition()));
            one.setSewageCondition(JsonUtil.toJsonStr(editDto.getSewageCondition()));
            one.setAmountCondition(JsonUtil.toJsonStr(editDto.getAmountCondition()));
            save = service.updateById(one);
        }

        return save ? ResponseData.ok(true) :ResponseData.warn("保存失败");

    }



}
