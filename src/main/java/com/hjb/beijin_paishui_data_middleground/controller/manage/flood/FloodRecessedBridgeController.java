package com.hjb.beijin_paishui_data_middleground.controller.manage.flood;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.flood.FloodRecessedBridgeEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.flood.FloodRecessedBridge;
import com.hjb.beijin_paishui_data_middleground.service.szls.FloodRecessedBridgeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/1/10 上午9:29
 * @description 下凹桥控制器
 */
@RestController
@Api(tags = "数据填报-防汛调度-下凹桥相关接口")
@RequestMapping("manage/flood/recessedBridge")
@Validated
@Slf4j
public class FloodRecessedBridgeController {

    @Resource
    private FloodRecessedBridgeService service;

    @GetMapping("get")
    @ApiOperation("获取数据")
    public ResponseData<FloodRecessedBridge> get() {
        return ResponseData.ok(service.getReportedData(null, null));
    }

    @PostMapping("save")
    @ApiOperation("保存数据")
    public ResponseData<Boolean> save(@RequestBody FloodRecessedBridgeEditDto editDto) {
        FloodRecessedBridge one = service.getOne(
                new LambdaQueryWrapper<FloodRecessedBridge>()
                        .eq(FloodRecessedBridge::getIsReported, 1)
                        .eq(Objects.nonNull(editDto.getId()),
                                FloodRecessedBridge::getId,
                                editDto.getId())
        );
        boolean save;
        if (Objects.isNull(one)) {
            // 保存
            one = new FloodRecessedBridge();
            BeanUtils.copyProperties(editDto, one);
            one.setIsReported(1);
            one.setRecessedBridge(JsonUtil.toJsonStr(editDto.getRecessedBridgeData()));
            save = service.save(one);
        } else {
            // 更新
            BeanUtils.copyProperties(editDto, one);
            one.setIsReported(1);
            one.setRecessedBridge(JsonUtil.toJsonStr(editDto.getRecessedBridgeData()));
            save = service.updateById(one);
        }
        return save ? ResponseData.ok(true) :ResponseData.warn("保存失败");
    }
    }

