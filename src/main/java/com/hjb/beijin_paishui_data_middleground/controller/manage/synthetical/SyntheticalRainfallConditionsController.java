package com.hjb.beijin_paishui_data_middleground.controller.manage.synthetical;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hjb.beijin_paishui_data_middleground.common.advice.CustomException;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.synthetical.SyntheticalRainfallConditionsEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical.SyntheticalRainfallConditions;
import com.hjb.beijin_paishui_data_middleground.entity.szls.vo.SyntheticalRainfallConditionsVo;
import com.hjb.beijin_paishui_data_middleground.service.szls.SyntheticalRainfallConditionsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/10 上午9:29
 * @description 降雨情况控制器
 */
@RestController
@Api(tags = "数据填报-综合态势-降雨情况相关接口")
@RequestMapping("manage/synthetical/rainfallConditions")
@Validated
@Slf4j
public class SyntheticalRainfallConditionsController {

    @Resource
    private SyntheticalRainfallConditionsService service;

    @GetMapping("get")
    @ApiOperation("获取数据")
    public ResponseData<SyntheticalRainfallConditionsVo> get() {
        return ResponseData.ok(service.getReportedDataByManage(null));
    }

    @PostMapping("saveBasic")
    @ApiOperation("保存基本数据")
    @Transactional(rollbackFor = Exception.class)
    public ResponseData<Boolean> saveBasic(@RequestBody
                                           List<SyntheticalRainfallConditionsEditDto.SyntheticalRainfallConditionsBasic> basics) {
        for (SyntheticalRainfallConditionsEditDto.SyntheticalRainfallConditionsBasic basic : basics) {
            // 根据每年查询
            SyntheticalRainfallConditions one = service.getOne(
                    new LambdaQueryWrapper<SyntheticalRainfallConditions>()
                            .eq(SyntheticalRainfallConditions::getIsReported, 1)
                            .eq(SyntheticalRainfallConditions::getSearchYear, basic.getSearchYear())
            );
            if (Objects.isNull(one)) {
                one = new SyntheticalRainfallConditions();
                BeanUtils.copyProperties(basic, one);
                one.setIsReported(1);
                service.save(one);
            } else {
                // 更新
                one.setRainfallAmount(basic.getRainfallAmount());
                one.setRainfallAmountYoy(basic.getRainfallAmountYoy());
                one.setCumulativeAverageRainfall(basic.getCumulativeAverageRainfall());
                one.setCumulativeAverageRainfallYoy(basic.getCumulativeAverageRainfallYoy());
                one.setIsReported(1);
                service.updateById(one);
            }
        }
        return ResponseData.ok(true);
    }

    @PostMapping("saveChart")
    @ApiOperation("保存图表数据")
    @Transactional(rollbackFor = Exception.class)
    public ResponseData<Boolean> saveChart(@RequestBody
                                           List<SyntheticalRainfallConditionsEditDto.EchartsValueDto> echartsValueDtos) {
        service.remove(
                new LambdaQueryWrapper<SyntheticalRainfallConditions>()
                        .eq(SyntheticalRainfallConditions::getIsReported, 1)
        );
        for (SyntheticalRainfallConditionsEditDto.EchartsValueDto echartsValueDto : echartsValueDtos) {
            // 根据每年查询
            SyntheticalRainfallConditions one = service.getOne(
                    new LambdaQueryWrapper<SyntheticalRainfallConditions>()
                            .eq(SyntheticalRainfallConditions::getIsReported, 1)
                            .eq(SyntheticalRainfallConditions::getSearchYear, echartsValueDto.getSearchYear())
            );
            if (Objects.isNull(one)) {
                one = new SyntheticalRainfallConditions();
                one.setSearchYear(echartsValueDto.getSearchYear());
                setChartValueByType(echartsValueDto, one);
                one.setIsReported(1);
                service.save(one);
            } else {
                // 更新
                setChartValueByType(echartsValueDto, one);
                one.setIsReported(1);
                service.updateById(one);
            }
        }
        return ResponseData.ok(true);
    }

    private void setChartValueByType(SyntheticalRainfallConditionsEditDto.EchartsValueDto echartsValueDto, SyntheticalRainfallConditions one) {
        switch (echartsValueDto.getType()) {
            case 1:
                one.setTop10RainfallStations(JsonUtil.toJsonStr(echartsValueDto.getEchartsValue()));
                break;
            case 2:
                one.setAdministrativeCumulativeRainfall(JsonUtil.toJsonStr(echartsValueDto.getEchartsValue()));
                break;
            case 3:
                one.setOfficeRainfallAdministrative(JsonUtil.toJsonStr(echartsValueDto.getEchartsValue()));
                break;
            case 4:
                one.setRiverRainfallAdministrative(JsonUtil.toJsonStr(echartsValueDto.getEchartsValue()));
                break;
            default:
                throw new CustomException(-2, "参数错误");
        }
    }

    @PostMapping("save")
    @ApiOperation("保存数据")
    public ResponseData<Boolean> save(@RequestBody List<SyntheticalRainfallConditionsEditDto> editDtos) {
        List<SyntheticalRainfallConditions> ones = service.list(
                new LambdaQueryWrapper<SyntheticalRainfallConditions>()
                        .eq(SyntheticalRainfallConditions::getIsReported, 1)
        );
        boolean save;
        if (!ones.isEmpty()) {
            // 删除所有
            List<Long> ids = ones.stream().map(SyntheticalRainfallConditions::getId).collect(Collectors.toList());
            service.removeByIds(ids);
        }
        // 保存
        ones = new ArrayList<>();
        for (SyntheticalRainfallConditionsEditDto editDto : editDtos) {
            SyntheticalRainfallConditions one = new SyntheticalRainfallConditions();
            BeanUtils.copyProperties(editDto, one);
            one.setIsReported(1);
            one.setTop10RainfallStations(JsonUtil.toJsonStr(editDto.getTop10RainfallStationsValue()));
            one.setAdministrativeCumulativeRainfall(JsonUtil.toJsonStr(editDto.getAdministrativeCumulativeRainfallValue()));
            one.setOfficeRainfallAdministrative(JsonUtil.toJsonStr(editDto.getOfficeRainfallAdministrativeValue()));
            one.setRiverRainfallAdministrative(JsonUtil.toJsonStr(editDto.getRiverRainfallAdministrativeValue()));
            ones.add(one);
        }
        save = service.saveBatch(ones);
        return save ? ResponseData.ok(true) : ResponseData.warn("保存失败");

    }


}
