package com.hjb.beijin_paishui_data_middleground.controller.manage.drain;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainAncillaryFacilities;
import com.hjb.beijin_paishui_data_middleground.service.szls.DrainAncillaryFacilitiesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/1/10 上午9:29
 * @description 附属设施控制器
 */
@RestController
@Api(tags = "数据填报-排水设施-附属设施相关接口")
@RequestMapping("manage/drain/ancillaryFacilities")
@Validated
@Slf4j
public class DrainAncillaryFacilitiesController {

    @Resource
    private DrainAncillaryFacilitiesService service;

    @GetMapping("get")
    @ApiOperation("获取数据")
    public ResponseData<DrainAncillaryFacilities> get() {
        DrainAncillaryFacilities one = service.getReportedData();
        return ResponseData.ok(one);
    }

    @PostMapping("save")
    @ApiOperation("保存数据")
    public ResponseData<Boolean> save(@RequestBody DrainAncillaryFacilities editDto) {
        DrainAncillaryFacilities one = service.getOne(
                new LambdaQueryWrapper<DrainAncillaryFacilities>()
                        .eq(DrainAncillaryFacilities::getIsReported, 1)
                        .eq(Objects.nonNull(editDto.getId()),
                                DrainAncillaryFacilities::getId,
                                editDto.getId())
        );
        boolean save;
        if (Objects.isNull(one)) {
            // 保存
            one = new DrainAncillaryFacilities();
            BeanUtils.copyProperties(editDto, one);
            one.setIsReported(1);
            save = service.save(one);
        } else {
            // 更新
            BeanUtils.copyProperties(editDto, one);
            one.setIsReported(1);
            save = service.updateById(one);
        }

        return save ? ResponseData.ok(true) :ResponseData.warn("保存失败");

    }



}
