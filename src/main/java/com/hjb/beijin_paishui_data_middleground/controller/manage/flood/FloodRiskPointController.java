package com.hjb.beijin_paishui_data_middleground.controller.manage.flood;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.flood.FloodRiskPointEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.flood.FloodRiskPoint;
import com.hjb.beijin_paishui_data_middleground.service.szls.FloodRiskPointService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/1/10 上午9:29
 * @description 风险点控制器
 */
@RestController
@Api(tags = "数据填报-防汛调度-风险点相关接口")
@RequestMapping("manage/flood/riskPoint")
@Validated
@Slf4j
public class FloodRiskPointController {

    @Resource
    private FloodRiskPointService service;

    @GetMapping("get")
    @ApiOperation("获取数据")
    public ResponseData<FloodRiskPoint> get() {
        return ResponseData.ok(service.getReportedData(null, null));
    }

    @PostMapping("save")
    @ApiOperation("保存数据")
    public ResponseData<Boolean> save(@RequestBody FloodRiskPointEditDto editDto) {
        FloodRiskPoint one = service.getOne(
                new LambdaQueryWrapper<FloodRiskPoint>()
                        .eq(FloodRiskPoint::getIsReported, 1)
                        .eq(Objects.nonNull(editDto.getId()),
                                FloodRiskPoint::getId,
                                editDto.getId())
        );
        boolean save;
        if (Objects.isNull(one)) {
            // 保存
            one = new FloodRiskPoint();
            BeanUtils.copyProperties(editDto, one);
            one.setIsReported(1);
            one.setRiskPoint(JsonUtil.toJsonStr(editDto.getRiskPointData()));
            save = service.save(one);
        } else {
            // 更新
            BeanUtils.copyProperties(editDto, one);
            one.setIsReported(1);
            one.setRiskPoint(JsonUtil.toJsonStr(editDto.getRiskPointData()));
            save = service.updateById(one);
        }

        return save ? ResponseData.ok(true) :ResponseData.warn("保存失败");

    }



}
