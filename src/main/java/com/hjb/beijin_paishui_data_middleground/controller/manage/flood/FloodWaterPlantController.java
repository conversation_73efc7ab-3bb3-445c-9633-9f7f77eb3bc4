package com.hjb.beijin_paishui_data_middleground.controller.manage.flood;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.flood.FloodWaterPlantEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.flood.FloodWaterPlant;
import com.hjb.beijin_paishui_data_middleground.service.szls.FloodWaterPlantService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/1/10 上午9:29
 * @description 水厂控制器
 */
@RestController
@Api(tags = "数据填报-防汛调度-水厂相关接口")
@RequestMapping("manage/flood/waterPlant")
@Validated
@Slf4j
public class FloodWaterPlantController {

    @Resource
    private FloodWaterPlantService service;

    @GetMapping("get")
    @ApiOperation("获取数据")
    public ResponseData<FloodWaterPlant> get() {

        return ResponseData.ok(service.getReportedData());
    }

    @PostMapping("save")
    @ApiOperation("保存数据")
    public ResponseData<Boolean> save(@RequestBody FloodWaterPlantEditDto editDto) {
        FloodWaterPlant one = service.getOne(
                new LambdaQueryWrapper<FloodWaterPlant>()
                        .eq(FloodWaterPlant::getIsReported, 1)
                        .eq(Objects.nonNull(editDto.getId()),
                                FloodWaterPlant::getId,
                                editDto.getId())
        );
        boolean save;
        if (Objects.isNull(one)) {
            // 保存
            one = new FloodWaterPlant();
            BeanUtils.copyProperties(editDto, one);
            one.setIsReported(1);
            one.setWaterPlantDetails(JsonUtil.toJsonStr(editDto.getWaterPlantDetailsData()));
            save = service.save(one);
        } else {
            // 更新
            BeanUtils.copyProperties(editDto, one);
            one.setIsReported(1);
            one.setWaterPlantDetails(JsonUtil.toJsonStr(editDto.getWaterPlantDetailsData()));
            save = service.updateById(one);
        }

        return save ? ResponseData.ok(true) :ResponseData.warn("保存失败");

    }



}
