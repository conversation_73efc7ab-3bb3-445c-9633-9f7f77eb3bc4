package com.hjb.beijin_paishui_data_middleground.controller.manage.flood;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.flood.FloodRainwaterStorage;
import com.hjb.beijin_paishui_data_middleground.service.szls.FloodRainwaterStorageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/10 上午9:29
 * @description 泵站蓄排控制器
 */
@RestController
@Api(tags = "数据填报-防汛调度-泵站蓄排相关接口")
@RequestMapping("manage/flood/rainwaterStorage")
@Validated
@Slf4j
public class FloodRainwaterStorageController {

    @Resource
    private FloodRainwaterStorageService service;

//    @GetMapping("get")
//    @ApiOperation("获取数据")
//    public ResponseData<List<FloodRainwaterStorage>> get () {
//        return ResponseData.ok(service.getReportedData(null));
//    }

    @GetMapping("get")
    @ApiOperation("获取数据")
    public ResponseData<FloodRainwaterStorage> get () {
        return ResponseData.ok(service.getReportedDataOne(null));
    }

    //    @PostMapping("save")
//    @ApiOperation("保存数据")
//    public ResponseData<Boolean> save(@RequestBody List<FloodRainwaterStorage> editDtos) {
//        List<FloodRainwaterStorage> ones = service.list(
//                new LambdaQueryWrapper<FloodRainwaterStorage>()
//                        .eq(FloodRainwaterStorage::getIsReported, 1)
//        );
//        boolean save;
//        if (!ones.isEmpty()) {
//            List<Long> ids = ones.stream().map(FloodRainwaterStorage::getId).collect(Collectors.toList());
//            service.removeBatchByIds(ids);
//        }
//        // 保存
//        ones = new ArrayList<>();
//        for (FloodRainwaterStorage editDto : editDtos) {
//            FloodRainwaterStorage one = new FloodRainwaterStorage();
//            BeanUtils.copyProperties(editDto, one);
//            one.setIsReported(1);
//            ones.add(one);
//        }
//        save = service.saveBatch(ones);
//        return save ? ResponseData.ok(true) : ResponseData.warn("保存失败");
//
//    }
    @PostMapping("save")
    @ApiOperation("保存数据")
    public ResponseData<Boolean> save (@RequestBody FloodRainwaterStorage editDto) {
        boolean save = false;
        FloodRainwaterStorage one = new FloodRainwaterStorage();
        BeanUtils.copyProperties(editDto, one);
        one.setIsReported(1);
        if (!Objects.isNull(editDto.getId())) {
            FloodRainwaterStorage floodRainwaterStorage = service.getById(editDto.getId());
            if (!Objects.isNull(floodRainwaterStorage)) {
                save = service.updateById(one);
            } else {
                save = service.save(one);
            }
        } else
            save = service.save(one);

        return save ? ResponseData.ok(true) : ResponseData.warn("保存失败");
    }

}
