package com.hjb.beijin_paishui_data_middleground.controller.manage.drain;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.drain.DrainMaintenanceProductionEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainMaintenanceProduction;
import com.hjb.beijin_paishui_data_middleground.service.szls.DrainMaintenanceProductionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/1/10 上午9:29
 * @description 养护生产控制器
 */
@RestController
@Api(tags = "数据填报-排水设施-养护生产相关接口")
@RequestMapping("manage/drain/maintenanceProduction")
@Validated
@Slf4j
public class DrainMaintenanceProductionController {

    @Resource
    private DrainMaintenanceProductionService service;

    @GetMapping("get")
    @ApiOperation("获取数据")
    public ResponseData<DrainMaintenanceProduction> get() {

        return ResponseData.ok(service.getReportedData());
    }

    @PostMapping("save")
    @ApiOperation("保存数据")
    public ResponseData<Boolean> save(@RequestBody DrainMaintenanceProductionEditDto editDto) {
        DrainMaintenanceProduction one = service.getOne(
                new LambdaQueryWrapper<DrainMaintenanceProduction>()
                        .eq(DrainMaintenanceProduction::getIsReported, 1)
                        .eq(Objects.nonNull(editDto.getId()),
                                DrainMaintenanceProduction::getId,
                                editDto.getId())
        );
        boolean save;
        if (Objects.isNull(one)) {
            // 保存
            one = new DrainMaintenanceProduction();
            BeanUtils.copyProperties(editDto, one);
            one.setIsReported(1);
            one.setAnnualTrend(JsonUtil.toJsonStr(editDto.getAnnualTrendValue()));
            save = service.save(one);
        } else {
            // 更新
            BeanUtils.copyProperties(editDto, one);
            one.setIsReported(1);
            one.setAnnualTrend(JsonUtil.toJsonStr(editDto.getAnnualTrendValue()));
            save = service.updateById(one);
        }

        return save ? ResponseData.ok(true) :ResponseData.warn("保存失败");

    }



}
