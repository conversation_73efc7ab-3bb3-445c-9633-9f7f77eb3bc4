package com.hjb.beijin_paishui_data_middleground.controller.manage.event;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.event.EventFacilityAppealEventEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.event.EventFacilityAppealEvent;
import com.hjb.beijin_paishui_data_middleground.service.szls.EventFacilityAppealEventService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/1/10 上午9:29
 * @description 设施诉求类事件控制器
 */
@RestController
@Api(tags = "数据填报-接诉即办-设施诉求类事件接口")
@RequestMapping("manage/event/facilityAppealEvent")
@Validated
@Slf4j
public class EventFacilityAppealEventController {

    @Resource
    private EventFacilityAppealEventService service;

    @GetMapping("get")
    @ApiOperation("获取数据")
    public ResponseData<EventFacilityAppealEvent> get() {

        return ResponseData.ok(service.getReportedData());
    }

    @PostMapping("save")
    @ApiOperation("保存数据")
    public ResponseData<Boolean> save(@RequestBody EventFacilityAppealEventEditDto editDto) {
        EventFacilityAppealEvent one = service.getOne(
                new LambdaQueryWrapper<EventFacilityAppealEvent>()
                        .eq(EventFacilityAppealEvent::getIsReported, 1)
                        .eq(Objects.nonNull(editDto.getId()),
                                EventFacilityAppealEvent::getId,
                                editDto.getId())
        );
        boolean save;
        if (Objects.isNull(one)) {
            // 保存
            one = new EventFacilityAppealEvent();
            BeanUtils.copyProperties(editDto, one);
            one.setIsReported(1);
            one.setEventClassification(JsonUtil.toJsonStr(editDto.getEventClassificationData()));
            save = service.save(one);
        } else {
            // 更新
            BeanUtils.copyProperties(editDto, one);
            one.setIsReported(1);
            one.setEventClassification(JsonUtil.toJsonStr(editDto.getEventClassificationData()));
            save = service.updateById(one);
        }

        return save ? ResponseData.ok(true) :ResponseData.warn("保存失败");

    }



}
