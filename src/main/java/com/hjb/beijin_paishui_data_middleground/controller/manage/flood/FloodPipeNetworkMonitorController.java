package com.hjb.beijin_paishui_data_middleground.controller.manage.flood;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.flood.FloodPipeNetworkMonitorEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.flood.FloodPipeNetworkMonitor;
import com.hjb.beijin_paishui_data_middleground.service.szls.FloodPipeNetworkMonitorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/10 上午9:29
 * @description 官网检测控制器
 */
@RestController
@Api(tags = "数据填报-防汛调度-管网检测相关接口")
@RequestMapping("manage/flood/pipeNetworkMonitor")
@Validated
@Slf4j
public class FloodPipeNetworkMonitorController {

    @Resource
    private FloodPipeNetworkMonitorService service;

    @GetMapping("get")
    @ApiOperation("获取数据")
    public ResponseData<List<FloodPipeNetworkMonitorEditDto>> get(String type) {
        return ResponseData.ok(service.getReportedData(null));
    }

    @PostMapping("save")
    @ApiOperation("保存数据")
    public ResponseData<Boolean> save(@RequestBody List<FloodPipeNetworkMonitorEditDto> editDtos) {
        List<FloodPipeNetworkMonitor> ones = service.list(
                new LambdaQueryWrapper<FloodPipeNetworkMonitor>()
                        .eq(FloodPipeNetworkMonitor::getIsReported, 1)
        );
        boolean save;
        if (!ones.isEmpty()) {
            List<Long> ids = ones.stream().map(FloodPipeNetworkMonitor::getId).collect(Collectors.toList());
            service.removeBatchByIds(ids);
        }
        // 保存
        ones = new ArrayList<>();
        for (FloodPipeNetworkMonitorEditDto editDto : editDtos) {
            FloodPipeNetworkMonitor one = new FloodPipeNetworkMonitor();
            BeanUtils.copyProperties(editDto, one);
            one.setIsReported(1);
            one.setStationDetails(JsonUtil.toJsonStr(editDto.getStationDetailsData()));
            ones.add(one);
        }
        save = service.saveBatch(ones);
        return save ? ResponseData.ok(true) : ResponseData.warn("保存失败");

    }


}
