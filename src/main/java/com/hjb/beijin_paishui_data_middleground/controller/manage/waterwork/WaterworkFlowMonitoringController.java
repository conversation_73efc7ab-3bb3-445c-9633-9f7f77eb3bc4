package com.hjb.beijin_paishui_data_middleground.controller.manage.waterwork;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.waterwork.WaterworkFlowMonitoringEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.waterwork.WaterworkFlowMonitoring;
import com.hjb.beijin_paishui_data_middleground.service.szls.WaterworkFlowMonitoringService;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @createDate 2025/7/30 13:00
 * @description 流量监测
 */
@RestController
@Api(tags = "数据填报-防汛调度-流量监测")
@RequestMapping("manage/waterwork/flowMonitoring")
@Validated
@Slf4j
public class WaterworkFlowMonitoringController {

    @Resource
    private WaterworkFlowMonitoringService service;

    @GetMapping("get")
    @ApiOperation("获取数据")
    public ResponseData<WaterworkFlowMonitoring> get() {

        return ResponseData.ok(service.getReportedData());
    }

    @PostMapping("save")
    @ApiOperation("保存数据")
    public ResponseData<Boolean> save(@RequestBody WaterworkFlowMonitoringEditDto editDto) {
        WaterworkFlowMonitoring one = service.getOne(
                new LambdaQueryWrapper<WaterworkFlowMonitoring>()
                        .eq(WaterworkFlowMonitoring::getIsReported, 1)
                        .eq(Objects.nonNull(editDto.getId()),
                                WaterworkFlowMonitoring::getId,
                                editDto.getId())
        );
        boolean save;
        if (Objects.isNull(one)) {
            // 保存
            one = new WaterworkFlowMonitoring();
            BeanUtils.copyProperties(editDto, one);
            one.setIsReported(1);
            one.setFlowMonitoring(JsonUtil.toJsonStr(editDto.getWaterworkFlowMonitoringValue()));
            save = service.save(one);
        } else {
            // 更新
            BeanUtils.copyProperties(editDto, one);
            one.setIsReported(1);
            one.setFlowMonitoring(JsonUtil.toJsonStr(editDto.getWaterworkFlowMonitoringValue()));
            save = service.updateById(one);
        }
        return save ? ResponseData.ok(true) :ResponseData.warn("保存失败");
    }
}
