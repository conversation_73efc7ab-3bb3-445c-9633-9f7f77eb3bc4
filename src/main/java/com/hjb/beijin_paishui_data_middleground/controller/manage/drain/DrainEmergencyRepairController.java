package com.hjb.beijin_paishui_data_middleground.controller.manage.drain;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.drain.DrainEmergencyRepairEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.DrainEmergencyRepair;
import com.hjb.beijin_paishui_data_middleground.service.szls.DrainEmergencyRepairService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/1/10 上午9:29
 * @description 应急事件控制器
 */
@RestController
@Api(tags = "数据填报-排水设施-应急事件相关接口")
@RequestMapping("manage/drain/emergencyRepair")
@Validated
@Slf4j
public class DrainEmergencyRepairController {

    @Resource
    private DrainEmergencyRepairService service;

    @GetMapping("get")
    @ApiOperation("获取数据")
    public ResponseData<DrainEmergencyRepair> get() {
        DrainEmergencyRepair one = service.getReportedData();
        return ResponseData.ok(one);
    }

    @PostMapping("save")
    @ApiOperation("保存数据")
    public ResponseData<Boolean> save(@RequestBody DrainEmergencyRepairEditDto editDto) {
        DrainEmergencyRepair one = service.getOne(
                new LambdaQueryWrapper<DrainEmergencyRepair>()
                        .eq(DrainEmergencyRepair::getIsReported, 1)
                        .eq(Objects.nonNull(editDto.getId()),
                                DrainEmergencyRepair::getId,
                                editDto.getId())
        );
        boolean save;
        if (Objects.isNull(one)) {
            // 保存
            one = new DrainEmergencyRepair();
            BeanUtils.copyProperties(editDto, one);
            one.setIsReported(1);
            one.setEmergencyRepairAll(JsonUtil.toJsonStr(editDto.getEmergencyRepairAllValue()));
            one.setEmergencyRescue(JsonUtil.toJsonStr(editDto.getEmergencyRescueValue()));
            one.setRushToRepair(JsonUtil.toJsonStr(editDto.getRushToRepairValue()));
            one.setFloodPrevention(JsonUtil.toJsonStr(editDto.getFloodPreventionValue()));
            save = service.save(one);
        } else {
            // 更新
            BeanUtils.copyProperties(editDto, one);
            one.setIsReported(1);
            one.setEmergencyRepairAll(JsonUtil.toJsonStr(editDto.getEmergencyRepairAllValue()));
            one.setEmergencyRepairAll(JsonUtil.toJsonStr(editDto.getEmergencyRepairAllValue()));
            one.setEmergencyRescue(JsonUtil.toJsonStr(editDto.getEmergencyRescueValue()));
            one.setRushToRepair(JsonUtil.toJsonStr(editDto.getRushToRepairValue()));
            one.setFloodPrevention(JsonUtil.toJsonStr(editDto.getFloodPreventionValue()));     save = service.updateById(one);
        }

        return save ? ResponseData.ok(true) :ResponseData.warn("保存失败");

    }



}
