package com.hjb.beijin_paishui_data_middleground.controller.manage.event;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.event.EventEventSource;
import com.hjb.beijin_paishui_data_middleground.service.szls.EventEventSourceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/1/10 上午9:29
 * @description 事件来源控制器
 */
@RestController
@Api(tags = "数据填报-接诉即办-事件来源相关接口")
@RequestMapping("manage/event/eventSource")
@Validated
@Slf4j
public class EventEventSourceController {

    @Resource
    private EventEventSourceService service;

    @GetMapping("get")
    @ApiOperation("获取数据")
    public ResponseData<EventEventSource> get() {

        return ResponseData.ok(service.getReportedData());
    }

    @PostMapping("save")
    @ApiOperation("保存数据")
    public ResponseData<Boolean> save(@RequestBody EventEventSource editDto) {
        EventEventSource one = service.getOne(
                new LambdaQueryWrapper<EventEventSource>()
                        .eq(EventEventSource::getIsReported, 1)
                        .eq(Objects.nonNull(editDto.getId()),
                                EventEventSource::getId,
                                editDto.getId())
        );
        boolean save;
        if (Objects.isNull(one)) {
            // 保存
            one = new EventEventSource();
            BeanUtils.copyProperties(editDto, one);
            one.setIsReported(1);
            save = service.save(one);
        } else {
            // 更新
            BeanUtils.copyProperties(editDto, one);
            one.setIsReported(1);
            save = service.updateById(one);
        }

        return save ? ResponseData.ok(true) :ResponseData.warn("保存失败");

    }



}
