package com.hjb.beijin_paishui_data_middleground.controller.manage;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.SzlsModuleEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.SzlsModule;
import com.hjb.beijin_paishui_data_middleground.entity.szls.vo.SzlsModuleVo;
import com.hjb.beijin_paishui_data_middleground.service.szls.SzlsModuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/1/6 下午12:05
 * @description 接诉即办控制器
 */
@RestController
@Api(tags = "数据填报-模块相关接口")
@RequestMapping("manage/module")
@Validated
@Slf4j
public class SzlsModuleController {

    @Resource
    private SzlsModuleService szlsModuleService;

    @PostMapping("add")
    @ApiOperation("添加模块")
    public ResponseData<Boolean> add(@Validated @RequestBody SzlsModuleEditDto szlsModuleEditDto) {
        SzlsModule module = szlsModuleService.getOne(
                new LambdaQueryWrapper<SzlsModule>()
                        .eq(SzlsModule::getModuleName, szlsModuleEditDto.getModuleName())
                        .eq(Objects.nonNull(szlsModuleEditDto.getParentId()), SzlsModule::getParentId, szlsModuleEditDto.getParentId())
        );
        if (Objects.nonNull(module)) {
            return ResponseData.warn("模块名名称 '" + szlsModuleEditDto.getModuleName() + "' 已经存在");
        }
        SzlsModule szlsModule = new SzlsModule();
        BeanUtils.copyProperties(szlsModuleEditDto, szlsModule);
        if (Objects.isNull(szlsModuleEditDto.getParentId())) {
            szlsModule.setParentId(0L);
        }
        boolean save = szlsModuleService.save(szlsModule);
        return save ? ResponseData.ok(true) : ResponseData.warn("添加失败");
    }

    @GetMapping("delete")
    @ApiOperation("删除模块")
    public ResponseData<Boolean> delete(@NotNull(message = "模块 ID 不能为空") Long id) {
        boolean remove = szlsModuleService.removeById(id);
        return remove ? ResponseData.ok(true) : ResponseData.warn("删除失败");
    }

    @PostMapping("update")
    @ApiOperation("修改模块")
    public ResponseData<Boolean> update(@Validated @RequestBody SzlsModuleEditDto szlsModuleEditDto) {
        if (Objects.isNull(szlsModuleEditDto.getId())) {
            return ResponseData.warn("模块 ID 不能为空");
        }
        SzlsModule module = szlsModuleService.getOne(
                new LambdaQueryWrapper<SzlsModule>().eq(SzlsModule::getModuleName, szlsModuleEditDto.getModuleName())
                        .ne(SzlsModule::getId, szlsModuleEditDto.getId())
                        .eq(Objects.nonNull(szlsModuleEditDto.getParentId()), SzlsModule::getParentId, szlsModuleEditDto.getParentId())
        );
        if (Objects.nonNull(module)) {
            return ResponseData.warn("模块名名称 '" + szlsModuleEditDto.getModuleName() + "' 已经存在");
        }
        SzlsModule szlsModule = new SzlsModule();
        BeanUtils.copyProperties(szlsModuleEditDto, szlsModule);
        if (Objects.isNull(szlsModuleEditDto.getParentId())) {
            szlsModule.setParentId(0L);
        }
        boolean save = szlsModuleService.updateById(szlsModule);
        return save ? ResponseData.ok(true) : ResponseData.warn("修改失败");
    }

    @GetMapping("tree")
    @ApiOperation("获取树形结构")
    public ResponseData<List<SzlsModuleVo>> tree() {
        return ResponseData.ok(szlsModuleService.tree());
    }


}
