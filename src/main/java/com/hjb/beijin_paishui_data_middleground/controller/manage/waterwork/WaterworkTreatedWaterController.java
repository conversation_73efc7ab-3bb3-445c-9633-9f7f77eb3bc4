package com.hjb.beijin_paishui_data_middleground.controller.manage.waterwork;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.common.utils.JsonUtil;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.waterwork.WaterworkTreatedWaterEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.waterwork.WaterworkTreatedWater;
import com.hjb.beijin_paishui_data_middleground.service.szls.WaterworkTreatedWaterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @createDate 2025/7/30 18:00
 * @description 处理水量
 */
@RestController
@Api(tags = "数据填报-防汛调度-处理水量")
@RequestMapping("manage/waterwork/treatedWater")
@Validated
@Slf4j
public class WaterworkTreatedWaterController {

    @Resource
    private WaterworkTreatedWaterService service;

    @GetMapping("get")
    @ApiOperation("获取数据")
    public ResponseData<WaterworkTreatedWater> get() {

        return ResponseData.ok(service.getReportedData());
    }

    @PostMapping("save")
    @ApiOperation("保存数据")
    public ResponseData<Boolean> save(@RequestBody WaterworkTreatedWaterEditDto editDto) {
        WaterworkTreatedWater one = service.getOne(
                new LambdaQueryWrapper<WaterworkTreatedWater>()
                        .eq(WaterworkTreatedWater::getIsReported, 1)
                        .eq(Objects.nonNull(editDto.getId()),
                                WaterworkTreatedWater::getId,
                                editDto.getId())
        );
        boolean save;
        if (Objects.isNull(one)) {
            // 保存
            one = new WaterworkTreatedWater();
            BeanUtils.copyProperties(editDto, one);
            one.setIsReported(1);
            one.setTreatedWater(JsonUtil.toJsonStr(editDto.getTreatedWaterValue()));
            save = service.save(one);
        } else {
            // 更新
            BeanUtils.copyProperties(editDto, one);
            one.setIsReported(1);
            one.setTreatedWater(JsonUtil.toJsonStr(editDto.getTreatedWaterValue()));
            save = service.updateById(one);
        }
        return save ? ResponseData.ok(true) :ResponseData.warn("保存失败");
    }
}
