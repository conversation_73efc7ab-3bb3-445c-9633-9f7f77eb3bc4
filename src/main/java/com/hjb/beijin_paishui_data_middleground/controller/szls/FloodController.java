package com.hjb.beijin_paishui_data_middleground.controller.szls;

import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.constants.SzlsModuleEnum;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.flood.FloodPipeNetworkMonitorEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.flood.*;
import com.hjb.beijin_paishui_data_middleground.service.szls.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/15 上午10:08
 * @description 综合态势相关接口
 */
@RestController
@Api(tags = "防汛调度相关接口")
@RequestMapping("szls/flood")
@Validated
@Slf4j
public class FloodController {

    @Resource
    private FloodRainwaterStorageService floodRainwaterStorageService;
    @Resource
    private FloodRealTimeRainfallService floodRealTimeRainfallService;
    @Resource
    private FloodWaterPlantService floodWaterPlantService;
    @Resource
    private FloodPipeNetworkMonitorService floodPipeNetworkMonitorService;
    @Resource
    private FloodRecessedBridgeService floodRecessedBridgeService;
    @Resource
    private FloodRiskPointService floodRiskPointService;
    @Resource
    private FloodEmergencyUnitService floodEmergencyUnitService;
    @Resource
    private SzlsModuleService szlsModuleService;

    @GetMapping("getRealTimeRainfall")
    @ApiOperation("实时雨量数据")
    public ResponseData<FloodRealTimeRainfall> getRealTimeRainfall() {
        if (szlsModuleService.isReported("实时雨量", SzlsModuleEnum.FXDD.getId())) {
            return ResponseData.ok(floodRealTimeRainfallService.getReportedData());
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getRainwaterStorage")
    @ApiOperation("泵站排蓄数据")
    public ResponseData<FloodRainwaterStorage> getRainwaterStorage(String company) {
        if (szlsModuleService.isReported("泵站排蓄", SzlsModuleEnum.FXDD.getId())) {
            return ResponseData.ok(floodRainwaterStorageService.getReportedData(company).get(0));
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getWaterPlant")
    @ApiOperation("水厂数据")
    public ResponseData<FloodWaterPlant> getWaterPlant() {
        if (szlsModuleService.isReported("水厂", SzlsModuleEnum.FXDD.getId())) {
            return ResponseData.ok(floodWaterPlantService.getReportedData());
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getPipeNetworkMonitor")
    @ApiOperation("管网检测数据")
    public ResponseData< List<FloodPipeNetworkMonitorEditDto>> getPipeNetworkMonitor(String type) {
        if (szlsModuleService.isReported("管网检测", SzlsModuleEnum.FXDD.getId())) {
            return ResponseData.ok(floodPipeNetworkMonitorService.getReportedData(null));
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getRecessedBridge")
    @ApiOperation("下凹桥数据")
    public ResponseData<FloodRecessedBridge> getRecessedBridge(String searchAdministrative, String searchCompany) {
        if (szlsModuleService.isReported("下凹桥", SzlsModuleEnum.FXDD.getId())) {
            return ResponseData.ok(floodRecessedBridgeService.getReportedData(searchAdministrative, searchCompany));
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getRiskPoint")
    @ApiOperation("风险点数据")
    public ResponseData<FloodRiskPoint> getRiskPoint(String searchAdministrative, String searchCompany) {
        if (szlsModuleService.isReported("风险点", SzlsModuleEnum.FXDD.getId())) {
            return ResponseData.ok(floodRiskPointService.getReportedData(searchAdministrative, searchCompany));
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getEmergencyUnit")
    @ApiOperation("抢险单元数据")
    public ResponseData<FloodEmergencyUnit> getEmergencyUnit(String searchType, String searchCompany, String searchUnit) {
        if (szlsModuleService.isReported("抢险单元", SzlsModuleEnum.FXDD.getId())) {
            return ResponseData.ok(floodEmergencyUnitService.getReportedData(searchType, searchCompany, searchUnit));
        }
        return ResponseData.ok(null);
    }

}
