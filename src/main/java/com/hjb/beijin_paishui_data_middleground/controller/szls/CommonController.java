package com.hjb.beijin_paishui_data_middleground.controller.szls;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.constants.SzlsModuleEnum;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.SzlsModule;
import com.hjb.beijin_paishui_data_middleground.entity.szls.vo.SzlsModuleVo;
import com.hjb.beijin_paishui_data_middleground.service.szls.SzlsModuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/1/15 上午10:08
 * @description 通用控制器
 */
@RestController
@Api(tags = "通用接口")
@RequestMapping("szls/common")
@Validated
@Slf4j
public class CommonController {

    @Resource
    private SzlsModuleService szlsModuleService;

    @GetMapping("getReportedAllConfig")
    @ApiOperation("获取展示填报数据配置")
    public ResponseData<List<SzlsModuleVo>> getReportedConfig() {
        return ResponseData.ok(szlsModuleService.tree());
    }

    @GetMapping("getReportedConfigByName")
    @ApiOperation("根据模块名称获取填报数据配置")
    public ResponseData<Boolean> getReportedConfigByName(String name) {
        SzlsModule szlsModule = szlsModuleService.getOne(
                new LambdaQueryWrapper<SzlsModule>().eq(SzlsModule::getModuleName, name)
        );
        if (Objects.isNull(szlsModule)) {
            return ResponseData.warn("模块不存在");
        }
        return ResponseData.ok(szlsModule.getIsReported() == 1);
    }

}
