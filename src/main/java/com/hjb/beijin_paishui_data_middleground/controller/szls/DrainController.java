package com.hjb.beijin_paishui_data_middleground.controller.szls;

import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.constants.SzlsModuleEnum;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.drain.DrainOperationalRiskEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.*;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical.*;
import com.hjb.beijin_paishui_data_middleground.service.szls.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/15 上午10:08
 * @description 综合态势相关接口
 */
@RestController
@Api(tags = "排水设施相关接口")
@RequestMapping("szls/drain")
@Validated
@Slf4j
public class DrainController {

    @Resource
    private DrainPipeNetworkAndFacilitiesService drainPipeNetworkAndFacilitiesService;
    @Resource
    private DrainAncillaryFacilitiesService drainAncillaryFacilitiesService;
    @Resource
    private DrainSpecialFacilityService drainSpecialFacilityService;
    @Resource
    private DrainMaintenanceProductionService drainMaintenanceProductionService;
    @Resource
    private DrainHybridConnectionService drainHybridConnectionService;
    @Resource
    private DrainPeripheralConstructionService drainPeripheralConstructionService;
    @Resource
    private DrainOperationalRiskService drainOperationalRiskService;
    @Resource
    private DrainPipeNetworkTestingService drainPipeNetworkTestingService;
    @Resource
    private DrainFacilityBlankingService drainFacilityBlankingService;
    @Resource
    private DrainEmergencyRepairService drainEmergencyRepairService;

    @Resource
    private DrainPipelineDefectService drainPipelineDefectService;
    @Resource
    private SzlsModuleService szlsModuleService;

    @GetMapping("getPipeNetworkAndFacilities")
    @ApiOperation("管网及设施数据")
    public ResponseData<DrainPipeNetworkAndFacilities> getPipeNetworkAndFacilities() {
        if (szlsModuleService.isReported("管网及设施", SzlsModuleEnum.PSSS.getId())) {
            return ResponseData.ok(drainPipeNetworkAndFacilitiesService.getReportedData());
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getAncillaryFacilities")
    @ApiOperation("附属设施数据")
    public ResponseData<DrainAncillaryFacilities> getAncillaryFacilities() {
        if (szlsModuleService.isReported("附属设施", SzlsModuleEnum.PSSS.getId())) {
            return ResponseData.ok(drainAncillaryFacilitiesService.getReportedData());
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getSpecialFacility")
    @ApiOperation("特殊设施数据")
    public ResponseData<DrainSpecialFacility> getSpecialFacility() {
        if (szlsModuleService.isReported("特殊设施", SzlsModuleEnum.PSSS.getId())) {
            return ResponseData.ok(drainSpecialFacilityService.getReportedData());
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getMaintenanceProduction")
    @ApiOperation("养护生产数据")
    public ResponseData<DrainMaintenanceProduction> getMaintenanceProduction() {
        if (szlsModuleService.isReported("养护生产", SzlsModuleEnum.PSSS.getId())) {
            return ResponseData.ok(drainMaintenanceProductionService.getReportedData());
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getHybridConnection")
    @ApiOperation("混接数据")
    public ResponseData<DrainHybridConnection> getHybridConnection() {
        if (szlsModuleService.isReported("混接", SzlsModuleEnum.PSSS.getId())) {
            return ResponseData.ok(drainHybridConnectionService.getReportedData());
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getPeripheralConstruction")
    @ApiOperation("周边施工数据")
    public ResponseData<DrainPeripheralConstruction> getPeripheralConstruction() {
        if (szlsModuleService.isReported("周边施工", SzlsModuleEnum.PSSS.getId())) {
            return ResponseData.ok(drainPeripheralConstructionService.getReportedData());
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getOperationalRisk")
    @ApiOperation("运行风险数据")
    public ResponseData<DrainOperationalRiskEditDto> getOperationalRisk() {
        if (szlsModuleService.isReported("运行风险", SzlsModuleEnum.PSSS.getId())) {
            List<DrainOperationalRiskEditDto> reportedData = drainOperationalRiskService.getReportedData();
            if(reportedData.size() > 0)
                return ResponseData.ok(reportedData.get(0));
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getPipeNetworkTesting")
    @ApiOperation("管网检测数据")
    public ResponseData<DrainPipeNetworkTesting> getPipeNetworkTesting() {
        if (szlsModuleService.isReported("管网检测", SzlsModuleEnum.PSSS.getId())) {
            return ResponseData.ok(drainPipeNetworkTestingService.getReportedData());
        }
        return ResponseData.ok(null);
    }
    @GetMapping("getPipelineDefect")
    @ApiOperation("管道缺陷数据")
    public ResponseData<DrainPipelineDefect> getPipelineDefect() {
        if (szlsModuleService.isReported("管道缺陷", SzlsModuleEnum.PSSS.getId())) {
            return ResponseData.ok(drainPipelineDefectService.getReportedData());
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getFacilityBlanking")
    @ApiOperation("设施消隐数据")
    public ResponseData<DrainFacilityBlanking> getFacilityBlanking() {
        if (szlsModuleService.isReported("设施消隐", SzlsModuleEnum.PSSS.getId())) {
            return ResponseData.ok(drainFacilityBlankingService.getReportedData());
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getEmergencyRepair")
    @ApiOperation("应急事件数据")
    public ResponseData<DrainEmergencyRepair> getEmergencyRepair() {
        if (szlsModuleService.isReported("应急事件", SzlsModuleEnum.PSSS.getId())) {
            return ResponseData.ok(drainEmergencyRepairService.getReportedData());
        }
        return ResponseData.ok(null);
    }

}
