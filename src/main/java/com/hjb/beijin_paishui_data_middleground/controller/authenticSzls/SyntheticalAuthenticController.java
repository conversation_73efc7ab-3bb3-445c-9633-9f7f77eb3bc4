package com.hjb.beijin_paishui_data_middleground.controller.authenticSzls;

import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.constants.SzlsModuleEnum;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.synthetical.*;
import com.hjb.beijin_paishui_data_middleground.entity.szls.vo.SyntheticalRainfallConditionsVo;
import com.hjb.beijin_paishui_data_middleground.service.reception.SyntheticalService;
import com.hjb.beijin_paishui_data_middleground.service.szls.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/1/15 上午10:08
 * @description 综合态势相关接口
 */
@RestController
@Api(tags = "综合态势相关接口")
@RequestMapping("authenticSzls/zhts")
@Validated
@Slf4j
public class SyntheticalAuthenticController {
    @Resource
    private SzlsModuleService szlsModuleService;
    @Resource
    private SyntheticalService syntheticalService;
    @Resource
    private SyntheticalPumpStationOperationService syntheticalPumpStationOperationService;
    @Resource
    private SyntheticalSewageTreatmentService syntheticalSewageTreatmentService;
    @Resource
    private SyntheticalReclaimedWaterSupplyService syntheticalReclaimedWaterSupplyService;
    @Resource
    private SyntheticalSludgeDisposalService syntheticalSludgeDisposalService;
    @Resource
    private SyntheticalWaterTreatmentPlateService syntheticalWaterTreatmentPlateService;
    @Resource
    private SyntheticalRainfallConditionsService syntheticalRainfallConditionsService;
    @Resource
    private SyntheticalFloodControlService syntheticalFloodControlService;

    @GetMapping("getPipelineOperation")
    @ApiOperation("管网运营数据")
    public ResponseData<SyntheticalPipelineOperation> getPipelineOperation () {
        if (szlsModuleService.isReported("管网运营", SzlsModuleEnum.ZHTS.getId())) {
            return syntheticalService.getPipelineOperation();
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getPumpStationOperation")
    @ApiOperation("泵站运营数据")
    public ResponseData<SyntheticalPumpStationOperation> getPumpStationOperation () {
        if (szlsModuleService.isReported("泵站运营", SzlsModuleEnum.ZHTS.getId())) {
            return ResponseData.ok(syntheticalPumpStationOperationService.getReportedData());
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getSewageTreatment")
    @ApiOperation("污水处理数据")
    public ResponseData<SyntheticalSewageTreatment> getSewageTreatment () {
        if (szlsModuleService.isReported("污水处理", SzlsModuleEnum.ZHTS.getId())) {
            return ResponseData.ok(syntheticalSewageTreatmentService.getReportedData());
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getReclaimedWaterSupply")
    @ApiOperation("再生水供应数据")
    public ResponseData<SyntheticalReclaimedWaterSupply> getReclaimedWaterSupply () {
        if (szlsModuleService.isReported("再生水供应", SzlsModuleEnum.ZHTS.getId())) {
            return ResponseData.ok(syntheticalReclaimedWaterSupplyService.getReportedData());
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getSludgeDisposal")
    @ApiOperation("污泥处理数据")
    public ResponseData<SyntheticalSludgeDisposal> getSludgeDisposal () {
        if (szlsModuleService.isReported("污泥处理", SzlsModuleEnum.ZHTS.getId())) {
            return ResponseData.ok(syntheticalSludgeDisposalService.getReportedData());
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getRainfallConditions")
    @ApiOperation("降雨情况数据")
    public ResponseData<SyntheticalRainfallConditionsVo> getRainfallConditions (String year) {
        if (szlsModuleService.isReported("降雨情况", SzlsModuleEnum.ZHTS.getId())) {
            return ResponseData.ok(syntheticalRainfallConditionsService.getReportedDataByManage(year));
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getPipelinePlate")
    @ApiOperation("管网板块数据")
    public ResponseData<SyntheticalPipelinePlate> getPipelinePlate () {
        if (szlsModuleService.isReported("管网板块", SzlsModuleEnum.ZHTS.getId())) {
            return syntheticalService.getPipelinePlate();
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getWaterTreatmentPlate")
    @ApiOperation("水处理板块数据")
    public ResponseData<SyntheticalWaterTreatmentPlate> getWaterTreatmentPlate () {
        if (szlsModuleService.isReported("水处理板块", SzlsModuleEnum.ZHTS.getId())) {
            return ResponseData.ok(syntheticalWaterTreatmentPlateService.getReportedData());
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getFloodControl")
    @ApiOperation("防汛保障数据")
    public ResponseData<SyntheticalFloodControl> getFloodControl () {
        if (szlsModuleService.isReported("防汛保障", SzlsModuleEnum.ZHTS.getId())) {
            return ResponseData.ok(syntheticalFloodControlService.getReportedData());
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getComplaintResolution")
    @ApiOperation("接诉即办数据")
    public ResponseData<SyntheticalComplaintResolution> getComplaintResolution () {
        if (szlsModuleService.isReported("接诉即办", SzlsModuleEnum.ZHTS.getId())) {
            return syntheticalService.getComplaintResolution(LocalDateTime.now());
        }
        return ResponseData.ok(null);
    }

}
