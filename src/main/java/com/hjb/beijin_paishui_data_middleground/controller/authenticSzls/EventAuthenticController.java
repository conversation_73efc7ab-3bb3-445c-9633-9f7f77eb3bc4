package com.hjb.beijin_paishui_data_middleground.controller.authenticSzls;

import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.constants.SzlsModuleEnum;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.event.*;
import com.hjb.beijin_paishui_data_middleground.service.reception.EventService;
import com.hjb.beijin_paishui_data_middleground.service.szls.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/1/6 下午12:05
 * @description 接诉即办控制器
 */
@RestController
@Api(tags = "接诉即办相关接口")
@RequestMapping("authenticSzls/event")
@Validated
@Slf4j
public class EventAuthenticController {
    @Resource
    private EventService eventService;
    @Resource
    private SzlsModuleService szlsModuleService;
    @Resource
    private EventComplaintResolutionService eventComplaintResolutionService;

    @GetMapping("getComplaintResolution")
    @ApiOperation("接诉即办数据")
    public ResponseData<EventComplaintResolution> getComplaintResolution() {
        if (szlsModuleService.isReported("接诉即办", SzlsModuleEnum.JSJB.getId())) {
            return ResponseData.ok(eventComplaintResolutionService.getReportedData());
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getEvent")
    @ApiOperation("事件数据")
    public ResponseData<EventEvent> getEvent() {
        if (szlsModuleService.isReported("事件", SzlsModuleEnum.JSJB.getId())) {
            return eventService.getEvent(LocalDateTime.now());
        }
        return ResponseData.ok(null);   
    }

    @GetMapping("getEventSource")
    @ApiOperation("事件来源数据")
    public ResponseData<EventEventSource> getEventSource() {
        if (szlsModuleService.isReported("事件来源", SzlsModuleEnum.JSJB.getId())) {
            return eventService.getEventSource(LocalDateTime.now());
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getCallOut")
    @ApiOperation("呼叫数据")
    public ResponseData<EventCallOut> getCallOut() {
        if (szlsModuleService.isReported("呼叫", SzlsModuleEnum.JSJB.getId())) {
            return eventService.getCallOut();
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getEventType")
    @ApiOperation("事件类型数据")
    public ResponseData<EventEventType> getEventType() {
        if (szlsModuleService.isReported("事件类型", SzlsModuleEnum.JSJB.getId())) {
            return eventService.getEventType(LocalDateTime.now());
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getFacilityAppealEvent")
    @ApiOperation("设施诉求类事件数据")
    public ResponseData<EventFacilityAppealEvent> getFacilityAppealEvent() {
        if (szlsModuleService.isReported("设施诉求类事件", SzlsModuleEnum.JSJB.getId())) {
            return eventService.getFacilityAppealEvent(LocalDateTime.now());
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getSubCenterUndertake")
    @ApiOperation("按办理类型各分中心承办量数据")
    public ResponseData<EventSubCenterUndertake> getSubCenterUndertake() {
        if (szlsModuleService.isReported("按办理类型各分中心承办量", SzlsModuleEnum.JSJB.getId())) {
            return eventService.getSubCenterUndertake(LocalDateTime.now());
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getBusinessOfficeUndertake")
    @ApiOperation("按办理类型业务部室承办量数据")
    public ResponseData<EventBusinessOfficeUndertake> getBusinessOfficeUndertake() {
        if (szlsModuleService.isReported("按业务类型业务部室承办量", SzlsModuleEnum.JSJB.getId())) {
            return eventService.getBusinessOfficeUndertake(LocalDateTime.now());
        }
        return ResponseData.ok(null);
    }

}
