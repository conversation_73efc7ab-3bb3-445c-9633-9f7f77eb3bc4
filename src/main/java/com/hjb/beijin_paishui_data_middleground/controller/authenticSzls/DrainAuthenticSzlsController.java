package com.hjb.beijin_paishui_data_middleground.controller.authenticSzls;

import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.constants.SzlsModuleEnum;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.drain.DrainOperationalRiskEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.drain.*;
import com.hjb.beijin_paishui_data_middleground.service.reception.DrainService;
import com.hjb.beijin_paishui_data_middleground.service.szls.SzlsModuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description: 排水设施相关接口
 * @Author: lvhongen
 * @Date: 2025-03-20 09:51
 * @Version： 1.0
 **/
@RestController
@Api(tags = "排水设施相关接口")
@RequestMapping("authenticSzls/drain")
@Validated
@Slf4j
public class DrainAuthenticSzlsController {
    @Resource
    private DrainService drainService;
    @Resource
    private SzlsModuleService szlsModuleService;

    @GetMapping("getPipeNetworkAndFacilities")
    @ApiOperation("管网及设施数据")
    public ResponseData<DrainPipeNetworkAndFacilities> getPipeNetworkAndFacilities () {
        if (szlsModuleService.isReported("管网及设施", SzlsModuleEnum.PSSS.getId())) {
            return drainService.getPipeNetworkAndFacilities();
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getAncillaryFacilities")
    @ApiOperation("附属设施数据")
    public ResponseData<DrainAncillaryFacilities> getAncillaryFacilities () {
        if (szlsModuleService.isReported("附属设施", SzlsModuleEnum.PSSS.getId())) {
            return drainService.getAncillaryFacilities();
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getSpecialFacility")
    @ApiOperation("特殊设施数据")
    public ResponseData<DrainSpecialFacility> getSpecialFacility () {
        if (szlsModuleService.isReported("特殊设施", SzlsModuleEnum.PSSS.getId())) {
            return drainService.getSpecialFacility();
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getMaintenanceProduction")
    @ApiOperation("养护生产数据")
    public ResponseData<DrainMaintenanceProduction> getMaintenanceProduction () {
        if (szlsModuleService.isReported("养护生产", SzlsModuleEnum.PSSS.getId())) {
            return drainService.getMaintenanceProduction(LocalDate.now());
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getHybridConnection")
    @ApiOperation("混接数据")
    public ResponseData<DrainHybridConnection> getHybridConnection () {
        if (szlsModuleService.isReported("混接", SzlsModuleEnum.PSSS.getId())) {
            return drainService.getHybridConnection();
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getPeripheralConstruction")
    @ApiOperation("周边施工数据")
    public ResponseData<DrainPeripheralConstruction> getPeripheralConstruction () {
        if (szlsModuleService.isReported("周边施工", SzlsModuleEnum.PSSS.getId())) {
            return drainService.getPeripheralConstruction();
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getOperationalRisk")
    @ApiOperation("运行风险数据")
    public ResponseData<DrainOperationalRiskEditDto> getOperationalRisk () {
        if (szlsModuleService.isReported("运行风险", SzlsModuleEnum.PSSS.getId())) {
            return drainService.getOperationalRisk(LocalDateTime.now());
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getPipeNetworkTesting")
    @ApiOperation("管网检测数据")
    public ResponseData<DrainPipeNetworkTesting> getPipeNetworkTesting () {
        if (szlsModuleService.isReported("管网检测", SzlsModuleEnum.PSSS.getId())) {
            return drainService.getPipeNetworkTesting();
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getPipeNetworkBlemishPie")
    @ApiOperation("管网缺陷饼图数据")
    public ResponseData<DrainPipeNetworkTesting> getPipeNetworkBlemishPie () {
        if (szlsModuleService.isReported("管网检测", SzlsModuleEnum.PSSS.getId())) {
            // 填报数据
            return ResponseData.ok(null);
        }
        // 真实数据
        return ResponseData.ok(drainService.getPipeNetworkBlemishPie_Real());
    }

    @GetMapping("getFacilityBlanking")
    @ApiOperation("设施消隐数据")
    public ResponseData<DrainFacilityBlanking> getFacilityBlanking () {
        if (szlsModuleService.isReported("设施消隐", SzlsModuleEnum.PSSS.getId())) {
            return drainService.getFacilityBlanking(LocalDateTime.now());
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getEmergencyRepair")
    @ApiOperation("应急事件数据")
    public ResponseData<DrainEmergencyRepair> getEmergencyRepair () {
        if (szlsModuleService.isReported("应急事件", SzlsModuleEnum.PSSS.getId())) {
            return drainService.getEmergencyRepair(LocalDateTime.now());
        }
        return ResponseData.ok(null);
    }
}
