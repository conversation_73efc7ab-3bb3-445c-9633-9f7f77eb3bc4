package com.hjb.beijin_paishui_data_middleground.controller.authenticSzls;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponsePage;
import com.hjb.beijin_paishui_data_middleground.entity.sub.dto.PlanPageRequest;
import com.hjb.beijin_paishui_data_middleground.entity.sub.vo.PlanPageResponse;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.SzlsPlanEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.SzlsPlan;
import com.hjb.beijin_paishui_data_middleground.service.reception.PlanService;
import com.hjb.beijin_paishui_data_middleground.service.szls.SzlsPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.Objects;

@RestController
@Api(tags = "方案相关接口")
@RequestMapping("authenticSzls/plan")
@Validated
@Slf4j
public class PlanAuthenticController {

    @Resource
    private PlanService planService;

    @Resource
    private SzlsPlanService szlsPlanService;

    @GetMapping("getToken")
    @ApiOperation("获取token")
    public ResponseData<String> getToken() {
        return ResponseData.ok(planService.getToken());
    }

    @PostMapping("selectPlanMsgList")
    @ApiOperation("获取数据方计划列表")
    public ResponseData<PlanPageResponse> selectPlanMsgList(@RequestBody PlanPageRequest planPageRequest) {
        return ResponseData.ok(planService.selectPlanMsgList(planPageRequest));
    }

    @PostMapping("page")
    @ApiOperation("获取推送过的计划列表")
    public ResponseData<ResponsePage<SzlsPlan>> page(@RequestBody PlanPageRequest planPageRequest) {
        Page<SzlsPlan> page = new Page<>();
        LambdaQueryWrapper<SzlsPlan> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Objects.nonNull(planPageRequest.getModelId()), SzlsPlan::getModelId, planPageRequest.getModelId())
                .like(StringUtils.isNoneBlank(planPageRequest.getPlanName()), SzlsPlan::getPlanName, planPageRequest.getPlanName())
                .ge(Objects.nonNull(planPageRequest.getStartTime()), SzlsPlan::getStartTime, planPageRequest.getStartTime())
                .le(Objects.nonNull(planPageRequest.getEndTime()), SzlsPlan::getEndTime, planPageRequest.getEndTime());
        szlsPlanService.page(page, wrapper);
        return ResponseData.ok(ResponsePage.transform(page));
    }

    @PostMapping("pushPlan")
    @ApiOperation("推送方案")
    public ResponseData<Boolean> pushPlan(@RequestBody SzlsPlanEditDto editDto) {
        SzlsPlan szlsPlan = szlsPlanService.getOne(new LambdaQueryWrapper<SzlsPlan>()
                .eq(SzlsPlan::getPlanId, editDto.getPlanId()));

        boolean success;
        if (szlsPlan == null) {
            szlsPlan = new SzlsPlan();
            BeanUtils.copyProperties(editDto, szlsPlan);
            success = szlsPlanService.save(szlsPlan);
        } else {
            BeanUtils.copyProperties(editDto, szlsPlan);
            szlsPlan.setDataState(0);
            success = szlsPlanService.updateById(szlsPlan);
        }

        if (success) {
            String planId = editDto.getPlanId();
            LocalDateTime startTime = toLocalDateTime(editDto.getStartTime());
            LocalDateTime endTime = toLocalDateTime(editDto.getEndTime());
            int stepMinutes = editDto.getOutputStep();
            szlsPlanService.processWaterDepthTask(planId, startTime, endTime, stepMinutes);
        }

        return success ? ResponseData.ok(success) : ResponseData.error("推送失败");
    }

    // 工具方法
    private LocalDateTime toLocalDateTime(Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

}
