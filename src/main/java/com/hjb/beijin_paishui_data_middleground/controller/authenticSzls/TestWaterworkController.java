package com.hjb.beijin_paishui_data_middleground.controller.authenticSzls;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Api(tags = "水厂测试接口")
@RequestMapping("authenticSzls/waterwork")
@Validated
@Slf4j
public class TestWaterworkController {

    public static final String WATER_URL = "http://10.2.110.37:18086/api/query_rt";

    @PostMapping("test")
    @ApiOperation("测试最新数据接口")
    public ResponseData<Object> buildIndicatorSection(@RequestBody List<String> indicatorList) {
        // 请求接口
        String bodyJson = JSONUtil.toJsonStr(indicatorList);

        HttpResponse response = HttpRequest.post(WATER_URL)
                .body(bodyJson)
                .execute();
        

        // 打印响应体
        String responseBody = response.body();
        System.out.println("接口响应内容：" + responseBody);

        JSONObject respJson = JSONUtil.parseObj(responseBody);

        return ResponseData.ok(respJson);
    }

    public static final String WATER_URL_2 = "http://10.2.110.37:18086/api/query_hs";
    @PostMapping("test2")
    @ApiOperation("测试历史数据接口")
    public ResponseData<Object> buildIndicatorSection(@RequestBody HisDto hisDto) {
        // 请求接口
        String bodyJson = JSONUtil.toJsonStr(hisDto);

        HttpResponse response = HttpRequest.post(WATER_URL_2)
                .body(bodyJson)
                .execute();

        // 打印响应体
        String responseBody = response.body();
        System.out.println("接口响应内容：" + responseBody);

        JSONObject respJson = JSONUtil.parseObj(responseBody);

        return ResponseData.ok(respJson);
    }

    @Data
    public static class HisDto {
        private String from;
        private String to;
        private List<String> id;
        private String sampling;
    }

}
