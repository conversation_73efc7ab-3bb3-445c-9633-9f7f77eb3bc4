package com.hjb.beijin_paishui_data_middleground.controller.authenticSzls;
import com.hjb.beijin_paishui_data_middleground.common.response.ResponseData;
import com.hjb.beijin_paishui_data_middleground.constants.SzlsModuleEnum;
import com.hjb.beijin_paishui_data_middleground.entity.interactive.vo.SyntheticaSelectData;
import com.hjb.beijin_paishui_data_middleground.entity.szls.dto.flood.FloodPipeNetworkMonitorEditDto;
import com.hjb.beijin_paishui_data_middleground.entity.szls.po.flood.*;
import com.hjb.beijin_paishui_data_middleground.service.reception.FloodService;
import com.hjb.beijin_paishui_data_middleground.service.szls.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/15 上午10:08
 * @description 综合态势相关接口
 */
@RestController
@Api(tags = "防汛调度相关接口")
@RequestMapping("authenticSzls/flood")
@Validated
@Slf4j
public class FloodAuthenticController {
    @Resource
    private FloodService floodService;
    @Resource
    private SzlsModuleService szlsModuleService;

    @GetMapping("getRealTimeRainfall")
    @ApiOperation("实时雨量数据")
    public ResponseData<FloodRealTimeRainfall> getRealTimeRainfall () {
        if (szlsModuleService.isReported("实时雨量", SzlsModuleEnum.FXDD.getId())) {
            return floodService.getRealTimeRainfall();
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getRainwaterStorage")
    @ApiOperation("泵站排蓄数据")
    public ResponseData<FloodRainwaterStorage> getRainwaterStorage (String company) {
        if (szlsModuleService.isReported("泵站排蓄", SzlsModuleEnum.FXDD.getId())) {
            return null;
        }
        return ResponseData.ok(null);
    }

    @GetMapping("getWaterPlant")
    @ApiOperation("水厂数据")
    public ResponseData<FloodWaterPlant> getWaterPlant () {
        if (szlsModuleService.isReported("水厂", SzlsModuleEnum.FXDD.getId())) {
            return floodService.getWaterPlant();
        }
        return ResponseData.ok(null);
    }
    @GetMapping("getPipeNetworkMonitorQueryList")
    @ApiOperation("管网检测分类列表")
    public ResponseData<List<SyntheticaSelectData.SelectData>> getPipeNetworkMonitorQueryList () {
        if (szlsModuleService.isReported("管网检测", SzlsModuleEnum.FXDD.getId())) {
            List<SyntheticaSelectData.SelectData> selectData = new ArrayList<>();
            selectData.add(new SyntheticaSelectData.SelectData("厂前溢流","cqyl"));
            selectData.add(new SyntheticaSelectData.SelectData("排河口","phk"));
            selectData.add(new SyntheticaSelectData.SelectData("污水管道","wsgd"));
            selectData.add(new SyntheticaSelectData.SelectData("雨水管道","ysgd"));
            return ResponseData.ok(selectData);
        }
        return ResponseData.ok(null);
    }
    @GetMapping("getPipeNetworkMonitor")
    @ApiOperation("管网检测数据")
    public ResponseData<FloodPipeNetworkMonitorEditDto> getPipeNetworkMonitor (String type) {
        if (szlsModuleService.isReported("管网检测", SzlsModuleEnum.FXDD.getId())) {
            return floodService.getPipeNetworkMonitor(type);
        }
        return ResponseData.ok(null);
    }
//    @GetMapping("getUpdateStation")
//    @ApiOperation("更新测站数据")
//    public ResponseData<List<StationInfo>> getUpdateStation () {
//            return floodService.getUpdateStation();
//    }
//    @GetMapping("getTest")
//    @ApiOperation("获取对应关系")
//    public ResponseData geTTest () {
//        List<StationTypeData> list = stationTypeDataService.list();
//        List<Map<String,Object>> allLost = new ArrayList<>();
//        for (StationTypeData stationTypeData : list) {
//            Map<String,Object> map = new HashMap<>();
//            map.put("分组",stationTypeData.getGroupName());
//            map.put("测站编码",stationTypeData.getCode());
//            map.put("测站名称",stationTypeData.getName());
//            map.put("测站类别",stationTypeData.getType());
//            List<GwPipe> list1 = gwPipeService.list(new LambdaQueryWrapper<GwPipe>().eq(GwPipe::getXyjdm, stationTypeData.getCode()));
//            map.put("对应管线数量",list1.size());
//            if(list1.size() == 0){
//                allLost.add(map);
//            }
//            if(list1.size() > 1){
//                List<GwPipe> list2 = gwPipeService.list(new LambdaQueryWrapper<GwPipe>().eq(GwPipe::getSyjdm, stationTypeData.getCode()));
//                if(list2.size() > 1){
//                    List<String> codeList = new ArrayList<>();
//                    for (GwPipe gwPipe : list2) {
//                        codeList.add(gwPipe.getCode());
//                    }
//                    String result = String.join(",", codeList);
//                    map.put("对应的上游管线编码",result);
//                    allLost.add(map);
//                }
//            }
//        }
//        writeToTxt(allLost, "\\usr\\local\\szls\\output.txt");
//        return ResponseData.ok(allLost);
//    }
//    private void writeToTxt(List<Map<String, Object>> data, String filePath) {
//        try (FileWriter writer = new FileWriter(filePath)) {
//            for (Map<String, Object> map : data) {
//                for (Map.Entry<String, Object> entry : map.entrySet()) {
//                    writer.write(entry.getKey() + ": " + entry.getValue() + "\t");
//                }
//                writer.write("\n");
//            }
//            System.out.println("数据已成功写入到 " + filePath);
//        } catch (IOException e) {
//            System.err.println("写入文件时出错: " + e.getMessage());
//        }
//    }

//    @PostMapping("uploadStation")
//    @ApiOperation("存储物联网测站站点清单")
//    public ResponseData uploadStation (@RequestPart("file") MultipartFile file) throws SQLException, IOException {
//        boolean excel2007 = ExcelUtils.isExcel2007(file.getOriginalFilename());
//        if (!excel2007)
//            return ResponseData.error("请出入标准的execl格式的文件");
//        floodService.uploadStation(file);
//        return ResponseData.ok(null);
//    }
    @GetMapping("getRecessedBridgeQueryList")
    @ApiOperation("查询下凹桥查询列表列表")
    public ResponseData getRecessedBridgeQueryList(){
        return floodService.getRecessedBridgeQueryList();
    }
    @GetMapping("getRecessedBridge")
    @ApiOperation("下凹桥数据")
    public ResponseData<FloodRecessedBridge> getRecessedBridge (String searchAdministrative, String searchCompany) {
        if (szlsModuleService.isReported("下凹桥", SzlsModuleEnum.FXDD.getId())) {
            return floodService.getRecessedBridge(searchAdministrative, searchCompany);
        }
        return ResponseData.ok(null);
    }
    @GetMapping("getRiskPointQueryList")
    @ApiOperation("查询风险点查询列表列表")
    public ResponseData getRiskPointQueryList(){
        return floodService.getRiskPointQueryList();
    }
    @GetMapping("getRiskPoint")
    @ApiOperation("风险点数据")
    public ResponseData<FloodRiskPoint> getRiskPoint (String searchAdministrative, String searchCompany) {
        if (szlsModuleService.isReported("风险点", SzlsModuleEnum.FXDD.getId())) {
            return floodService.getRiskPoint(searchAdministrative, searchCompany);
        }
        return ResponseData.ok(null);
    }
    @GetMapping("getEmergencyUnitQueryList")
    @ApiOperation("查询抢险单元查询列表")
    public ResponseData getEmergencyUnitQueryList(){
        return floodService.getEmergencyUnitQueryList();

    }
    @GetMapping("getEmergencyUnit")
    @ApiOperation("抢险单元数据")
    public ResponseData<FloodEmergencyUnit> getEmergencyUnit (String searchType, String searchCompany, String searchUnit) {
        if (szlsModuleService.isReported("抢险单元", SzlsModuleEnum.FXDD.getId())) {
            return floodService.getEmergencyUnit(searchType, searchCompany, searchUnit);
        }
        return ResponseData.ok(null);
    }

}
