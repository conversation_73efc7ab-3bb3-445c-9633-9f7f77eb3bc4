# 推送方案接口文档

## 接口概述

**接口名称：** 推送方案  
**接口描述：** 用于推送淹没方案数据到数字孪生系统，支持新增和更新方案，并异步处理水深数据  
**接口路径：** `POST http://10.15.100.122:8808/api/authenticSzls/plan/pushPlan`  
**接口标签：** 方案相关接口  
**开发者：** 北京排水集团数字孪生数据中台  

---

## 请求信息

### 请求方式
```http
POST /api/authenticSzls/plan/pushPlan
```

### 请求头
```http
Content-Type: application/json
```

### 请求参数

#### 请求体 (SzlsPlanEditDto)

| 参数名 | 类型 | 必填 | 描述 | 示例值                   |
|--------|------|------|------|-----------------------|
| planId | String | 是 | 计划唯一标识ID | "PLAN_20250101_001"   |
| modelId | Integer | 是 | 模型类型<br/>• 1: 概化模型<br/>• 2: 细化模型 | 1                     |
| planName | String | 是 | 方案名称 | "北京市暴雨淹没方案_2025"      |
| startTime | Date | 是 | 计算开始时间<br/>格式: yyyy-MM-dd HH:mm:ss | "2025-01-01 08:00:00" |
| endTime | Date | 是 | 计算结束时间<br/>格式: yyyy-MM-dd HH:mm:ss | "2025-01-01 20:00:00" |
| outputStep | Integer | 是 | 输出步长（分钟） | 5                     |
| gmtCreate | Date | 否 | 方案创建时间<br/>格式: yyyy-MM-dd HH:mm:ss | "2025-01-01 07:30:00" |

### 请求示例

```json
{
  "planId": "PLAN_20250101_001",
  "modelId": 1,
  "planName": "北京市暴雨淹没方案_2025",
  "startTime": "2025-01-01 08:00:00",
  "endTime": "2025-01-01 20:00:00",
  "outputStep": 30,
  "gmtCreate": "2025-01-01 07:30:00"
}
```

---

## 响应信息

### 响应格式 (ResponseData<Boolean>)

| 参数名 | 类型 | 描述                                      |
|--------|------|-----------------------------------------|
| code | Integer | 响应状态码<br/>• 0: 成功<br/>• 其他: 失败          |
| message | String | 响应消息                                    |
| data | Boolean | 推送结果<br/>• true: 推送成功<br/>• false: 推送失败 |

### 响应示例

#### ✅ 成功响应
```json
{
  "code": 0,
  "message": "操作成功",
  "data": true
}
```

#### ❌ 失败响应
```json
{
  "code": 1,
  "message": "推送失败，请检查参数",
  "data": false
}
```

---

## 状态码说明

| 状态码 | 状态 | 说明   | 描述 |
|-----|------|------|------|
| 0   | SUCCESS | 操作成功 | 方案推送成功 |
| -2  | PARAM_ERR | 参数错误 | 请求参数格式或内容错误 |
| -1  | FAILURE | 操作失败 | 操作失败 |

---

## 注意事项

### ⚠️ 重要提醒

1. **时间格式**: 所有时间字段必须使用 `yyyy-MM-dd HH:mm:ss` 格式，时区为 GMT+8
4. **重复推送**: 相同 `planId` 的方案可以重复推送，会更新现有记录并重新处理数据
5. **模型类型**: `modelId` 只能为 1（概化模型）或 2（细化模型）
